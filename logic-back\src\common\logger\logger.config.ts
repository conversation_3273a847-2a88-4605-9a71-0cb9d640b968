import * as winston from 'winston';
import * as DailyRotateFile from 'winston-daily-rotate-file';
import { WinstonModuleOptions } from 'nest-winston';
import * as path from 'path';
import * as fs from 'fs';

// 确保logs目录存在
const logsDir = path.join(process.cwd(), 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// 自定义日志格式
const logFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss.SSS'
  }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf(({ timestamp, level, message, context, trace, ...meta }) => {
    let logMessage = `${timestamp} [${level.toUpperCase()}]`;
    
    if (context) {
      logMessage += ` [${context}]`;
    }
    
    logMessage += ` ${message}`;
    
    // 添加额外的元数据
    if (Object.keys(meta).length > 0) {
      logMessage += ` ${JSON.stringify(meta)}`;
    }
    
    // 添加错误堆栈
    if (trace) {
      logMessage += `\n${trace}`;
    }
    
    return logMessage;
  })
);

// 控制台日志格式（更简洁）
const consoleFormat = winston.format.combine(
  winston.format.colorize(),
  winston.format.timestamp({
    format: 'HH:mm:ss.SSS'
  }),
  winston.format.printf(({ timestamp, level, message, context }) => {
    let logMessage = `${timestamp} ${level}`;
    
    if (context) {
      logMessage += ` [${context}]`;
    }
    
    logMessage += ` ${message}`;
    
    return logMessage;
  })
);

export function createLoggerConfig(): WinstonModuleOptions {
  const isDevelopment = process.env.NODE_ENV === 'dev' || process.env.NODE_ENV === 'development';

  const transports: winston.transport[] = [];

  // 控制台输出（开发环境）
  if (isDevelopment) {
    transports.push(
      new winston.transports.Console({
        format: consoleFormat,
        level: 'debug'
      })
    );
  }

  // 所有日志文件（按日期轮转）
  transports.push(
    new DailyRotateFile({
      filename: path.join(logsDir, 'application-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      zippedArchive: true,
      maxSize: '20m',
      maxFiles: '30d',
      format: logFormat,
      level: 'debug'
    })
  );

  // 错误日志文件（按日期轮转）
  transports.push(
    new DailyRotateFile({
      filename: path.join(logsDir, 'error-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      zippedArchive: true,
      maxSize: '20m',
      maxFiles: '30d',
      format: logFormat,
      level: 'error'
    })
  );

  // HTTP请求日志文件（按日期轮转）
  transports.push(
    new DailyRotateFile({
      filename: path.join(logsDir, 'http-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      zippedArchive: true,
      maxSize: '20m',
      maxFiles: '30d',
      format: logFormat,
      level: 'info'
    })
  );

  // 数据库日志文件（按日期轮转）
  transports.push(
    new DailyRotateFile({
      filename: path.join(logsDir, 'database-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      zippedArchive: true,
      maxSize: '20m',
      maxFiles: '30d',
      format: logFormat,
      level: 'debug'
    })
  );

  // Console日志文件（专门用于捕获console.log等输出）
  transports.push(
    new DailyRotateFile({
      filename: path.join(logsDir, 'console-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      zippedArchive: true,
      maxSize: '20m',
      maxFiles: '30d',
      format: logFormat,
      level: 'debug'
    })
  );

  return {
    transports,
    format: logFormat,
    defaultMeta: {
      service: 'logic-back',
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      pid: process.pid,
      hostname: require('os').hostname()
    },
    exitOnError: false,
    // 异常处理
    exceptionHandlers: [
      new DailyRotateFile({
        filename: path.join(logsDir, 'exceptions-%DATE%.log'),
        datePattern: 'YYYY-MM-DD',
        zippedArchive: true,
        maxSize: '20m',
        maxFiles: '30d',
        format: logFormat
      })
    ],
    // 未捕获的Promise拒绝处理
    rejectionHandlers: [
      new DailyRotateFile({
        filename: path.join(logsDir, 'rejections-%DATE%.log'),
        datePattern: 'YYYY-MM-DD',
        zippedArchive: true,
        maxSize: '20m',
        maxFiles: '30d',
        format: logFormat
      })
    ]
  };
}

/**
 * 重写console方法，将所有console输出重定向到Winston日志系统
 */
export function overrideConsole(logger: winston.Logger) {
  // 保存原始的console方法
  const originalConsole = {
    log: console.log,
    error: console.error,
    warn: console.warn,
    info: console.info,
    debug: console.debug,
    trace: console.trace,
    assert: console.assert,
    dir: console.dir,
    dirxml: console.dirxml,
    group: console.group,
    groupCollapsed: console.groupCollapsed,
    groupEnd: console.groupEnd,
    table: console.table,
    time: console.time,
    timeEnd: console.timeEnd,
    count: console.count,
    clear: console.clear
  };

  // 重写console.log
  console.log = (...args: any[]) => {
    const message = args.map(arg =>
      typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
    ).join(' ');
    logger.info(message, { context: 'Console' });
    // 在开发环境下仍然输出到原始控制台
    if (process.env.NODE_ENV === 'dev' || process.env.NODE_ENV === 'development') {
      originalConsole.log(...args);
    }
  };

  // 重写console.error
  console.error = (...args: any[]) => {
    const message = args.map(arg =>
      typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
    ).join(' ');
    logger.error(message, { context: 'Console' });
    if (process.env.NODE_ENV === 'dev' || process.env.NODE_ENV === 'development') {
      originalConsole.error(...args);
    }
  };

  // 重写console.warn
  console.warn = (...args: any[]) => {
    const message = args.map(arg =>
      typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
    ).join(' ');
    logger.warn(message, { context: 'Console' });
    if (process.env.NODE_ENV === 'dev' || process.env.NODE_ENV === 'development') {
      originalConsole.warn(...args);
    }
  };

  // 重写console.info
  console.info = (...args: any[]) => {
    const message = args.map(arg =>
      typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
    ).join(' ');
    logger.info(message, { context: 'Console' });
    if (process.env.NODE_ENV === 'dev' || process.env.NODE_ENV === 'development') {
      originalConsole.info(...args);
    }
  };

  // 重写console.debug
  console.debug = (...args: any[]) => {
    const message = args.map(arg =>
      typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
    ).join(' ');
    logger.debug(message, { context: 'Console' });
    if (process.env.NODE_ENV === 'dev' || process.env.NODE_ENV === 'development') {
      originalConsole.debug(...args);
    }
  };

  // 重写console.trace
  console.trace = (...args: any[]) => {
    const message = args.map(arg =>
      typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
    ).join(' ');
    const stack = new Error().stack;
    logger.debug(message, { context: 'Console', trace: stack });
    if (process.env.NODE_ENV === 'dev' || process.env.NODE_ENV === 'development') {
      originalConsole.trace(...args);
    }
  };

  // 重写console.assert
  console.assert = (condition: boolean, ...args: any[]) => {
    if (!condition) {
      const message = args.map(arg =>
        typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
      ).join(' ');
      logger.error(`Assertion failed: ${message}`, { context: 'Console' });
    }
    if (process.env.NODE_ENV === 'dev' || process.env.NODE_ENV === 'development') {
      originalConsole.assert(condition, ...args);
    }
  };

  // 重写console.dir
  console.dir = (obj: any, options?: any) => {
    const message = JSON.stringify(obj, null, 2);
    logger.info(`Dir: ${message}`, { context: 'Console' });
    if (process.env.NODE_ENV === 'dev' || process.env.NODE_ENV === 'development') {
      originalConsole.dir(obj, options);
    }
  };

  // 重写console.table
  console.table = (data: any) => {
    const message = JSON.stringify(data, null, 2);
    logger.info(`Table: ${message}`, { context: 'Console' });
    if (process.env.NODE_ENV === 'dev' || process.env.NODE_ENV === 'development') {
      originalConsole.table(data);
    }
  };

  // 其他console方法保持原样，但添加日志记录
  console.group = (...args: any[]) => {
    const message = args.join(' ');
    logger.info(`Group: ${message}`, { context: 'Console' });
    if (process.env.NODE_ENV === 'dev' || process.env.NODE_ENV === 'development') {
      originalConsole.group(...args);
    }
  };

  console.groupCollapsed = (...args: any[]) => {
    const message = args.join(' ');
    logger.info(`GroupCollapsed: ${message}`, { context: 'Console' });
    if (process.env.NODE_ENV === 'dev' || process.env.NODE_ENV === 'development') {
      originalConsole.groupCollapsed(...args);
    }
  };

  console.groupEnd = () => {
    logger.info('GroupEnd', { context: 'Console' });
    if (process.env.NODE_ENV === 'dev' || process.env.NODE_ENV === 'development') {
      originalConsole.groupEnd();
    }
  };

  // 时间相关的方法需要特殊处理
  const timers = new Map<string, number>();

  console.time = (label: string = 'default') => {
    timers.set(label, Date.now());
    logger.info(`Timer started: ${label}`, { context: 'Console' });
    if (process.env.NODE_ENV === 'dev' || process.env.NODE_ENV === 'development') {
      originalConsole.time(label);
    }
  };

  console.timeEnd = (label: string = 'default') => {
    const startTime = timers.get(label);
    if (startTime) {
      const duration = Date.now() - startTime;
      timers.delete(label);
      logger.info(`Timer ended: ${label} - ${duration}ms`, { context: 'Console' });
    }
    if (process.env.NODE_ENV === 'dev' || process.env.NODE_ENV === 'development') {
      originalConsole.timeEnd(label);
    }
  };

  const counters = new Map<string, number>();

  console.count = (label: string = 'default') => {
    const count = (counters.get(label) || 0) + 1;
    counters.set(label, count);
    logger.info(`Count: ${label} - ${count}`, { context: 'Console' });
    if (process.env.NODE_ENV === 'dev' || process.env.NODE_ENV === 'development') {
      originalConsole.count(label);
    }
  };

  console.clear = () => {
    logger.info('Console cleared', { context: 'Console' });
    if (process.env.NODE_ENV === 'dev' || process.env.NODE_ENV === 'development') {
      originalConsole.clear();
    }
  };

  // 返回原始console方法，以便在需要时恢复
  return originalConsole;
}
