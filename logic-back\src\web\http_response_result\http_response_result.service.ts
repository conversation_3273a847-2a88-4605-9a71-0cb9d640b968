import { Injectable, Optional } from '@nestjs/common';
import { HttpResponse, SUCCESS_CODE, ERROR_CODE } from './http-response.interface';
import { LoggerService } from '../../common/logger/logger.service';

/**
 * 日志选项接口
 */
export interface LogOptions {
  /** 请求路径 */
  path?: string;
  /** 用户ID */
  userId?: number;
  /** 请求ID */
  requestId?: string;
  /** 执行时间 */
  executionTime?: number;
  /** 日志上下文 */
  context?: string;
  /** 是否启用日志 */
  enableLog?: boolean;
}

@Injectable()
export class HttpResponseResultService {
  constructor(
    @Optional() private readonly loggerService?: LoggerService
  ) {}
  /**
   * 成功响应
   * @param data 响应数据
   * @param msg 响应消息
   * @param code 状态码
   * @param logOptions 日志选项
   */
  success<T = any>(
    data?: T,
    msg = '操作成功',
    code = SUCCESS_CODE,
    logOptions?: LogOptions
  ): HttpResponse<T> {
    const response = {
      code,
      msg,
      data: data as T,
    };

    // 记录成功日志
    this.logSuccess(msg, data, code, logOptions);

    return response;
  }

  /**
   * 错误响应
   * @param msg 错误消息
   * @param data 错误数据
   * @param code 状态码
   * @param logOptions 日志选项
   */
  error<T = any>(
    msg = '系统错误',
    data?: T,
    code = ERROR_CODE,
    logOptions?: LogOptions
  ): HttpResponse<T> {
    const response = {
      code,
      msg,
      data: data as T,
    };

    // 记录错误日志
    this.logError(msg, data, code, logOptions);

    return response;
  }

  /**
   * 自定义响应
   * @param code 状态码
   * @param msg 响应消息
   * @param data 响应数据
   * @param logOptions 日志选项
   */
  custom<T = any>(
    code: number,
    msg: string,
    data?: T,
    logOptions?: LogOptions
  ): HttpResponse<T> {
    const response = {
      code,
      msg,
      data: data as T,
    };

    // 记录自定义响应日志
    if (code >= 200 && code < 300) {
      this.logSuccess(msg, data, code, logOptions);
    } else {
      this.logError(msg, data, code, logOptions);
    }

    return response;
  }

  // ==================== 私有日志方法 ====================

  /**
   * 记录成功日志
   * @param msg 消息
   * @param data 数据
   * @param code 状态码
   * @param logOptions 日志选项
   */
  private logSuccess<T>(
    msg: string,
    data: T,
    code: number,
    logOptions?: LogOptions
  ): void {
    if (!this.shouldLog(logOptions)) return;

    const context = logOptions?.context || 'HttpResponseResultService';
    const logMessage = this.buildLogMessage('SUCCESS', msg, code, logOptions);

    this.loggerService?.log(logMessage, context);

    // 记录详细信息（调试级别）
    if (logOptions?.enableLog) {
      this.loggerService?.debug('Success Details', JSON.stringify({
        message: msg,
        code,
        data,
        path: logOptions?.path,
        userId: logOptions?.userId,
        requestId: logOptions?.requestId,
        executionTime: logOptions?.executionTime,
        timestamp: new Date().toISOString()
      }), context);
    }
  }

  /**
   * 记录错误日志
   * @param msg 消息
   * @param data 数据
   * @param code 状态码
   * @param logOptions 日志选项
   */
  private logError<T>(
    msg: string,
    data: T,
    code: number,
    logOptions?: LogOptions
  ): void {
    if (!this.shouldLog(logOptions)) return;

    const context = logOptions?.context || 'HttpResponseResultService';
    const logMessage = this.buildLogMessage('ERROR', msg, code, logOptions);

    this.loggerService?.error(logMessage, undefined, context);

    // 记录详细错误信息
    this.loggerService?.error('Error Details', JSON.stringify({
      message: msg,
      code,
      data,
      path: logOptions?.path,
      userId: logOptions?.userId,
      requestId: logOptions?.requestId,
      executionTime: logOptions?.executionTime,
      timestamp: new Date().toISOString()
    }), context);
  }

  /**
   * 构建日志消息
   * @param level 日志级别
   * @param msg 消息
   * @param code 状态码
   * @param logOptions 日志选项
   */
  private buildLogMessage(
    level: 'SUCCESS' | 'ERROR',
    msg: string,
    code: number,
    logOptions?: LogOptions
  ): string {
    const parts = [
      `[${level}]`,
      msg,
      `Code: ${code}`,
      logOptions?.path ? `Path: ${logOptions.path}` : '',
      logOptions?.userId ? `User: ${logOptions.userId}` : '',
      logOptions?.executionTime ? `Time: ${logOptions.executionTime}ms` : '',
      logOptions?.requestId ? `RequestId: ${logOptions.requestId}` : ''
    ].filter(Boolean);

    return parts.join(' - ');
  }

  /**
   * 判断是否应该记录日志
   * @param logOptions 日志选项
   */
  private shouldLog(logOptions?: LogOptions): boolean {
    // 如果没有日志服务，不记录
    if (!this.loggerService) return false;

    // 如果明确禁用日志，不记录
    if (logOptions?.enableLog === false) return false;

    // 默认记录日志
    return true;
  }
}
