"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/test-ip/page",{

/***/ "(app-pages-browser)/./app/test-ip/page.tsx":
/*!******************************!*\
  !*** ./app/test-ip/page.tsx ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ TestIPPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Divider,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/typography/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Divider,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/space/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Divider,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/card/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Divider,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Divider,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/divider/index.js\");\n/* harmony import */ var _lib_request__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/request */ \"(app-pages-browser)/./lib/request.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst { Title, Text, Paragraph } = _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_3__[\"default\"];\nfunction TestIPPage() {\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [result, setResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [realPublicIP, setRealPublicIP] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 获取真实的公网IP地址\n    const getRealPublicIP = async ()=>{\n        setLoading(true);\n        try {\n            console.log(\"\\uD83C\\uDF0D [Frontend] 开始获取真实公网IP...\");\n            // 使用多个IP查询服务，提高成功率\n            const ipServices = [\n                \"https://api.ipify.org?format=json\",\n                \"https://ipapi.co/json/\",\n                \"https://httpbin.org/ip\",\n                \"https://api.ip.sb/ip\",\n                \"https://ifconfig.me/ip\",\n                \"https://icanhazip.com\"\n            ];\n            for (const service of ipServices){\n                try {\n                    console.log(\"\\uD83D\\uDD0D 尝试获取公网IP: \".concat(service));\n                    const response = await fetch(service, {\n                        method: \"GET\",\n                        timeout: 5000\n                    });\n                    if (!response.ok) continue;\n                    let data;\n                    const contentType = response.headers.get(\"content-type\");\n                    if (contentType && contentType.includes(\"application/json\")) {\n                        data = await response.json();\n                        const ip = data.ip || data.origin || data.query;\n                        if (ip && isValidIPAddress(ip)) {\n                            console.log(\"✅ 成功获取公网IP: \".concat(ip, \" (来源: \").concat(service, \")\"));\n                            setRealPublicIP(ip);\n                            setResult({\n                                type: \"real-public-ip\",\n                                ip: ip,\n                                source: service,\n                                timestamp: new Date().toISOString()\n                            });\n                            return;\n                        }\n                    } else {\n                        const text = await response.text();\n                        const ip = text.trim();\n                        if (isValidIPAddress(ip)) {\n                            console.log(\"✅ 成功获取公网IP: \".concat(ip, \" (来源: \").concat(service, \")\"));\n                            setRealPublicIP(ip);\n                            setResult({\n                                type: \"real-public-ip\",\n                                ip: ip,\n                                source: service,\n                                timestamp: new Date().toISOString()\n                            });\n                            return;\n                        }\n                    }\n                } catch (error) {\n                    console.log(\"❌ 获取公网IP失败: \".concat(service, \" - \").concat(error));\n                    continue;\n                }\n            }\n            throw new Error(\"所有公网IP服务都无法访问\");\n        } catch (error) {\n            console.error(\"❌ [Frontend] 获取真实公网IP失败:\", error);\n            setResult({\n                type: \"error\",\n                error: error.message,\n                timestamp: new Date().toISOString()\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 验证IP地址格式\n    const isValidIPAddress = (ip)=>{\n        const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;\n        return ipv4Regex.test(ip);\n    };\n    // 测试获取当前IP位置\n    const testCurrentIP = async ()=>{\n        setLoading(true);\n        try {\n            console.log(\"\\uD83E\\uDDEA [Frontend] 开始测试当前IP获取...\");\n            const response = await _lib_request__WEBPACK_IMPORTED_MODULE_2__[\"default\"].get(\"/api/v1/ip-location/current\");\n            console.log(\"\\uD83D\\uDCE5 [Frontend] 收到响应:\", response);\n            setResult({\n                type: \"current-ip\",\n                data: response.data,\n                timestamp: new Date().toISOString()\n            });\n        } catch (error) {\n            var _error_response;\n            console.error(\"❌ [Frontend] 请求失败:\", error);\n            setResult({\n                type: \"error\",\n                error: error.message,\n                response: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data,\n                timestamp: new Date().toISOString()\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 测试查询指定IP\n    const testQueryIP = async ()=>{\n        setLoading(true);\n        try {\n            console.log(\"\\uD83E\\uDDEA [Frontend] 开始测试IP查询...\");\n            const testIP = \"*******\"; // Google DNS\n            const response = await _lib_request__WEBPACK_IMPORTED_MODULE_2__[\"default\"].get(\"/api/v1/ip-location/query?ip=\".concat(testIP, \"&includeRisk=false\"));\n            console.log(\"\\uD83D\\uDCE5 [Frontend] 收到响应:\", response);\n            setResult({\n                type: \"query-ip\",\n                testIP,\n                data: response.data,\n                timestamp: new Date().toISOString()\n            });\n        } catch (error) {\n            var _error_response;\n            console.error(\"❌ [Frontend] 请求失败:\", error);\n            setResult({\n                type: \"error\",\n                error: error.message,\n                response: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data,\n                timestamp: new Date().toISOString()\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 测试登录接口（观察IP日志）\n    const testLoginIP = async ()=>{\n        setLoading(true);\n        try {\n            console.log(\"\\uD83E\\uDDEA [Frontend] 开始测试登录IP获取...\");\n            // 这里故意使用错误的登录信息，只是为了触发IP获取逻辑\n            const response = await _lib_request__WEBPACK_IMPORTED_MODULE_2__[\"default\"].post(\"/api/user-auth/password\", {\n                phone: \"12345678910\",\n                password: \"123456\"\n            });\n            console.log(\"\\uD83D\\uDCE5 [Frontend] 登录响应:\", response);\n            setResult({\n                type: \"login-test\",\n                data: response.data,\n                timestamp: new Date().toISOString()\n            });\n        } catch (error) {\n            var _error_response;\n            console.error(\"\\uD83D\\uDCDD [Frontend] 登录测试完成 (预期失败):\", error);\n            setResult({\n                type: \"login-test\",\n                error: error.message,\n                response: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data,\n                note: \"这是预期的失败，主要用于观察IP获取日志\",\n                timestamp: new Date().toISOString()\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"24px\",\n            maxWidth: \"1200px\",\n            margin: \"0 auto\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                level: 2,\n                children: \"\\uD83E\\uDDEA IP地址获取测试页面\"\n            }, void 0, false, {\n                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                lineNumber: 191,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                children: \"这个页面用于测试前端到后端的IP地址传递和获取功能。 请打开浏览器开发者工具的控制台，以及后端服务器的日志，观察IP获取过程。\"\n            }, void 0, false, {\n                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                lineNumber: 193,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                direction: \"vertical\",\n                size: \"large\",\n                style: {\n                    width: \"100%\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83C\\uDFAF 测试功能\",\n                        size: \"small\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            wrap: true,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    type: \"primary\",\n                                    loading: loading,\n                                    onClick: testCurrentIP,\n                                    children: \"测试获取当前IP位置\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    loading: loading,\n                                    onClick: testQueryIP,\n                                    children: \"测试查询指定IP (*******)\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    loading: loading,\n                                    onClick: testLoginIP,\n                                    danger: true,\n                                    children: \"测试登录IP获取 (会失败)\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 9\n                    }, this),\n                    result && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83D\\uDCCA 测试结果\",\n                        size: \"small\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                background: \"#f5f5f5\",\n                                padding: \"16px\",\n                                borderRadius: \"6px\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                style: {\n                                    margin: 0,\n                                    fontSize: \"12px\",\n                                    lineHeight: \"1.4\"\n                                },\n                                children: JSON.stringify(result, null, 2)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83D\\uDCCB 观察要点\",\n                        size: \"small\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            direction: \"vertical\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            children: \"\\uD83C\\uDF10 前端中间件日志：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"查看浏览器控制台，观察 [Middleware] 标记的日志\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            children: \"\\uD83D\\uDDA5️ 后端IP提取日志：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"查看后端控制台，观察 [Backend] 标记的日志\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            children: \"\\uD83D\\uDD10 登录日志：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 255,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"查看后端控制台，观察 [LoginLog] 标记的日志\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 256,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 254,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            children: \"\\uD83D\\uDD0D 重点观察：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"IP地址是否从前端正确传递到后端，以及各个环节的IP获取情况\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                            lineNumber: 243,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 242,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83C\\uDF0D 当前环境信息\",\n                        size: \"small\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            direction: \"vertical\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    children: [\n                                        \"浏览器 User-Agent: \",\n                                         true ? navigator.userAgent : 0\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    children: [\n                                        \"当前时间: \",\n                                        new Date().toISOString()\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 270,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    children: [\n                                        \"页面URL: \",\n                                         true ? window.location.href : 0\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                            lineNumber: 268,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 267,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                lineNumber: 198,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n        lineNumber: 190,\n        columnNumber: 5\n    }, this);\n}\n_s(TestIPPage, \"X1Msb4W44QQRH4Yvzh/LKJZfd50=\");\n_c = TestIPPage;\nvar _c;\n$RefreshReg$(_c, \"TestIPPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/test-ip/page.tsx\n"));

/***/ })

});