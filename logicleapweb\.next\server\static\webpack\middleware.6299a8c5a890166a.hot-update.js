"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./middleware.ts":
/*!***********************!*\
  !*** ./middleware.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n\nfunction middleware(request) {\n    // 只处理 API 请求\n    if (request.nextUrl.pathname.startsWith(\"/api/\")) {\n        // 获取客户端真实IP地址\n        const forwarded = request.headers.get(\"x-forwarded-for\");\n        const realIp = request.headers.get(\"x-real-ip\");\n        const cfConnectingIp = request.headers.get(\"cf-connecting-ip\");\n        const remoteAddr = request.headers.get(\"remote-addr\");\n        // 尝试多种方式获取IP\n        let clientIp = request.ip;\n        if (!clientIp && forwarded) {\n            clientIp = forwarded.split(\",\")[0].trim();\n        }\n        if (!clientIp && realIp) {\n            clientIp = realIp;\n        }\n        if (!clientIp && cfConnectingIp) {\n            clientIp = cfConnectingIp;\n        }\n        if (!clientIp && remoteAddr) {\n            clientIp = remoteAddr;\n        }\n        // 如果还是没有获取到，尝试从其他来源\n        if (!clientIp) {\n            // 在开发环境下，我们可以使用一个模拟的IP\n            if (true) {\n                clientIp = \"*************\"; // 模拟的内网IP\n            } else {}\n        }\n        // console.log('🌐 中间件获取到的IP信息:', {\n        //   finalClientIp: clientIp,\n        //   forwarded,\n        //   realIp,\n        //   cfConnectingIp,\n        //   remoteAddr,\n        //   requestIp: request.ip,\n        //   url: request.nextUrl.pathname,\n        //   method: request.method\n        // });\n        // 创建新的请求头\n        const requestHeaders = new Headers(request.headers);\n        // 设置真实IP相关的头部\n        requestHeaders.set(\"x-forwarded-for\", clientIp);\n        requestHeaders.set(\"x-real-ip\", clientIp);\n        requestHeaders.set(\"x-client-ip\", clientIp);\n        // 创建新的响应，将修改后的头部传递给后端\n        const response = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.rewrite(request.nextUrl, {\n            request: {\n                headers: requestHeaders\n            }\n        });\n        return response;\n    }\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n}\nconst config = {\n    matcher: [\n        /*\r\n     * 匹配所有 API 路由\r\n     */ \"/api/:path*\"\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKG1pZGRsZXdhcmUpLy4vbWlkZGxld2FyZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBd0Q7QUFFakQsU0FBU0MsV0FBV0MsT0FBb0I7SUFDN0MsYUFBYTtJQUNiLElBQUlBLFFBQVFDLE9BQU8sQ0FBQ0MsUUFBUSxDQUFDQyxVQUFVLENBQUMsVUFBVTtRQUNoRCxjQUFjO1FBQ2QsTUFBTUMsWUFBWUosUUFBUUssT0FBTyxDQUFDQyxHQUFHLENBQUM7UUFDdEMsTUFBTUMsU0FBU1AsUUFBUUssT0FBTyxDQUFDQyxHQUFHLENBQUM7UUFDbkMsTUFBTUUsaUJBQWlCUixRQUFRSyxPQUFPLENBQUNDLEdBQUcsQ0FBQztRQUMzQyxNQUFNRyxhQUFhVCxRQUFRSyxPQUFPLENBQUNDLEdBQUcsQ0FBQztRQUV2QyxhQUFhO1FBQ2IsSUFBSUksV0FBV1YsUUFBUVcsRUFBRTtRQUV6QixJQUFJLENBQUNELFlBQVlOLFdBQVc7WUFDMUJNLFdBQVdOLFVBQVVRLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRSxDQUFDQyxJQUFJO1FBQ3pDO1FBRUEsSUFBSSxDQUFDSCxZQUFZSCxRQUFRO1lBQ3ZCRyxXQUFXSDtRQUNiO1FBRUEsSUFBSSxDQUFDRyxZQUFZRixnQkFBZ0I7WUFDL0JFLFdBQVdGO1FBQ2I7UUFFQSxJQUFJLENBQUNFLFlBQVlELFlBQVk7WUFDM0JDLFdBQVdEO1FBQ2I7UUFFQSxvQkFBb0I7UUFDcEIsSUFBSSxDQUFDQyxVQUFVO1lBQ2IsdUJBQXVCO1lBQ3ZCLElBQUlJLElBQXlCLEVBQWU7Z0JBQzFDSixXQUFXLGlCQUFpQixVQUFVO1lBQ3hDLE9BQU8sRUFFTjtRQUNIO1FBRUEsbUNBQW1DO1FBQ25DLDZCQUE2QjtRQUM3QixlQUFlO1FBQ2YsWUFBWTtRQUNaLG9CQUFvQjtRQUNwQixnQkFBZ0I7UUFDaEIsMkJBQTJCO1FBQzNCLG1DQUFtQztRQUNuQywyQkFBMkI7UUFDM0IsTUFBTTtRQUVOLFVBQVU7UUFDVixNQUFNSyxpQkFBaUIsSUFBSUMsUUFBUWhCLFFBQVFLLE9BQU87UUFFbEQsY0FBYztRQUNkVSxlQUFlRSxHQUFHLENBQUMsbUJBQW1CUDtRQUN0Q0ssZUFBZUUsR0FBRyxDQUFDLGFBQWFQO1FBQ2hDSyxlQUFlRSxHQUFHLENBQUMsZUFBZVA7UUFFbEMsc0JBQXNCO1FBQ3RCLE1BQU1RLFdBQVdwQixxREFBWUEsQ0FBQ3FCLE9BQU8sQ0FBQ25CLFFBQVFDLE9BQU8sRUFBRTtZQUNyREQsU0FBUztnQkFDUEssU0FBU1U7WUFDWDtRQUNGO1FBRUEsT0FBT0c7SUFDVDtJQUVBLE9BQU9wQixxREFBWUEsQ0FBQ3NCLElBQUk7QUFDMUI7QUFFTyxNQUFNQyxTQUFTO0lBQ3BCQyxTQUFTO1FBQ1A7O0tBRUMsR0FDRDtLQUNEO0FBQ0gsRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9taWRkbGV3YXJlLnRzPzQyMmQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgTmV4dFJlcXVlc3QsIE5leHRSZXNwb25zZSB9IGZyb20gJ25leHQvc2VydmVyJztcclxuXHJcbmV4cG9ydCBmdW5jdGlvbiBtaWRkbGV3YXJlKHJlcXVlc3Q6IE5leHRSZXF1ZXN0KSB7XHJcbiAgLy8g5Y+q5aSE55CGIEFQSSDor7fmsYJcclxuICBpZiAocmVxdWVzdC5uZXh0VXJsLnBhdGhuYW1lLnN0YXJ0c1dpdGgoJy9hcGkvJykpIHtcclxuICAgIC8vIOiOt+WPluWuouaIt+err+ecn+WunklQ5Zyw5Z2AXHJcbiAgICBjb25zdCBmb3J3YXJkZWQgPSByZXF1ZXN0LmhlYWRlcnMuZ2V0KCd4LWZvcndhcmRlZC1mb3InKTtcclxuICAgIGNvbnN0IHJlYWxJcCA9IHJlcXVlc3QuaGVhZGVycy5nZXQoJ3gtcmVhbC1pcCcpO1xyXG4gICAgY29uc3QgY2ZDb25uZWN0aW5nSXAgPSByZXF1ZXN0LmhlYWRlcnMuZ2V0KCdjZi1jb25uZWN0aW5nLWlwJyk7XHJcbiAgICBjb25zdCByZW1vdGVBZGRyID0gcmVxdWVzdC5oZWFkZXJzLmdldCgncmVtb3RlLWFkZHInKTtcclxuXHJcbiAgICAvLyDlsJ3or5XlpJrnp43mlrnlvI/ojrflj5ZJUFxyXG4gICAgbGV0IGNsaWVudElwID0gcmVxdWVzdC5pcDtcclxuXHJcbiAgICBpZiAoIWNsaWVudElwICYmIGZvcndhcmRlZCkge1xyXG4gICAgICBjbGllbnRJcCA9IGZvcndhcmRlZC5zcGxpdCgnLCcpWzBdLnRyaW0oKTtcclxuICAgIH1cclxuXHJcbiAgICBpZiAoIWNsaWVudElwICYmIHJlYWxJcCkge1xyXG4gICAgICBjbGllbnRJcCA9IHJlYWxJcDtcclxuICAgIH1cclxuXHJcbiAgICBpZiAoIWNsaWVudElwICYmIGNmQ29ubmVjdGluZ0lwKSB7XHJcbiAgICAgIGNsaWVudElwID0gY2ZDb25uZWN0aW5nSXA7XHJcbiAgICB9XHJcblxyXG4gICAgaWYgKCFjbGllbnRJcCAmJiByZW1vdGVBZGRyKSB7XHJcbiAgICAgIGNsaWVudElwID0gcmVtb3RlQWRkcjtcclxuICAgIH1cclxuXHJcbiAgICAvLyDlpoLmnpzov5jmmK/msqHmnInojrflj5bliLDvvIzlsJ3or5Xku47lhbbku5bmnaXmupBcclxuICAgIGlmICghY2xpZW50SXApIHtcclxuICAgICAgLy8g5Zyo5byA5Y+R546v5aKD5LiL77yM5oiR5Lus5Y+v5Lul5L2/55So5LiA5Liq5qih5ouf55qESVBcclxuICAgICAgaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAnZGV2ZWxvcG1lbnQnKSB7XHJcbiAgICAgICAgY2xpZW50SXAgPSAnMTkyLjE2OC4xLjEwMCc7IC8vIOaooeaLn+eahOWGhee9kUlQXHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgY2xpZW50SXAgPSAnMTI3LjAuMC4xJztcclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC8vIGNvbnNvbGUubG9nKCfwn4yQIOS4remXtOS7tuiOt+WPluWIsOeahElQ5L+h5oGvOicsIHtcclxuICAgIC8vICAgZmluYWxDbGllbnRJcDogY2xpZW50SXAsXHJcbiAgICAvLyAgIGZvcndhcmRlZCxcclxuICAgIC8vICAgcmVhbElwLFxyXG4gICAgLy8gICBjZkNvbm5lY3RpbmdJcCxcclxuICAgIC8vICAgcmVtb3RlQWRkcixcclxuICAgIC8vICAgcmVxdWVzdElwOiByZXF1ZXN0LmlwLFxyXG4gICAgLy8gICB1cmw6IHJlcXVlc3QubmV4dFVybC5wYXRobmFtZSxcclxuICAgIC8vICAgbWV0aG9kOiByZXF1ZXN0Lm1ldGhvZFxyXG4gICAgLy8gfSk7XHJcblxyXG4gICAgLy8g5Yib5bu65paw55qE6K+35rGC5aS0XHJcbiAgICBjb25zdCByZXF1ZXN0SGVhZGVycyA9IG5ldyBIZWFkZXJzKHJlcXVlc3QuaGVhZGVycyk7XHJcblxyXG4gICAgLy8g6K6+572u55yf5a6eSVDnm7jlhbPnmoTlpLTpg6hcclxuICAgIHJlcXVlc3RIZWFkZXJzLnNldCgneC1mb3J3YXJkZWQtZm9yJywgY2xpZW50SXApO1xyXG4gICAgcmVxdWVzdEhlYWRlcnMuc2V0KCd4LXJlYWwtaXAnLCBjbGllbnRJcCk7XHJcbiAgICByZXF1ZXN0SGVhZGVycy5zZXQoJ3gtY2xpZW50LWlwJywgY2xpZW50SXApO1xyXG5cclxuICAgIC8vIOWIm+W7uuaWsOeahOWTjeW6lO+8jOWwhuS/ruaUueWQjueahOWktOmDqOS8oOmAkue7meWQjuerr1xyXG4gICAgY29uc3QgcmVzcG9uc2UgPSBOZXh0UmVzcG9uc2UucmV3cml0ZShyZXF1ZXN0Lm5leHRVcmwsIHtcclxuICAgICAgcmVxdWVzdDoge1xyXG4gICAgICAgIGhlYWRlcnM6IHJlcXVlc3RIZWFkZXJzLFxyXG4gICAgICB9LFxyXG4gICAgfSk7XHJcblxyXG4gICAgcmV0dXJuIHJlc3BvbnNlO1xyXG4gIH1cclxuXHJcbiAgcmV0dXJuIE5leHRSZXNwb25zZS5uZXh0KCk7XHJcbn1cclxuXHJcbmV4cG9ydCBjb25zdCBjb25maWcgPSB7XHJcbiAgbWF0Y2hlcjogW1xyXG4gICAgLypcclxuICAgICAqIOWMuemFjeaJgOaciSBBUEkg6Lev55SxXHJcbiAgICAgKi9cclxuICAgICcvYXBpLzpwYXRoKicsXHJcbiAgXSxcclxufTtcclxuIl0sIm5hbWVzIjpbIk5leHRSZXNwb25zZSIsIm1pZGRsZXdhcmUiLCJyZXF1ZXN0IiwibmV4dFVybCIsInBhdGhuYW1lIiwic3RhcnRzV2l0aCIsImZvcndhcmRlZCIsImhlYWRlcnMiLCJnZXQiLCJyZWFsSXAiLCJjZkNvbm5lY3RpbmdJcCIsInJlbW90ZUFkZHIiLCJjbGllbnRJcCIsImlwIiwic3BsaXQiLCJ0cmltIiwicHJvY2VzcyIsInJlcXVlc3RIZWFkZXJzIiwiSGVhZGVycyIsInNldCIsInJlc3BvbnNlIiwicmV3cml0ZSIsIm5leHQiLCJjb25maWciLCJtYXRjaGVyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(middleware)/./middleware.ts\n");

/***/ })

});