"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./middleware.ts":
/*!***********************!*\
  !*** ./middleware.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n\nfunction middleware(request) {\n    // 只处理 API 请求\n    if (request.nextUrl.pathname.startsWith(\"/api/\")) {\n        // 获取客户端真实IP地址\n        const forwarded = request.headers.get(\"x-forwarded-for\");\n        const realIp = request.headers.get(\"x-real-ip\");\n        const cfConnectingIp = request.headers.get(\"cf-connecting-ip\");\n        const remoteAddr = request.headers.get(\"remote-addr\");\n        // 尝试多种方式获取IP\n        let clientIp = request.ip;\n        if (!clientIp && forwarded) {\n            clientIp = forwarded.split(\",\")[0].trim();\n        }\n        if (!clientIp && realIp) {\n            clientIp = realIp;\n        }\n        if (!clientIp && cfConnectingIp) {\n            clientIp = cfConnectingIp;\n        }\n        if (!clientIp && remoteAddr) {\n            clientIp = remoteAddr;\n        }\n        // 如果还是没有获取到，尝试从其他来源\n        if (!clientIp) {\n            // 检查是否通过内网穿透访问\n            const host = request.headers.get(\"host\");\n            const isNgrokTunnel = host?.includes(\"ngrok\") || host?.includes(\"tunnel\");\n            if (true) {\n                if (isNgrokTunnel) {\n                    // 通过内网穿透访问，使用默认IP让系统自动获取真实IP\n                    clientIp = \"127.0.0.1\";\n                    console.log(\"\\uD83C\\uDF10 [Middleware] 检测到内网穿透访问，使用系统自动获取IP\");\n                } else {\n                    // 本地开发环境，使用模拟公网IP进行测试\n                    clientIp = \"**************\"; // 模拟的公网IP（百度的IP）\n                    console.log(\"\\uD83C\\uDFAD [Middleware] 本地开发环境使用模拟公网IP:\", clientIp);\n                }\n            } else {}\n        }\n        // 添加详细的IP获取日志\n        console.log(\"\\uD83C\\uDF10 [Middleware] IP地址获取详情:\", {\n            url: request.nextUrl.pathname,\n            method: request.method,\n            原始IP来源: {\n                \"request.ip\": request.ip,\n                \"x-forwarded-for\": forwarded,\n                \"x-real-ip\": realIp,\n                \"cf-connecting-ip\": cfConnectingIp,\n                \"remote-addr\": remoteAddr\n            },\n            最终确定IP: clientIp,\n            环境: \"development\",\n            时间戳: new Date().toISOString()\n        });\n        // 创建新的请求头\n        const requestHeaders = new Headers(request.headers);\n        // 设置真实IP相关的头部\n        requestHeaders.set(\"x-forwarded-for\", clientIp);\n        requestHeaders.set(\"x-real-ip\", clientIp);\n        requestHeaders.set(\"x-client-ip\", clientIp);\n        // 创建新的响应，将修改后的头部传递给后端\n        const response = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.rewrite(request.nextUrl, {\n            request: {\n                headers: requestHeaders\n            }\n        });\n        return response;\n    }\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n}\nconst config = {\n    matcher: [\n        /*\r\n     * 匹配所有 API 路由\r\n     */ \"/api/:path*\"\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./middleware.ts\n");

/***/ })

});