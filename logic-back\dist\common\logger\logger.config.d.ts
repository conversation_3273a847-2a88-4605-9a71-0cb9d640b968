import * as winston from 'winston';
import { WinstonModuleOptions } from 'nest-winston';
export declare function createLoggerConfig(): WinstonModuleOptions;
export declare function overrideConsole(logger: winston.Logger): {
    log: {
        (...data: any[]): void;
        (message?: any, ...optionalParams: any[]): void;
    };
    error: {
        (...data: any[]): void;
        (message?: any, ...optionalParams: any[]): void;
    };
    warn: {
        (...data: any[]): void;
        (message?: any, ...optionalParams: any[]): void;
    };
    info: {
        (...data: any[]): void;
        (message?: any, ...optionalParams: any[]): void;
    };
    debug: {
        (...data: any[]): void;
        (message?: any, ...optionalParams: any[]): void;
    };
    trace: {
        (...data: any[]): void;
        (message?: any, ...optionalParams: any[]): void;
    };
    assert: {
        (condition?: boolean, ...data: any[]): void;
        (value: any, message?: string, ...optionalParams: any[]): void;
    };
    dir: {
        (item?: any, options?: any): void;
        (obj: any, options?: import("util").InspectOptions): void;
    };
    dirxml: {
        (...data: any[]): void;
        (...data: any[]): void;
    };
    group: {
        (...data: any[]): void;
        (...label: any[]): void;
    };
    groupCollapsed: {
        (...data: any[]): void;
        (...label: any[]): void;
    };
    groupEnd: {
        (): void;
        (): void;
    };
    table: {
        (tabularData?: any, properties?: string[]): void;
        (tabularData: any, properties?: readonly string[]): void;
    };
    time: {
        (label?: string): void;
        (label?: string): void;
    };
    timeEnd: {
        (label?: string): void;
        (label?: string): void;
    };
    count: {
        (label?: string): void;
        (label?: string): void;
    };
    clear: {
        (): void;
        (): void;
    };
};
