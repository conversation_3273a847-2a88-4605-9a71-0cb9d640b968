import { NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Observable } from 'rxjs';
import { Request } from 'express';
import { UnifiedResponseService, UnifiedResponse } from './unified-response.service';
export declare class UnifiedResponseInterceptor<T> implements NestInterceptor<T, UnifiedResponse<T>> {
    private readonly responseService;
    constructor(responseService: UnifiedResponseService);
    intercept(context: ExecutionContext, next: CallHandler): Observable<UnifiedResponse<T>>;
    private isUnifiedResponse;
    private isLegacyResponse;
}
export declare class RequestTimeMiddleware {
    use(req: Request, res: any, next: () => void): void;
}
export declare function ApiResponse(message?: string): (target: any, propertyKey: string, descriptor: PropertyDescriptor) => PropertyDescriptor;
export declare function ApiErrorResponse(message?: string, code?: number): (target: any, propertyKey: string, descriptor: PropertyDescriptor) => PropertyDescriptor;
