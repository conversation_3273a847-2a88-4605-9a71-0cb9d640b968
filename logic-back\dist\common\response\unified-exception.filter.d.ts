import { ExceptionFilter, ArgumentsHost, HttpException, HttpStatus } from '@nestjs/common';
export declare class UnifiedExceptionFilter implements ExceptionFilter {
    private readonly responseService;
    constructor(responseService: UnifiedResponseService);
    catch(exception: unknown, host: ArgumentsHost): void;
    private mapHttpStatusToBusinessCode;
    private getDefaultMessageByHttpStatus;
}
export declare class BusinessException extends HttpException {
    constructor(message: string, businessCode?: number, httpStatus?: HttpStatus, data?: any);
}
export declare class ValidationException extends BusinessException {
    constructor(message: string, data?: any);
}
export declare class UnauthorizedException extends BusinessException {
    constructor(message?: string, data?: any);
}
export declare class ForbiddenException extends BusinessException {
    constructor(message?: string, data?: any);
}
export declare class NotFoundException extends BusinessException {
    constructor(message?: string, data?: any);
}
