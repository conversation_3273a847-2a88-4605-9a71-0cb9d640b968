"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResponseTransformInterceptor = void 0;
const common_1 = require("@nestjs/common");
const operators_1 = require("rxjs/operators");
const http_response_interface_1 = require("./http-response.interface");
const http_response_result_service_1 = require("./http_response_result.service");
let ResponseTransformInterceptor = class ResponseTransformInterceptor {
    responseService;
    constructor(responseService) {
        this.responseService = responseService;
    }
    intercept(context, next) {
        const request = context.switchToHttp().getRequest();
        const path = request.path || request.url || '';
        const startTime = Date.now();
        const excludePaths = ['/weixin/message', '/health', '/metrics'];
        if (excludePaths.some(excludePath => path.includes(excludePath))) {
            return next.handle();
        }
        return next.handle().pipe((0, operators_1.map)(data => {
            const executionTime = Date.now() - startTime;
            const logOptions = {
                path,
                requestId: request.headers['x-request-id'],
                executionTime,
                context: 'ResponseTransformInterceptor',
                enableLog: true
            };
            if (data && typeof data === 'object' && 'code' in data && 'msg' in data && 'data' in data) {
                return data;
            }
            return this.responseService.success(data, '操作成功', http_response_interface_1.SUCCESS_CODE, logOptions);
        }));
    }
};
exports.ResponseTransformInterceptor = ResponseTransformInterceptor;
exports.ResponseTransformInterceptor = ResponseTransformInterceptor = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [http_response_result_service_1.HttpResponseResultService])
], ResponseTransformInterceptor);
//# sourceMappingURL=response-transform.interceptor.js.map