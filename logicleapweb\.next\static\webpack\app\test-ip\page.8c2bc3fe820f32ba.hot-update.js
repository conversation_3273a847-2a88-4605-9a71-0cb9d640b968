"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/test-ip/page",{

/***/ "(app-pages-browser)/./app/test-ip/page.tsx":
/*!******************************!*\
  !*** ./app/test-ip/page.tsx ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ TestIPPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Divider,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/typography/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Divider,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/space/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Divider,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/card/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Divider,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Divider,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/divider/index.js\");\n/* harmony import */ var _lib_request__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/request */ \"(app-pages-browser)/./lib/request.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst { Title, Text, Paragraph } = _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_3__[\"default\"];\nfunction TestIPPage() {\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [result, setResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [realPublicIP, setRealPublicIP] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [logs, setLogs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [debugInfo, setDebugInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 添加日志的辅助函数\n    const addLog = (message, data)=>{\n        const timestamp = new Date().toLocaleTimeString();\n        const logEntry = \"[\".concat(timestamp, \"] \").concat(message);\n        // 输出到控制台\n        if (data) {\n            console.log(message, data);\n        } else {\n            console.log(message);\n        }\n        // 添加到页面日志\n        setLogs((prev)=>[\n                ...prev.slice(-19),\n                logEntry\n            ]); // 保留最近20条日志\n    };\n    // 清空日志\n    const clearLogs = ()=>{\n        setLogs([]);\n        console.clear();\n        addLog(\"\\uD83E\\uDDF9 [Frontend] 日志已清空\");\n    };\n    // 诊断内网穿透问题\n    const diagnoseTunnel = async ()=>{\n        setLoading(true);\n        try {\n            var _data_summary, _data_summary1, _data_summary2, _data_summary3, _data_summary4;\n            addLog(\"\\uD83D\\uDD0D [Frontend Diagnose] 开始诊断内网穿透问题...\");\n            const response = await fetch(\"/api/debug/headers\");\n            const data = await response.json();\n            addLog(\"\\uD83D\\uDD0D [Frontend Diagnose] 服务器调试信息:\", data);\n            setDebugInfo(data);\n            // 分析结果\n            const analysis = {\n                访问方式: data.accessType,\n                检测到的IP: (_data_summary = data.summary) === null || _data_summary === void 0 ? void 0 : _data_summary.detectedIp,\n                是否真实IP: (_data_summary1 = data.summary) === null || _data_summary1 === void 0 ? void 0 : _data_summary1.isRealIp,\n                IP相关头部数量: (_data_summary2 = data.summary) === null || _data_summary2 === void 0 ? void 0 : _data_summary2.ipRelatedHeaders,\n                主要问题: ((_data_summary3 = data.summary) === null || _data_summary3 === void 0 ? void 0 : _data_summary3.detectedIp) === \"127.0.0.1\" ? \"内网穿透未正确转发真实IP\" : \"正常\",\n                建议: ((_data_summary4 = data.summary) === null || _data_summary4 === void 0 ? void 0 : _data_summary4.detectedIp) === \"127.0.0.1\" ? \"尝试其他内网穿透服务或检查穿透配置\" : \"当前配置正常\"\n            };\n            addLog(\"\\uD83D\\uDCCA [Frontend Diagnose] 诊断分析结果:\", analysis);\n            setResult({\n                type: \"tunnel-diagnose\",\n                data: data,\n                analysis: analysis,\n                timestamp: new Date().toISOString()\n            });\n        } catch (error) {\n            addLog(\"❌ [Frontend Diagnose] 诊断失败:\", error);\n            setResult({\n                type: \"error\",\n                error: error.message,\n                timestamp: new Date().toISOString()\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 页面加载时输出环境信息\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        var _navigator_connection, _navigator_connection1;\n        addLog(\"\\uD83C\\uDF10 [Frontend Init] 测试页面初始化...\");\n        console.log(\"\\uD83D\\uDDA5️ [Frontend Init] 浏览器环境信息:\", {\n            页面信息: {\n                URL: window.location.href,\n                域名: window.location.hostname,\n                端口: window.location.port,\n                协议: window.location.protocol,\n                路径: window.location.pathname\n            },\n            访问方式: {\n                是否本地访问: window.location.hostname === \"localhost\",\n                是否内网穿透: window.location.hostname.includes(\"ngrok\") || window.location.hostname.includes(\"tunnel\"),\n                访问类型: window.location.hostname === \"localhost\" ? \"本地开发\" : window.location.hostname.includes(\"ngrok\") ? \"ngrok穿透\" : window.location.hostname.includes(\"tunnel\") ? \"其他穿透\" : \"未知\"\n            },\n            浏览器信息: {\n                用户代理: navigator.userAgent,\n                语言: navigator.language,\n                平台: navigator.platform,\n                在线状态: navigator.onLine\n            },\n            网络信息: {\n                连接类型: ((_navigator_connection = navigator.connection) === null || _navigator_connection === void 0 ? void 0 : _navigator_connection.effectiveType) || \"未知\",\n                网络状态: ((_navigator_connection1 = navigator.connection) === null || _navigator_connection1 === void 0 ? void 0 : _navigator_connection1.downlink) ? \"\".concat(navigator.connection.downlink, \"Mbps\") : \"未知\"\n            },\n            时间信息: {\n                本地时间: new Date().toISOString(),\n                时区: Intl.DateTimeFormat().resolvedOptions().timeZone,\n                时区偏移: new Date().getTimezoneOffset()\n            }\n        });\n        // 输出预期的测试流程\n        console.log(\"\\uD83D\\uDCCB [Frontend Init] 测试流程说明:\", {\n            测试目标: \"IP地址获取和传递功能验证\",\n            测试步骤: [\n                \"1. 获取真实公网IP (通过第三方API)\",\n                \"2. 测试当前IP位置获取 (后端API)\",\n                \"3. 测试指定IP查询 (*******)\",\n                \"4. 测试登录IP记录 (模拟登录失败)\"\n            ],\n            观察要点: [\n                \"前端请求日志 (\\uD83D\\uDCE4 [Frontend Request])\",\n                \"前端响应日志 (\\uD83D\\uDCE5 [Frontend Response])\",\n                \"中间件IP处理 (\\uD83C\\uDF10 [Middleware])\",\n                \"后端IP提取 (\\uD83D\\uDDA5️ [Backend])\",\n                \"登录IP记录 (\\uD83D\\uDD10 [LoginLog])\"\n            ],\n            预期结果: {\n                本地访问: \"IP为模拟值 (**************)\",\n                穿透访问: \"IP为真实公网IP\",\n                地理位置: \"根据IP解析出对应位置\"\n            }\n        });\n    }, []);\n    // 获取真实的公网IP地址\n    const getRealPublicIP = async ()=>{\n        setLoading(true);\n        try {\n            console.log(\"\\uD83C\\uDF0D [Frontend] 开始获取真实公网IP...\");\n            // 使用多个IP查询服务，提高成功率\n            const ipServices = [\n                \"https://api.ipify.org?format=json\",\n                \"https://ipapi.co/json/\",\n                \"https://httpbin.org/ip\",\n                \"https://api.ip.sb/ip\",\n                \"https://ifconfig.me/ip\",\n                \"https://icanhazip.com\"\n            ];\n            for (const service of ipServices){\n                try {\n                    console.log(\"\\uD83D\\uDD0D 尝试获取公网IP: \".concat(service));\n                    const controller = new AbortController();\n                    const timeoutId = setTimeout(()=>controller.abort(), 5000);\n                    const response = await fetch(service, {\n                        method: \"GET\",\n                        signal: controller.signal\n                    });\n                    clearTimeout(timeoutId);\n                    if (!response.ok) continue;\n                    let data;\n                    const contentType = response.headers.get(\"content-type\");\n                    if (contentType && contentType.includes(\"application/json\")) {\n                        data = await response.json();\n                        const ip = data.ip || data.origin || data.query;\n                        if (ip && isValidIPAddress(ip)) {\n                            console.log(\"✅ 成功获取公网IP: \".concat(ip, \" (来源: \").concat(service, \")\"));\n                            setRealPublicIP(ip);\n                            setResult({\n                                type: \"real-public-ip\",\n                                ip: ip,\n                                source: service,\n                                timestamp: new Date().toISOString()\n                            });\n                            return;\n                        }\n                    } else {\n                        const text = await response.text();\n                        const ip = text.trim();\n                        if (isValidIPAddress(ip)) {\n                            console.log(\"✅ 成功获取公网IP: \".concat(ip, \" (来源: \").concat(service, \")\"));\n                            setRealPublicIP(ip);\n                            setResult({\n                                type: \"real-public-ip\",\n                                ip: ip,\n                                source: service,\n                                timestamp: new Date().toISOString()\n                            });\n                            return;\n                        }\n                    }\n                } catch (error) {\n                    console.log(\"❌ 获取公网IP失败: \".concat(service, \" - \").concat(error));\n                    continue;\n                }\n            }\n            throw new Error(\"所有公网IP服务都无法访问\");\n        } catch (error) {\n            console.error(\"❌ [Frontend] 获取真实公网IP失败:\", error);\n            setResult({\n                type: \"error\",\n                error: error.message,\n                timestamp: new Date().toISOString()\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 验证IP地址格式\n    const isValidIPAddress = (ip)=>{\n        const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;\n        return ipv4Regex.test(ip);\n    };\n    // 测试获取当前IP位置\n    const testCurrentIP = async ()=>{\n        setLoading(true);\n        try {\n            addLog(\"\\uD83E\\uDDEA [Frontend Test] 开始测试当前IP获取...\");\n            addLog(\"\\uD83C\\uDF10 [Frontend Test] 当前环境信息:\", {\n                页面URL: window.location.href,\n                域名: window.location.hostname,\n                是否内网穿透: window.location.hostname.includes(\"ngrok\") || window.location.hostname.includes(\"tunnel\"),\n                用户代理: navigator.userAgent.substring(0, 100) + \"...\",\n                时间戳: new Date().toISOString()\n            });\n            console.log(\"\\uD83D\\uDCE4 [Frontend Test] 准备发送请求到: /api/v1/ip-location/current\");\n            const startTime = Date.now();\n            const response = await _lib_request__WEBPACK_IMPORTED_MODULE_2__[\"default\"].get(\"/api/v1/ip-location/current\");\n            const endTime = Date.now();\n            console.log(\"\\uD83D\\uDCE5 [Frontend Test] 请求完成:\", {\n                耗时: \"\".concat(endTime - startTime, \"ms\"),\n                响应状态: response.status,\n                响应数据: response.data,\n                时间戳: new Date().toISOString()\n            });\n            setResult({\n                type: \"current-ip\",\n                data: response.data,\n                requestTime: endTime - startTime,\n                timestamp: new Date().toISOString()\n            });\n        } catch (error) {\n            var _error_response, _error_response1, _error_response2;\n            console.error(\"❌ [Frontend Test] 当前IP测试失败:\", {\n                错误类型: error.name,\n                错误消息: error.message,\n                响应状态: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status,\n                响应数据: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.data,\n                完整错误: error,\n                时间戳: new Date().toISOString()\n            });\n            setResult({\n                type: \"error\",\n                error: error.message,\n                response: (_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.data,\n                timestamp: new Date().toISOString()\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 测试查询指定IP\n    const testQueryIP = async ()=>{\n        setLoading(true);\n        try {\n            const testIP = \"*******\"; // Google DNS\n            console.log(\"\\uD83E\\uDDEA [Frontend Test] 开始测试IP查询...\");\n            console.log(\"\\uD83C\\uDFAF [Frontend Test] 查询参数:\", {\n                目标IP: testIP,\n                IP类型: \"Google DNS服务器\",\n                预期位置: \"美国\",\n                包含风险评估: false,\n                时间戳: new Date().toISOString()\n            });\n            const queryUrl = \"/api/v1/ip-location/query?ip=\".concat(testIP, \"&includeRisk=false\");\n            console.log(\"\\uD83D\\uDCE4 [Frontend Test] 准备发送查询请求:\", queryUrl);\n            const startTime = Date.now();\n            const response = await _lib_request__WEBPACK_IMPORTED_MODULE_2__[\"default\"].get(queryUrl);\n            const endTime = Date.now();\n            console.log(\"\\uD83D\\uDCE5 [Frontend Test] IP查询完成:\", {\n                查询IP: testIP,\n                耗时: \"\".concat(endTime - startTime, \"ms\"),\n                响应状态: response.status,\n                地理位置: response.data ? {\n                    国家: response.data.country,\n                    省份: response.data.province,\n                    城市: response.data.city,\n                    运营商: response.data.isp,\n                    置信度: response.data.confidence\n                } : \"无数据\",\n                完整响应: response.data,\n                时间戳: new Date().toISOString()\n            });\n            setResult({\n                type: \"query-ip\",\n                testIP,\n                data: response.data,\n                requestTime: endTime - startTime,\n                timestamp: new Date().toISOString()\n            });\n        } catch (error) {\n            var _error_response, _error_response1, _error_response2;\n            console.error(\"❌ [Frontend Test] IP查询失败:\", {\n                查询IP: \"*******\",\n                错误类型: error.name,\n                错误消息: error.message,\n                响应状态: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status,\n                响应数据: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.data,\n                可能原因: [\n                    \"后端服务未启动\",\n                    \"IP解析服务异常\",\n                    \"网络连接问题\"\n                ],\n                时间戳: new Date().toISOString()\n            });\n            setResult({\n                type: \"error\",\n                error: error.message,\n                response: (_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.data,\n                timestamp: new Date().toISOString()\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 测试登录接口（观察IP日志）\n    const testLoginIP = async ()=>{\n        setLoading(true);\n        try {\n            console.log(\"\\uD83E\\uDDEA [Frontend Test] 开始测试登录IP获取...\");\n            console.log(\"\\uD83D\\uDD10 [Frontend Test] 登录测试说明:\", {\n                目的: \"观察登录时的IP获取和记录过程\",\n                预期结果: \"登录失败（使用错误凭据）\",\n                观察重点: [\n                    \"IP地址获取\",\n                    \"登录日志记录\",\n                    \"错误处理\"\n                ],\n                测试凭据: {\n                    手机号: \"12345678910\",\n                    密码: \"123456 (错误密码)\"\n                },\n                时间戳: new Date().toISOString()\n            });\n            console.log(\"\\uD83D\\uDCE4 [Frontend Test] 发送登录请求...\");\n            const startTime = Date.now();\n            // 这里故意使用错误的登录信息，只是为了触发IP获取逻辑\n            const response = await _lib_request__WEBPACK_IMPORTED_MODULE_2__[\"default\"].post(\"/api/user-auth/password\", {\n                phone: \"12345678901\",\n                password: \"123456\"\n            });\n            const endTime = Date.now();\n            console.log(\"\\uD83D\\uDCE5 [Frontend Test] 登录响应 (意外成功):\", {\n                耗时: \"\".concat(endTime - startTime, \"ms\"),\n                响应状态: response.status,\n                响应数据: response.data,\n                注意: \"这不应该成功，请检查后端验证逻辑\",\n                时间戳: new Date().toISOString()\n            });\n            setResult({\n                type: \"login-test\",\n                data: response.data,\n                requestTime: endTime - startTime,\n                timestamp: new Date().toISOString()\n            });\n        } catch (error) {\n            var _error_response, _error_response1, _error_response2;\n            console.log(\"\\uD83D\\uDCDD [Frontend Test] 登录测试完成 (预期失败):\", {\n                错误类型: error.name,\n                错误消息: error.message,\n                响应状态: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status,\n                响应数据: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.data,\n                分析: {\n                    是否预期失败: true,\n                    失败原因: \"使用了错误的登录凭据\",\n                    IP获取状态: \"应该已触发IP获取和日志记录\",\n                    后续检查: \"查看后端控制台的 [LoginLog] 日志\"\n                },\n                时间戳: new Date().toISOString()\n            });\n            setResult({\n                type: \"login-test\",\n                error: error.message,\n                response: (_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.data,\n                note: \"这是预期的失败，主要用于观察IP获取日志\",\n                timestamp: new Date().toISOString()\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"24px\",\n            maxWidth: \"1200px\",\n            margin: \"0 auto\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                level: 2,\n                children: \"\\uD83E\\uDDEA IP地址获取测试页面\"\n            }, void 0, false, {\n                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                lineNumber: 416,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                children: \"这个页面用于测试前端到后端的IP地址传递和获取功能。 请打开浏览器开发者工具的控制台，以及后端服务器的日志，观察IP获取过程。\"\n            }, void 0, false, {\n                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                lineNumber: 418,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                direction: \"vertical\",\n                size: \"large\",\n                style: {\n                    width: \"100%\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83C\\uDFAF 测试功能\",\n                        size: \"small\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                wrap: true,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        type: \"primary\",\n                                        loading: loading,\n                                        onClick: getRealPublicIP,\n                                        style: {\n                                            background: \"#52c41a\",\n                                            borderColor: \"#52c41a\"\n                                        },\n                                        children: \"\\uD83C\\uDF0D 获取真实公网IP\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                        lineNumber: 428,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        loading: loading,\n                                        onClick: testCurrentIP,\n                                        children: \"测试获取当前IP位置\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                        lineNumber: 437,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        loading: loading,\n                                        onClick: testQueryIP,\n                                        children: \"测试查询指定IP (*******)\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                        lineNumber: 444,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        loading: loading,\n                                        onClick: testLoginIP,\n                                        danger: true,\n                                        children: \"测试登录IP获取 (会失败)\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                        lineNumber: 451,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        onClick: clearLogs,\n                                        style: {\n                                            marginLeft: \"12px\"\n                                        },\n                                        children: \"\\uD83E\\uDDF9 清空日志\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                        lineNumber: 459,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        loading: loading,\n                                        onClick: diagnoseTunnel,\n                                        style: {\n                                            marginLeft: \"12px\",\n                                            borderColor: \"#fa8c16\",\n                                            color: \"#fa8c16\"\n                                        },\n                                        children: \"\\uD83D\\uDD0D 诊断穿透问题\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                        lineNumber: 466,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                lineNumber: 427,\n                                columnNumber: 11\n                            }, this),\n                            realPublicIP && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginTop: \"12px\",\n                                    padding: \"8px\",\n                                    background: \"#f6ffed\",\n                                    border: \"1px solid #b7eb8f\",\n                                    borderRadius: \"6px\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    strong: true,\n                                    style: {\n                                        color: \"#52c41a\"\n                                    },\n                                    children: [\n                                        \"\\uD83C\\uDF0D 你的真实公网IP: \",\n                                        realPublicIP\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 477,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                lineNumber: 476,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 426,\n                        columnNumber: 9\n                    }, this),\n                    result && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83D\\uDCCA 测试结果\",\n                        size: \"small\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                background: \"#f5f5f5\",\n                                padding: \"16px\",\n                                borderRadius: \"6px\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                style: {\n                                    margin: 0,\n                                    fontSize: \"12px\",\n                                    lineHeight: \"1.4\"\n                                },\n                                children: JSON.stringify(result, null, 2)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                lineNumber: 486,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                            lineNumber: 485,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 484,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83D\\uDCDD 实时前端日志\",\n                        size: \"small\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    background: \"#000\",\n                                    color: \"#00ff00\",\n                                    padding: \"12px\",\n                                    borderRadius: \"6px\",\n                                    fontFamily: \"Monaco, Consolas, monospace\",\n                                    fontSize: \"12px\",\n                                    maxHeight: \"300px\",\n                                    overflowY: \"auto\"\n                                },\n                                children: logs.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        color: \"#666\"\n                                    },\n                                    children: \"等待日志输出...\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 506,\n                                    columnNumber: 15\n                                }, this) : logs.map((log, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            marginBottom: \"2px\"\n                                        },\n                                        children: log\n                                    }, index, false, {\n                                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                        lineNumber: 509,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                lineNumber: 495,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginTop: \"8px\",\n                                    fontSize: \"12px\",\n                                    color: \"#666\"\n                                },\n                                children: \"\\uD83D\\uDCA1 提示：这里显示前端日志，完整日志请查看浏览器控制台\"\n                            }, void 0, false, {\n                                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                lineNumber: 515,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 494,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 520,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83D\\uDCCB 观察要点\",\n                        size: \"small\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            direction: \"vertical\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            children: \"\\uD83C\\uDF10 前端中间件日志：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 526,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"查看浏览器控制台，观察 [Middleware] 标记的日志\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 527,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 525,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            children: \"\\uD83D\\uDDA5️ 后端IP提取日志：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 531,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"查看后端控制台，观察 [Backend] 标记的日志\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 532,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 530,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            children: \"\\uD83D\\uDD10 登录日志：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 536,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"查看后端控制台，观察 [LoginLog] 标记的日志\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 537,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 535,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            children: \"\\uD83D\\uDD0D 重点观察：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 541,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"IP地址是否从前端正确传递到后端，以及各个环节的IP获取情况\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 542,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 540,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                            lineNumber: 524,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 523,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83E\\uDD14 为什么本地开发获取到127.0.0.1？\",\n                        size: \"small\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            direction: \"vertical\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            style: {\n                                                color: \"#fa8c16\"\n                                            },\n                                            children: \"\\uD83C\\uDFE0 本地开发环境：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 551,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"浏览器 → localhost:3000 → 后端API，所有请求都来自本机，所以IP是127.0.0.1\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 552,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 550,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            style: {\n                                                color: \"#52c41a\"\n                                            },\n                                            children: \"\\uD83C\\uDF0D 生产环境：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 556,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"用户浏览器 → CDN/负载均衡 → Web服务器 → 后端API，能获取到真实公网IP\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 557,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 555,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            style: {\n                                                color: \"#1890ff\"\n                                            },\n                                            children: \"\\uD83C\\uDFAD 模拟解决方案：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 561,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"中间件已配置在开发环境使用模拟公网IP (**************) 进行测试\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 562,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 560,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            style: {\n                                                color: \"#722ed1\"\n                                            },\n                                            children: \"\\uD83E\\uDDEA 真实IP对比：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 566,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: '点击\"获取真实公网IP\"按钮，对比你的真实公网IP和后端获取的IP'\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 567,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 565,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                            lineNumber: 549,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 548,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83C\\uDF0D 当前环境信息\",\n                        size: \"small\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            direction: \"vertical\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    children: [\n                                        \"浏览器 User-Agent: \",\n                                         true ? navigator.userAgent : 0\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 575,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    children: [\n                                        \"当前时间: \",\n                                        new Date().toISOString()\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 576,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    children: [\n                                        \"页面URL: \",\n                                         true ? window.location.href : 0\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 577,\n                                    columnNumber: 13\n                                }, this),\n                                 true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    style: {\n                                        color: window.location.hostname.includes(\"ngrok\") || window.location.hostname.includes(\"tunnel\") ? \"#52c41a\" : \"#fa8c16\",\n                                        fontWeight: \"bold\"\n                                    },\n                                    children: [\n                                        \"访问方式: \",\n                                        window.location.hostname.includes(\"ngrok\") || window.location.hostname.includes(\"tunnel\") ? \"\\uD83C\\uDF10 内网穿透访问 (可获取真实IP)\" : \"\\uD83C\\uDFE0 本地访问 (模拟IP)\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 579,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                            lineNumber: 574,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 573,\n                        columnNumber: 9\n                    }, this),\n                     true && !window.location.hostname.includes(\"ngrok\") && !window.location.hostname.includes(\"tunnel\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83D\\uDE80 想要测试真实IP获取？\",\n                        size: \"small\",\n                        style: {\n                            borderColor: \"#52c41a\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            direction: \"vertical\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    strong: true,\n                                    style: {\n                                        color: \"#52c41a\"\n                                    },\n                                    children: \"使用内网穿透获取真实IP：\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 597,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        background: \"#f6ffed\",\n                                        padding: \"12px\",\n                                        borderRadius: \"6px\",\n                                        border: \"1px solid #b7eb8f\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            code: true,\n                                            children: \"1. 安装ngrok: npm install -g ngrok\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 599,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 599,\n                                            columnNumber: 67\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            code: true,\n                                            children: \"2. 穿透前端: ngrok http 3000\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 600,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 600,\n                                            columnNumber: 59\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            code: true,\n                                            children: \"3. 访问ngrok提供的公网地址\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 601,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 601,\n                                            columnNumber: 52\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            code: true,\n                                            children: \"4. 重新测试IP获取功能\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 602,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 598,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    type: \"secondary\",\n                                    children: \"通过内网穿透，你可以模拟真实的生产环境，获取到真实的公网IP地址！\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 604,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                            lineNumber: 596,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 595,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                lineNumber: 423,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n        lineNumber: 415,\n        columnNumber: 5\n    }, this);\n}\n_s(TestIPPage, \"UKdI/aIOtKUiZXV4GhGqcrMqaTI=\");\n_c = TestIPPage;\nvar _c;\n$RefreshReg$(_c, \"TestIPPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/test-ip/page.tsx\n"));

/***/ })

});