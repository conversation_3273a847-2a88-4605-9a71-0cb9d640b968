{"version": 3, "file": "unified-response.interceptor.js", "sourceRoot": "", "sources": ["../../../src/common/response/unified-response.interceptor.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAsIA,kCAuBC;AAMD,4CA4BC;AA/LD,2CAKwB;AAExB,8CAAqC;AAErC,yEAAqF;AACrF,+BAAoC;AAO7B,IAAM,0BAA0B,GAAhC,MAAM,0BAA0B;IACR;IAA7B,YAA6B,eAAuC;QAAvC,oBAAe,GAAf,eAAe,CAAwB;IAAG,CAAC;IAExE,SAAS,CAAC,OAAyB,EAAE,IAAiB;QACpD,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,UAAU,EAAW,CAAC;QAC7D,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,GAAG,IAAI,EAAE,CAAC;QAG/C,MAAM,SAAS,GAAG,OAAO,CAAC,OAAO,CAAC,cAAc,CAAW,IAAI,IAAA,SAAM,GAAE,CAAC;QAGxE,MAAM,SAAS,GAAG,OAAO,CAAC,WAAW,CAAW,CAAC;QACjD,MAAM,aAAa,GAAG,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC;QAGrE,MAAM,YAAY,GAAG;YACnB,iBAAiB;YACjB,SAAS;YACT,UAAU;YACV,UAAU;YACV,WAAW;SACZ,CAAC;QAEF,IAAI,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC;YACjE,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC;QACvB,CAAC;QAED,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,CACvB,IAAA,eAAG,EAAC,IAAI,CAAC,EAAE;YAET,IAAI,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,CAAC;gBACjC,OAAO,IAAI,CAAC;YACd,CAAC;YAGD,IAAI,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC;gBAChC,OAAO,IAAI,CAAC,eAAe,CAAC,MAAM,CAChC,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,GAAG,EACR,IAAI,CAAC,IAAI,EACT;oBACE,IAAI;oBACJ,aAAa;oBACb,SAAS;oBACT,UAAU,EAAE,4BAA4B;iBACzC,CACF,CAAC;YACJ,CAAC;YAGD,OAAO,IAAI,CAAC,eAAe,CAAC,OAAO,CACjC,IAAI,EACJ,MAAM,EACN;gBACE,IAAI;gBACJ,aAAa;gBACb,SAAS;gBACT,UAAU,EAAE,4BAA4B;aACzC,CACF,CAAC;QACJ,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;IAMO,iBAAiB,CAAC,IAAS;QACjC,OAAO,CACL,IAAI;YACJ,OAAO,IAAI,KAAK,QAAQ;YACxB,MAAM,IAAI,IAAI;YACd,KAAK,IAAI,IAAI;YACb,MAAM,IAAI,IAAI;YACd,WAAW,IAAI,IAAI,CACpB,CAAC;IACJ,CAAC;IAMO,gBAAgB,CAAC,IAAS;QAChC,OAAO,CACL,IAAI;YACJ,OAAO,IAAI,KAAK,QAAQ;YACxB,MAAM,IAAI,IAAI;YACd,KAAK,IAAI,IAAI;YACb,MAAM,IAAI,IAAI;YACd,CAAC,CAAC,WAAW,IAAI,IAAI,CAAC,CACvB,CAAC;IACJ,CAAC;CACF,CAAA;AA7FY,gEAA0B;qCAA1B,0BAA0B;IADtC,IAAA,mBAAU,GAAE;qCAEmC,iDAAsB;GADzD,0BAA0B,CA6FtC;AAOM,IAAM,qBAAqB,GAA3B,MAAM,qBAAqB;IAChC,GAAG,CAAC,GAAY,EAAE,GAAQ,EAAE,IAAgB;QAC1C,GAAG,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAG9B,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE,CAAC;YACjC,GAAG,CAAC,OAAO,CAAC,cAAc,CAAC,GAAG,IAAA,SAAM,GAAE,CAAC;QACzC,CAAC;QAED,IAAI,EAAE,CAAC;IACT,CAAC;CACF,CAAA;AAXY,sDAAqB;gCAArB,qBAAqB;IADjC,IAAA,mBAAU,GAAE;GACA,qBAAqB,CAWjC;AAMD,SAAgB,WAAW,CAAC,OAAgB;IAC1C,OAAO,UAAU,MAAW,EAAE,WAAmB,EAAE,UAA8B;QAC/E,MAAM,cAAc,GAAG,UAAU,CAAC,KAAK,CAAC;QAExC,UAAU,CAAC,KAAK,GAAG,KAAK,WAAW,GAAG,IAAW;YAC/C,MAAM,MAAM,GAAG,MAAM,cAAc,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YAGtD,IAAI,MAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,MAAM,IAAI,MAAM,EAAE,CAAC;gBAC7D,OAAO,MAAM,CAAC;YAChB,CAAC;YAGD,IAAI,IAAI,CAAC,eAAe,IAAI,OAAO,IAAI,CAAC,eAAe,CAAC,OAAO,KAAK,UAAU,EAAE,CAAC;gBAC/E,OAAO,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YACvD,CAAC;YAGD,OAAO,MAAM,CAAC;QAChB,CAAC,CAAC;QAEF,OAAO,UAAU,CAAC;IACpB,CAAC,CAAC;AACJ,CAAC;AAMD,SAAgB,gBAAgB,CAAC,OAAgB,EAAE,IAAa;IAC9D,OAAO,UAAU,MAAW,EAAE,WAAmB,EAAE,UAA8B;QAC/E,MAAM,cAAc,GAAG,UAAU,CAAC,KAAK,CAAC;QAExC,UAAU,CAAC,KAAK,GAAG,KAAK,WAAW,GAAG,IAAW;YAC/C,IAAI,CAAC;gBACH,OAAO,MAAM,cAAc,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YAChD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBAEf,IAAI,IAAI,CAAC,eAAe,IAAI,OAAO,IAAI,CAAC,eAAe,CAAC,KAAK,KAAK,UAAU,EAAE,CAAC;oBAC7E,OAAO,IAAI,CAAC,eAAe,CAAC,KAAK,CAC/B,OAAO,IAAI,KAAK,CAAC,OAAO,EACxB,IAAI,EACJ,IAAI,EACJ;wBACE,UAAU,EAAE,MAAM,CAAC,WAAW,CAAC,IAAI;wBACnC,SAAS,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;qBACrE,CACF,CAAC;gBACJ,CAAC;gBAGD,MAAM,KAAK,CAAC;YACd,CAAC;QACH,CAAC,CAAC;QAEF,OAAO,UAAU,CAAC;IACpB,CAAC,CAAC;AACJ,CAAC"}