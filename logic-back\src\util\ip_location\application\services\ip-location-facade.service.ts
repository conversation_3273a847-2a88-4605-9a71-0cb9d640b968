import { Injectable } from '@nestjs/common';
import { LoggerService } from '../../../../common/logger/logger.service';

// 应用服务 - 新增导入
import { IpLocationApplicationService } from './ip-location-application.service';

// 请求和响应DTO
import { IpQueryRequestDto } from '../dto/requests/ip-query.request.dto';
import { RiskCheckRequestDto } from '../dto/requests/risk-check.request.dto';
import { TrustLocationRequestDto } from '../dto/requests/trust-location.request.dto';

// 领域服务 - 仅用于测试功能
import { IpLocationDomainService } from '../../domain/services/ip-location-domain.service';

/**
 * IP地理位置门面服务
 * 基于DDD架构提供统一的业务接口
 * 作为系统对外的统一入口，内部委托给应用服务处理复杂业务逻辑
 */
@Injectable()
export class IpLocationFacadeService {
  constructor(
    // 应用服务 - 处理所有业务逻辑
    private readonly applicationService: IpLocationApplicationService,

    // 领域服务 - 仅用于测试功能
    private readonly domainService: IpLocationDomainService,

    // 基础服务
    private readonly logger: LoggerService,
  ) {}

  /**
   * 查询IP地理位置信息
   * @param ip IP地址字符串
   * @param includeRisk 是否包含风险评估
   * @returns 统一格式的位置信息响应
   */
  async getLocationByIP(ip: string, includeRisk: boolean = false) {
    const startTime = Date.now();

    try {
      // 委托给应用服务处理业务逻辑
      const request: IpQueryRequestDto = { ip, includeRisk };
      const result = await this.applicationService.queryIpLocation(request);

      return this.createSuccessResponse(
        result,
        '查询成功',
        Date.now() - startTime
      );
    } catch (error) {
      this.logger.error(`IP位置查询失败: ${ip}`, error, 'IpLocationFacadeService');

      return this.createErrorResponse(
        error.message || '查询失败',
        Date.now() - startTime
      );
    }
  }

  /**
   * 评估登录风险
   * @param userId 用户ID
   * @param ip IP地址
   * @param userAgent 用户代理
   * @param sessionId 会话ID
   * @returns 统一格式的风险评估响应
   */
  async assessLoginRisk(
    userId: number,
    ip: string,
    userAgent?: string,
    sessionId?: string
  ) {
    const startTime = Date.now();

    try {
      // 委托给应用服务处理复杂的风险评估逻辑
      const request: RiskCheckRequestDto = {
        userId,
        ipAddress: ip,
        userAgent,
        sessionId
      };

      const result = await this.applicationService.checkLoginRisk(request);

      return this.createSuccessResponse(
        result,
        '风险评估完成',
        Date.now() - startTime
      );
    } catch (error) {
      this.logger.error(`登录风险评估失败: ${userId}-${ip}`, error, 'IpLocationFacadeService');

      return this.createErrorResponse(
        error.message || '风险评估失败',
        Date.now() - startTime
      );
    }
  }

  /**
   * 获取用户位置统计
   * @param userId 用户ID
   * @param days 统计天数
   * @returns 统一格式的位置统计响应
   */
  async getUserLocationStats(
    userId: number,
    days: number = 30
  ) {
    const startTime = Date.now();

    try {
      // 委托给应用服务处理统计逻辑
      const result = await this.applicationService.getUserLocationStatistics(userId, days);

      return this.createSuccessResponse(
        result,
        '统计查询成功',
        Date.now() - startTime
      );
    } catch (error) {
      this.logger.error(`用户位置统计失败: ${userId}`, error, 'IpLocationFacadeService');

      return this.createErrorResponse(
        error.message || '统计查询失败',
        Date.now() - startTime
      );
    }
  }



  /**
   * 设置可信位置
   * @param userId 用户ID
   * @param province 省份
   * @param city 城市
   * @param reason 设置原因
   * @returns 统一格式的设置结果响应
   */
  async setTrustedLocation(
    userId: number,
    province: string,
    city: string,
    reason: string = '用户主动设置'
  ) {
    const startTime = Date.now();

    try {
      // 委托给应用服务处理
      const request: TrustLocationRequestDto = {
        province,
        city,
        reason
      };

      await this.applicationService.setTrustedLocation(userId, request);

      return this.createSuccessResponse(
        { userId, province, city },
        '可信位置设置成功',
        Date.now() - startTime
      );
    } catch (error) {
      this.logger.error(`设置可信位置失败: ${userId}-${province}-${city}`, error, 'IpLocationFacadeService');

      return this.createErrorResponse(
        error.message || '设置失败',
        Date.now() - startTime
      );
    }
  }

  /**
   * 更新用户常用位置
   * @param userId 用户ID
   * @param ip IP地址
   * @returns 统一格式的更新结果响应
   */
  async updateUserCommonLocation(
    userId: number,
    ip: string
  ) {
    const startTime = Date.now();

    try {
      // 先获取IP位置信息
      const request: IpQueryRequestDto = { ip, includeRisk: false };
      const locationInfo = await this.applicationService.queryIpLocation(request);

      // 注意：这里需要转换类型，实际项目中应该统一DTO类型
      // 暂时跳过更新，只返回位置信息
      // await this.applicationService.updateUserCommonLocation(userId, locationInfo);

      return this.createSuccessResponse(
        { userId, ip, location: locationInfo },
        '常用位置更新成功',
        Date.now() - startTime
      );
    } catch (error) {
      this.logger.error(`更新常用位置失败: ${userId}-${ip}`, error, 'IpLocationFacadeService');

      return this.createErrorResponse(
        error.message || '更新失败',
        Date.now() - startTime
      );
    }
  }

  /**
   * 测试IP解析功能
   * @param testIp 测试IP
   * @returns 统一格式的测试结果响应
   */
  async testIpResolution(testIp: string = '*******') {
    const startTime = Date.now();

    try {
      const result = await this.domainService.testResolution(testIp);

      return this.createSuccessResponse(
        result,
        result.success ? '测试成功' : '测试失败',
        Date.now() - startTime
      );
    } catch (error) {
      this.logger.error(`IP解析测试失败: ${testIp}`, error, 'IpLocationFacadeService');

      return this.createErrorResponse(
        error.message || '测试失败',
        Date.now() - startTime
      );
    }
  }

  // ==================== 私有辅助方法 ====================

  /**
   * 创建成功响应
   * @param data 响应数据
   * @param message 响应消息
   * @param executionTime 执行时间
   * @param fromCache 是否来自缓存
   * @returns 统一格式的成功响应
   */
  private createSuccessResponse<T>(
    data: T,
    message: string = '操作成功',
    executionTime?: number,
    fromCache?: boolean
  ) {
    return {
      success: true,
      data,
      message,
      timestamp: new Date().toISOString(),
      executionTime: executionTime ? `${executionTime}ms` : undefined,
      fromCache
    };
  }

  /**
   * 创建错误响应
   * @param error 错误信息
   * @param executionTime 执行时间
   * @returns 统一格式的错误响应
   */
  private createErrorResponse(
    error: string,
    executionTime?: number
  ) {
    return {
      success: false,
      error,
      data: null,
      timestamp: new Date().toISOString(),
      executionTime: executionTime ? `${executionTime}ms` : undefined
    };
  }


}
