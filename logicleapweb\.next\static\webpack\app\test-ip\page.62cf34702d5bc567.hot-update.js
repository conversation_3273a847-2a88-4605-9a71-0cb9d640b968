"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/test-ip/page",{

/***/ "(app-pages-browser)/./app/test-ip/page.tsx":
/*!******************************!*\
  !*** ./app/test-ip/page.tsx ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ TestIPPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Divider,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/typography/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Divider,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/space/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Divider,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/card/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Divider,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Divider,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/divider/index.js\");\n/* harmony import */ var _lib_request__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/request */ \"(app-pages-browser)/./lib/request.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst { Title, Text, Paragraph } = _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_3__[\"default\"];\nfunction TestIPPage() {\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [result, setResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [realPublicIP, setRealPublicIP] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 测试获取当前IP位置\n    const testCurrentIP = async ()=>{\n        setLoading(true);\n        try {\n            console.log(\"\\uD83E\\uDDEA [Frontend] 开始测试当前IP获取...\");\n            const response = await _lib_request__WEBPACK_IMPORTED_MODULE_2__[\"default\"].get(\"/api/v1/ip-location/current\");\n            console.log(\"\\uD83D\\uDCE5 [Frontend] 收到响应:\", response);\n            setResult({\n                type: \"current-ip\",\n                data: response.data,\n                timestamp: new Date().toISOString()\n            });\n        } catch (error) {\n            var _error_response;\n            console.error(\"❌ [Frontend] 请求失败:\", error);\n            setResult({\n                type: \"error\",\n                error: error.message,\n                response: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data,\n                timestamp: new Date().toISOString()\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 测试查询指定IP\n    const testQueryIP = async ()=>{\n        setLoading(true);\n        try {\n            console.log(\"\\uD83E\\uDDEA [Frontend] 开始测试IP查询...\");\n            const testIP = \"*******\"; // Google DNS\n            const response = await _lib_request__WEBPACK_IMPORTED_MODULE_2__[\"default\"].get(\"/api/v1/ip-location/query?ip=\".concat(testIP, \"&includeRisk=false\"));\n            console.log(\"\\uD83D\\uDCE5 [Frontend] 收到响应:\", response);\n            setResult({\n                type: \"query-ip\",\n                testIP,\n                data: response.data,\n                timestamp: new Date().toISOString()\n            });\n        } catch (error) {\n            var _error_response;\n            console.error(\"❌ [Frontend] 请求失败:\", error);\n            setResult({\n                type: \"error\",\n                error: error.message,\n                response: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data,\n                timestamp: new Date().toISOString()\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 测试登录接口（观察IP日志）\n    const testLoginIP = async ()=>{\n        setLoading(true);\n        try {\n            console.log(\"\\uD83E\\uDDEA [Frontend] 开始测试登录IP获取...\");\n            // 这里故意使用错误的登录信息，只是为了触发IP获取逻辑\n            const response = await _lib_request__WEBPACK_IMPORTED_MODULE_2__[\"default\"].post(\"/api/user-auth/password\", {\n                phone: \"12345678910\",\n                password: \"123456\"\n            });\n            console.log(\"\\uD83D\\uDCE5 [Frontend] 登录响应:\", response);\n            setResult({\n                type: \"login-test\",\n                data: response.data,\n                timestamp: new Date().toISOString()\n            });\n        } catch (error) {\n            var _error_response;\n            console.error(\"\\uD83D\\uDCDD [Frontend] 登录测试完成 (预期失败):\", error);\n            setResult({\n                type: \"login-test\",\n                error: error.message,\n                response: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data,\n                note: \"这是预期的失败，主要用于观察IP获取日志\",\n                timestamp: new Date().toISOString()\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"24px\",\n            maxWidth: \"1200px\",\n            margin: \"0 auto\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                level: 2,\n                children: \"\\uD83E\\uDDEA IP地址获取测试页面\"\n            }, void 0, false, {\n                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                lineNumber: 107,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                children: \"这个页面用于测试前端到后端的IP地址传递和获取功能。 请打开浏览器开发者工具的控制台，以及后端服务器的日志，观察IP获取过程。\"\n            }, void 0, false, {\n                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                lineNumber: 109,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                direction: \"vertical\",\n                size: \"large\",\n                style: {\n                    width: \"100%\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83C\\uDFAF 测试功能\",\n                        size: \"small\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            wrap: true,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    type: \"primary\",\n                                    loading: loading,\n                                    onClick: testCurrentIP,\n                                    children: \"测试获取当前IP位置\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    loading: loading,\n                                    onClick: testQueryIP,\n                                    children: \"测试查询指定IP (*******)\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    loading: loading,\n                                    onClick: testLoginIP,\n                                    danger: true,\n                                    children: \"测试登录IP获取 (会失败)\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, this),\n                    result && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83D\\uDCCA 测试结果\",\n                        size: \"small\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                background: \"#f5f5f5\",\n                                padding: \"16px\",\n                                borderRadius: \"6px\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                style: {\n                                    margin: 0,\n                                    fontSize: \"12px\",\n                                    lineHeight: \"1.4\"\n                                },\n                                children: JSON.stringify(result, null, 2)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83D\\uDCCB 观察要点\",\n                        size: \"small\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            direction: \"vertical\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            children: \"\\uD83C\\uDF10 前端中间件日志：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"查看浏览器控制台，观察 [Middleware] 标记的日志\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            children: \"\\uD83D\\uDDA5️ 后端IP提取日志：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"查看后端控制台，观察 [Backend] 标记的日志\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            children: \"\\uD83D\\uDD10 登录日志：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"查看后端控制台，观察 [LoginLog] 标记的日志\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            children: \"\\uD83D\\uDD0D 重点观察：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"IP地址是否从前端正确传递到后端，以及各个环节的IP获取情况\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83C\\uDF0D 当前环境信息\",\n                        size: \"small\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            direction: \"vertical\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    children: [\n                                        \"浏览器 User-Agent: \",\n                                         true ? navigator.userAgent : 0\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    children: [\n                                        \"当前时间: \",\n                                        new Date().toISOString()\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    children: [\n                                        \"页面URL: \",\n                                         true ? window.location.href : 0\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                            lineNumber: 184,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                lineNumber: 114,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n        lineNumber: 106,\n        columnNumber: 5\n    }, this);\n}\n_s(TestIPPage, \"X1Msb4W44QQRH4Yvzh/LKJZfd50=\");\n_c = TestIPPage;\nvar _c;\n$RefreshReg$(_c, \"TestIPPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/test-ip/page.tsx\n"));

/***/ })

});