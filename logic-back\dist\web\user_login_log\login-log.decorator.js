"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LoginLogHelper = exports.LoginLogInterceptor = exports.ClientInfo = exports.LoginLog = exports.LoginLogType = exports.LOGIN_LOG_KEY = void 0;
const common_1 = require("@nestjs/common");
const login_logger_util_1 = require("./login-logger.util");
const user_login_log_entity_1 = require("../../util/database/mysql/user_login_log/entities/user_login_log.entity");
Object.defineProperty(exports, "LoginLogType", { enumerable: true, get: function () { return user_login_log_entity_1.LoginType; } });
exports.LOGIN_LOG_KEY = 'login_log';
const LoginLog = (config) => (0, common_1.SetMetadata)(exports.LOGIN_LOG_KEY, config);
exports.LoginLog = LoginLog;
exports.ClientInfo = (0, common_1.createParamDecorator)((data, ctx) => {
    const request = ctx.switchToHttp().getRequest();
    const getClientIp = (req) => {
        const xForwardedFor = req.headers['x-forwarded-for'];
        const xRealIp = req.headers['x-real-ip'];
        const xClientIp = req.headers['x-client-ip'];
        const connectionIp = req.connection?.remoteAddress;
        const socketIp = req.socket?.remoteAddress;
        const connectionSocketIp = req.connection?.socket?.remoteAddress;
        console.log('🔐 [LoginLog] IP地址获取详情:', {
            请求路径: req.url,
            请求方法: req.method,
            请求头IP信息: {
                'x-forwarded-for': xForwardedFor,
                'x-real-ip': xRealIp,
                'x-client-ip': xClientIp,
            },
            连接IP信息: {
                'connection.remoteAddress': connectionIp,
                'socket.remoteAddress': socketIp,
                'connection.socket.remoteAddress': connectionSocketIp,
            },
            时间戳: new Date().toISOString()
        });
        const headerIp = xForwardedFor || xRealIp || xClientIp || connectionIp || socketIp || connectionSocketIp;
        if (headerIp && headerIp.includes(',')) {
            const finalIp = headerIp.split(',')[0].trim();
            console.log('✅ [LoginLog] IP地址提取成功:', {
                原始IP: headerIp,
                最终IP: finalIp,
                来源: xForwardedFor ? 'x-forwarded-for' : xRealIp ? 'x-real-ip' : xClientIp ? 'x-client-ip' : '连接IP',
                时间戳: new Date().toISOString()
            });
            return finalIp;
        }
        const finalIp = headerIp || '127.0.0.1';
        console.log('✅ [LoginLog] IP地址确定:', {
            最终IP: finalIp,
            是否为默认: finalIp === '127.0.0.1',
            时间戳: new Date().toISOString()
        });
        return finalIp;
    };
    const clientIp = getClientIp(request);
    const userAgent = request.headers['user-agent'] || '未知设备';
    return {
        clientIp,
        userAgent,
        deviceInfo: login_logger_util_1.LoginLoggerUtil.getDeviceInfo(userAgent),
        request
    };
});
const common_2 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const rxjs_1 = require("rxjs");
const operators_1 = require("rxjs/operators");
let LoginLogInterceptor = class LoginLogInterceptor {
    reflector;
    constructor(reflector) {
        this.reflector = reflector;
    }
    intercept(context, next) {
        const loginLogConfig = this.reflector.get(exports.LOGIN_LOG_KEY, context.getHandler());
        if (!loginLogConfig) {
            return next.handle();
        }
        const request = context.switchToHttp().getRequest();
        const clientIp = this.getClientIp(request);
        const userAgent = request.headers['user-agent'] || '未知设备';
        const deviceInfo = login_logger_util_1.LoginLoggerUtil.getDeviceInfo(userAgent);
        return next.handle().pipe((0, operators_1.tap)(async (result) => {
            try {
                const userId = loginLogConfig.extractUserId
                    ? loginLogConfig.extractUserId(result, request.body)
                    : result?.data?.userInfo?.id || result?.userInfo?.id;
                const sessionId = loginLogConfig.extractSessionId
                    ? loginLogConfig.extractSessionId(result, request.body)
                    : `${loginLogConfig.loginType}-${userId}-${Date.now()}`;
                if (userId) {
                    await login_logger_util_1.LoginLoggerUtil.logSuccessLogin({
                        userId,
                        loginType: loginLogConfig.loginType,
                        clientIp,
                        userAgent,
                        deviceInfo,
                        sessionId
                    });
                }
            }
            catch (error) {
                console.error('记录登录成功日志失败:', error);
            }
        }), (0, operators_1.catchError)(async (error) => {
            try {
                const failReason = loginLogConfig.extractFailReason
                    ? loginLogConfig.extractFailReason(error)
                    : error.message || '登录失败';
                const userId = loginLogConfig.extractUserId
                    ? loginLogConfig.extractUserId(null, request.body)
                    : undefined;
                await login_logger_util_1.LoginLoggerUtil.logFailedLogin({
                    userId,
                    loginType: loginLogConfig.loginType,
                    clientIp,
                    userAgent,
                    failReason
                });
            }
            catch (logError) {
                console.error('记录登录失败日志失败:', logError);
            }
            return (0, rxjs_1.throwError)(error);
        }));
    }
    getClientIp(request) {
        const headerIp = request.headers['x-forwarded-for'] ||
            request.headers['x-real-ip'] ||
            request.headers['x-client-ip'] ||
            request.connection?.remoteAddress ||
            request.socket?.remoteAddress ||
            request.connection?.socket?.remoteAddress;
        if (headerIp && headerIp.includes(',')) {
            return headerIp.split(',')[0].trim();
        }
        return headerIp || '127.0.0.1';
    }
};
exports.LoginLogInterceptor = LoginLogInterceptor;
exports.LoginLogInterceptor = LoginLogInterceptor = __decorate([
    (0, common_2.Injectable)(),
    __metadata("design:paramtypes", [core_1.Reflector])
], LoginLogInterceptor);
class LoginLogHelper {
    static async logSuccess(userId, loginType, clientInfo, sessionId) {
        await login_logger_util_1.LoginLoggerUtil.logSuccessLogin({
            userId,
            loginType,
            clientIp: clientInfo.clientIp,
            userAgent: clientInfo.userAgent,
            deviceInfo: clientInfo.deviceInfo,
            sessionId: sessionId || `${loginType}-${userId}-${Date.now()}`
        });
    }
    static async logFailure(loginType, clientInfo, failReason, userId) {
        await login_logger_util_1.LoginLoggerUtil.logFailedLogin({
            userId,
            loginType,
            clientIp: clientInfo.clientIp,
            userAgent: clientInfo.userAgent,
            failReason
        });
    }
    static async logRoleSwitch(userId, clientInfo, fromRole, toRole) {
        console.warn('⚠️ logRoleSwitch 方法已废弃，请使用 logLogout + logSuccess 组合');
        console.log('🔄 角色切换日志（废弃方法）:', {
            userId,
            fromRole,
            toRole,
            clientIp: clientInfo.clientIp,
            userAgent: clientInfo.userAgent?.substring(0, 50) + '...'
        });
        try {
            await LoginLogHelper.logSuccess(userId, user_login_log_entity_1.LoginType.REFRESH, clientInfo, `role-switch-${userId}-${Date.now()}`);
            console.log('✅ 角色切换日志记录成功（废弃方法）');
        }
        catch (error) {
            console.error('❌ 角色切换日志记录失败:', error);
            console.error('❌ 错误详情:', error.stack);
        }
    }
    static async logLogout(userId, clientInfo, sessionId, reason, logoutType) {
        await login_logger_util_1.LoginLoggerUtil.logLogout({
            userId,
            clientIp: clientInfo.clientIp,
            sessionId: sessionId,
            reason: reason || '用户主动登出',
            logoutType: logoutType || 'active'
        });
    }
}
exports.LoginLogHelper = LoginLogHelper;
//# sourceMappingURL=login-log.decorator.js.map