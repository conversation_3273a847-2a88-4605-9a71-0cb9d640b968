import { Global, Module } from '@nestjs/common';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { HttpResponseResultService } from './http_response_result.service';
import { HttpResponseResultController } from './http_response_result.controller';
import { ResponseTransformInterceptor } from './response-transform.interceptor';
import { LoggerModule } from '../../common/logger/logger.module';

@Global()
@Module({
  imports: [LoggerModule], // 导入日志模块
  controllers: [HttpResponseResultController],
  providers: [
    HttpResponseResultService,
    {
      provide: APP_INTERCEPTOR,
      useClass: ResponseTransformInterceptor,
    },
  ],
  exports: [HttpResponseResultService],
})
export class HttpResponseResultModule {}
