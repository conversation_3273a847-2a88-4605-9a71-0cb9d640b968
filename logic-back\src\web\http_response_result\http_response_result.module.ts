import { Global, Module } from '@nestjs/common';
import { HttpResponseResultService } from './http_response_result.service';
import { HttpResponseResultController } from './http_response_result.controller';
import { LoggerModule } from '../../common/logger/logger.module';

@Global()
@Module({
  imports: [LoggerModule], // 导入日志模块
  controllers: [HttpResponseResultController],
  providers: [HttpResponseResultService],
  exports: [HttpResponseResultService],
})
export class HttpResponseResultModule {}
