"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HttpExceptionFilter = void 0;
const common_1 = require("@nestjs/common");
const http_response_result_service_1 = require("../http_response_result/http_response_result.service");
const logger_service_1 = require("../../common/logger/logger.service");
let HttpExceptionFilter = class HttpExceptionFilter {
    httpResponseResultService;
    loggerService;
    constructor(httpResponseResultService, loggerService) {
        this.httpResponseResultService = httpResponseResultService;
        this.loggerService = loggerService;
    }
    catch(exception, host) {
        const ctx = host.switchToHttp();
        const response = ctx.getResponse();
        const request = ctx.getRequest();
        const status = exception.getStatus();
        const exceptionResponse = exception.getResponse();
        let errorMessage = '服务器错误';
        let errorCode = status;
        let errorData = null;
        if (typeof exceptionResponse === 'object' && exceptionResponse !== null) {
            const exceptionObj = exceptionResponse;
            if (exceptionObj.msg) {
                errorMessage = exceptionObj.msg;
            }
            else if (exceptionObj.message) {
                errorMessage = exceptionObj.message;
            }
            if (exceptionObj.code) {
                errorCode = exceptionObj.code;
            }
            if (exceptionObj.data !== undefined) {
                errorData = exceptionObj.data;
            }
        }
        if (errorMessage === '服务器错误') {
            switch (status) {
                case common_1.HttpStatus.UNAUTHORIZED:
                    errorMessage = '请先登录后再访问';
                    break;
                case common_1.HttpStatus.FORBIDDEN:
                    errorMessage = '您没有权限执行此操作';
                    break;
                case common_1.HttpStatus.NOT_FOUND:
                    errorMessage = '请求的资源不存在';
                    break;
                case common_1.HttpStatus.BAD_REQUEST:
                    errorMessage = '请求参数错误';
                    break;
                case common_1.HttpStatus.INTERNAL_SERVER_ERROR:
                    errorMessage = '服务器内部错误';
                    break;
                default:
                    errorMessage = `请求错误 (${status})`;
            }
        }
        const logOptions = {
            path: request.url,
            requestId: request.headers['x-request-id'],
            context: 'HttpExceptionFilter',
            enableLog: true
        };
        const responseBody = this.httpResponseResultService.error(errorMessage, errorData, errorCode, logOptions);
        this.logExceptionDetails(exception, request, status, errorMessage);
        response
            .status(status)
            .json(responseBody);
    }
    logExceptionDetails(exception, request, status, errorMessage) {
        this.loggerService.error(`HTTP Exception: ${request.method} ${request.url} - ${errorMessage} (${status})`, exception.stack, 'HttpExceptionFilter');
        this.loggerService.error(JSON.stringify({
            type: 'Exception Details',
            url: request.url,
            method: request.method,
            statusCode: status,
            message: errorMessage,
            exceptionName: exception.name,
            userAgent: request.get('User-Agent'),
            ip: this.extractClientIP(request),
            requestId: request.headers['x-request-id'],
            timestamp: new Date().toISOString(),
            headers: {
                'content-type': request.get('Content-Type'),
                'authorization': request.get('Authorization') ? '[REDACTED]' : undefined,
                'x-forwarded-for': request.get('X-Forwarded-For'),
                'x-real-ip': request.get('X-Real-IP')
            },
            body: request.body ? this.sanitizeRequestBody(request.body) : undefined,
            query: request.query,
            params: request.params
        }), undefined, 'HttpExceptionFilter');
    }
    extractClientIP(request) {
        const forwarded = request.headers['x-forwarded-for'];
        const realIp = request.headers['x-real-ip'];
        const clientIp = request.socket?.remoteAddress;
        if (forwarded)
            return forwarded.split(',')[0].trim();
        if (realIp)
            return realIp;
        return clientIp?.replace('::ffff:', '') || '127.0.0.1';
    }
    sanitizeRequestBody(body) {
        if (!body || typeof body !== 'object')
            return body;
        const sensitiveFields = ['password', 'token', 'secret', 'key', 'authorization'];
        const sanitized = { ...body };
        for (const field of sensitiveFields) {
            if (sanitized[field]) {
                sanitized[field] = '[REDACTED]';
            }
        }
        return sanitized;
    }
};
exports.HttpExceptionFilter = HttpExceptionFilter;
exports.HttpExceptionFilter = HttpExceptionFilter = __decorate([
    (0, common_1.Catch)(common_1.HttpException),
    __metadata("design:paramtypes", [http_response_result_service_1.HttpResponseResultService,
        logger_service_1.LoggerService])
], HttpExceptionFilter);
//# sourceMappingURL=http-exception.filter.js.map