{"version": 3, "file": "unified-response.service.js", "sourceRoot": "", "sources": ["../../../src/common/response/unified-response.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAA4C;AAC5C,6DAAyD;AAyB5C,QAAA,cAAc,GAAG;IAC5B,OAAO,EAAE,GAAG;IACZ,WAAW,EAAE,GAAG;IAChB,YAAY,EAAE,GAAG;IACjB,SAAS,EAAE,GAAG;IACd,SAAS,EAAE,GAAG;IACd,cAAc,EAAE,GAAG;IACnB,cAAc,EAAE,IAAI;CACZ,CAAC;AAOJ,IAAM,sBAAsB,GAA5B,MAAM,sBAAsB;IACJ;IAA7B,YAA6B,aAA4B;QAA5B,kBAAa,GAAb,aAAa,CAAe;IAAG,CAAC;IAQ7D,OAAO,CACL,IAAQ,EACR,GAAG,GAAG,MAAM,EACZ,OAKC;QAED,MAAM,QAAQ,GAAuB;YACnC,IAAI,EAAE,sBAAc,CAAC,OAAO;YAC5B,GAAG;YACH,IAAI,EAAE,IAAS;YACf,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,IAAI,EAAE,OAAO,EAAE,IAAI;YACnB,aAAa,EAAE,OAAO,EAAE,aAAa,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,aAAa,IAAI,CAAC,CAAC,CAAC,SAAS;YAChF,SAAS,EAAE,OAAO,EAAE,SAAS;SAC9B,CAAC;QAGF,IAAI,OAAO,EAAE,UAAU,EAAE,CAAC;YACxB,IAAI,CAAC,aAAa,CAAC,GAAG,CACpB,YAAY,GAAG,MAAM,OAAO,CAAC,IAAI,IAAI,SAAS,EAAE,EAChD,OAAO,CAAC,UAAU,CACnB,CAAC;QACJ,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IASD,KAAK,CACH,GAAG,GAAG,MAAM,EACZ,IAAQ,EACR,IAAI,GAAG,sBAAc,CAAC,cAAc,EACpC,OAOC;QAED,MAAM,QAAQ,GAAuB;YACnC,IAAI;YACJ,GAAG;YACH,IAAI,EAAE,IAAS;YACf,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,IAAI,EAAE,OAAO,EAAE,IAAI;YACnB,aAAa,EAAE,OAAO,EAAE,aAAa,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,aAAa,IAAI,CAAC,CAAC,CAAC,SAAS;YAChF,SAAS,EAAE,OAAO,EAAE,SAAS;SAC9B,CAAC;QAGF,IAAI,OAAO,EAAE,UAAU,EAAE,CAAC;YACxB,IAAI,CAAC,aAAa,CAAC,KAAK,CACtB,UAAU,GAAG,MAAM,OAAO,CAAC,IAAI,IAAI,SAAS,YAAY,IAAI,EAAE,EAC9D,OAAO,EAAE,SAAS,EAAE,KAAK,EACzB,OAAO,CAAC,UAAU,CACnB,CAAC;YAGF,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,eAAe,EAAE,IAAI,CAAC,SAAS,CAAC;gBACvD,IAAI,EAAE,OAAO,CAAC,IAAI;gBAClB,IAAI;gBACJ,OAAO,EAAE,GAAG;gBACZ,IAAI;gBACJ,UAAU,EAAE,OAAO,CAAC,UAAU;gBAC9B,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,SAAS,EAAE,QAAQ,CAAC,SAAS;gBAC7B,SAAS,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC;oBAC7B,IAAI,EAAE,OAAO,CAAC,SAAS,CAAC,IAAI;oBAC5B,OAAO,EAAE,OAAO,CAAC,SAAS,CAAC,OAAO;oBAClC,KAAK,EAAE,OAAO,CAAC,SAAS,CAAC,KAAK;iBAC/B,CAAC,CAAC,CAAC,SAAS;aACd,CAAC,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;QAC1B,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAQD,aAAa,CACX,GAAW,EACX,IAAQ,EACR,OAKC;QAED,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,IAAI,EAAE,sBAAc,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;IACvE,CAAC;IAQD,UAAU,CACR,GAAG,GAAG,QAAQ,EACd,IAAQ,EACR,OAKC;QAED,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,IAAI,EAAE,sBAAc,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;IACpE,CAAC;IAQD,YAAY,CACV,GAAG,GAAG,UAAU,EAChB,IAAQ,EACR,OAKC;QAED,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,IAAI,EAAE,sBAAc,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;IACrE,CAAC;IAQD,SAAS,CACP,GAAG,GAAG,YAAY,EAClB,IAAQ,EACR,OAKC;QAED,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,IAAI,EAAE,sBAAc,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IAClE,CAAC;IAQD,QAAQ,CACN,GAAG,GAAG,UAAU,EAChB,IAAQ,EACR,OAKC;QAED,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,IAAI,EAAE,sBAAc,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IAClE,CAAC;IASD,MAAM,CACJ,IAAY,EACZ,GAAW,EACX,IAAQ,EACR,OAKC;QAED,MAAM,QAAQ,GAAuB;YACnC,IAAI;YACJ,GAAG;YACH,IAAI,EAAE,IAAS;YACf,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,IAAI,EAAE,OAAO,EAAE,IAAI;YACnB,aAAa,EAAE,OAAO,EAAE,aAAa,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,aAAa,IAAI,CAAC,CAAC,CAAC,SAAS;YAChF,SAAS,EAAE,OAAO,EAAE,SAAS;SAC9B,CAAC;QAGF,IAAI,OAAO,EAAE,UAAU,EAAE,CAAC;YACxB,IAAI,CAAC,aAAa,CAAC,GAAG,CACpB,oBAAoB,GAAG,YAAY,IAAI,MAAM,OAAO,CAAC,IAAI,IAAI,SAAS,EAAE,EACxE,OAAO,CAAC,UAAU,CACnB,CAAC;QACJ,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAMD,SAAS,CAAC,QAAyB;QACjC,OAAO,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO,CAAC;IAClD,CAAC;IAMD,OAAO,CAAC,QAAyB;QAC/B,OAAO,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO,CAAC;IAClD,CAAC;CACF,CAAA;AAvPY,wDAAsB;iCAAtB,sBAAsB;IADlC,IAAA,mBAAU,GAAE;qCAEiC,8BAAa;GAD9C,sBAAsB,CAuPlC"}