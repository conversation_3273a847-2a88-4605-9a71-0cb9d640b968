# 日志系统使用指南

## 概述

本项目使用基于 Winston 的增强日志系统，能够自动捕获和保存所有类型的日志输出，包括 `console.log`、`console.error` 等原生 JavaScript 控制台输出。

## 功能特性

### 🔄 自动Console重写
- 自动重写所有 `console.*` 方法
- 将所有控制台输出重定向到 Winston 日志系统
- 开发环境下仍保持原始控制台输出
- 支持所有console方法：log, error, warn, info, debug, trace, assert, dir, table, group, time, count等

### 📁 多文件分类存储
日志会自动分类保存到不同文件：
- `application-YYYY-MM-DD.log` - 所有日志
- `error-YYYY-MM-DD.log` - 错误日志
- `http-YYYY-MM-DD.log` - HTTP请求日志
- `database-YYYY-MM-DD.log` - 数据库操作日志
- `console-YYYY-MM-DD.log` - Console输出日志
- `exceptions-YYYY-MM-DD.log` - 未捕获异常
- `rejections-YYYY-MM-DD.log` - Promise拒绝

### 🔄 日志轮转
- 按日期自动轮转
- 自动压缩旧日志文件
- 最大文件大小：20MB
- 保留30天的日志文件

## 使用方法

### 1. 基本使用

#### 使用Console方法（推荐用于简单输出）
```typescript
// 这些都会被自动保存到日志文件
console.log('普通日志信息');
console.error('错误信息');
console.warn('警告信息');
console.info('信息日志');
console.debug('调试信息');

// 对象和数组也会被正确格式化
console.log('用户数据:', { name: '张三', age: 25 });
console.log('数组数据:', [1, 2, 3, 'test']);
```

#### 使用LoggerService（推荐用于业务日志）
```typescript
import { LoggerService } from './common/logger/logger.service';

@Injectable()
export class YourService {
  constructor(private readonly logger: LoggerService) {}

  someMethod() {
    // 基本日志方法
    this.logger.log('操作完成', 'YourService');
    this.logger.error('操作失败', error.stack, 'YourService');
    this.logger.warn('警告信息', 'YourService');
    this.logger.debug('调试信息', 'YourService');
  }
}
```

### 2. 专用日志方法

#### HTTP请求日志
```typescript
this.logger.logHttpRequest(req, res, responseTime);
```

#### 数据库操作日志
```typescript
this.logger.logDatabase('SELECT', 'users', { id: 1 });
this.logger.logDatabase('INSERT', 'users', userData, error);
```

#### 支付相关日志
```typescript
this.logger.logPayment('create_order', orderData);
this.logger.logPayment('payment_failed', orderData, error);
```

#### 认证相关日志
```typescript
this.logger.logAuth('login', userId, { method: 'password' });
this.logger.logAuth('login_failed', userId, { method: 'password' }, error);
```

#### 业务逻辑日志
```typescript
this.logger.logBusiness('user', 'register', userData);
this.logger.logBusiness('order', 'create', orderData, error);
```

#### 性能日志
```typescript
this.logger.logPerformance('database_query', duration, { query: 'SELECT * FROM users' });
```

#### 安全日志
```typescript
this.logger.logSecurity('suspicious_login', { ip: '*************' }, 'high');
```

### 3. 高级Console方法

```typescript
// 对象详细输出
console.dir(complexObject);

// 表格输出
console.table([
  { name: '张三', age: 25 },
  { name: '李四', age: 30 }
]);

// 分组输出
console.group('用户操作');
console.log('步骤1: 验证用户');
console.log('步骤2: 更新数据');
console.groupEnd();

// 性能计时
console.time('操作耗时');
// ... 执行操作
console.timeEnd('操作耗时');

// 计数器
console.count('API调用次数');

// 断言
console.assert(condition, '条件不满足时的错误信息');

// 堆栈跟踪
console.trace('当前调用堆栈');
```

## 测试接口

系统提供了测试接口来验证日志功能：

### 测试所有日志类型
```
GET /api/v1/logger-test/all
```

### 测试异步日志
```
GET /api/v1/logger-test/async
```

### 测试日志性能
```
GET /api/v1/logger-test/performance
```

### 测试Console方法
```
GET /api/v1/logger-test/console-methods
```

### 自定义日志测试
```
POST /api/v1/logger-test/custom
{
  "message": "自定义测试消息",
  "level": "info",
  "context": "TestContext"
}
```

### 错误模拟测试
```
GET /api/v1/logger-test/error-simulation
```

## 日志格式

### 标准格式
```
2025-07-30 14:30:25.123 [INFO] [Context] 日志消息 {"额外数据": "值"}
```

### 错误格式（包含堆栈）
```
2025-07-30 14:30:25.123 [ERROR] [Context] 错误消息 {"error": "错误详情"}
Error: 错误消息
    at Function.method (/path/to/file.js:123:45)
    at ...
```

## 配置说明

### 环境变量
- `NODE_ENV=dev` 或 `NODE_ENV=development` - 开发环境，会同时输出到控制台
- 其他值 - 生产环境，只输出到日志文件

### 日志级别
- `debug` - 调试信息（最详细）
- `info` - 一般信息
- `warn` - 警告信息
- `error` - 错误信息（最重要）

## 最佳实践

1. **使用合适的日志级别**
   - 调试信息使用 `debug`
   - 正常业务流程使用 `info`
   - 潜在问题使用 `warn`
   - 错误情况使用 `error`

2. **提供上下文信息**
   ```typescript
   this.logger.log('用户登录成功', 'AuthService');
   ```

3. **记录关键业务操作**
   ```typescript
   this.logger.logBusiness('payment', 'process', { orderId, amount });
   ```

4. **错误日志包含堆栈信息**
   ```typescript
   this.logger.error('处理失败', error.stack, 'ServiceName');
   ```

5. **使用结构化数据**
   ```typescript
   console.log('用户操作', { 
     userId: 123, 
     action: 'update_profile', 
     timestamp: new Date().toISOString() 
   });
   ```

## 故障排除

### 日志文件未生成
1. 检查 `logs` 目录是否存在且有写入权限
2. 确认 LoggerModule 已正确导入到 AppModule
3. 检查 ConsoleOverrideService 是否正常启动

### Console输出未保存
1. 确认应用启动时没有错误
2. 检查 `console-YYYY-MM-DD.log` 文件
3. 验证 ConsoleOverrideService 是否正确重写了console方法

### 性能问题
1. 调整日志级别，减少不必要的debug日志
2. 检查日志文件大小和轮转配置
3. 考虑使用异步日志写入（如需要）

## 注意事项

1. **开发环境**：Console输出会同时显示在控制台和保存到文件
2. **生产环境**：Console输出只保存到文件，不显示在控制台
3. **异常处理**：未捕获的异常和Promise拒绝会自动记录到专门的日志文件
4. **性能影响**：日志系统经过优化，对性能影响很小
5. **文件权限**：确保应用有权限在 `logs` 目录创建和写入文件
