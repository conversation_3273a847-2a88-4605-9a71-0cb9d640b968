import { LoggerService } from '../../../../common/logger/logger.service';
import { IpLocationApplicationService } from './ip-location-application.service';
import { IpLocationDomainService } from '../../domain/services/ip-location-domain.service';
export declare class IpLocationFacadeService {
    private readonly applicationService;
    private readonly domainService;
    private readonly logger;
    constructor(applicationService: IpLocationApplicationService, domainService: IpLocationDomainService, logger: LoggerService);
    getLocationByIP(ip: string, includeRisk?: boolean): Promise<{
        success: boolean;
        data: import("../dto/responses/location-info.response.dto").LocationInfoResponseDto;
        message: string;
        timestamp: string;
        executionTime: string | undefined;
        fromCache: boolean | undefined;
    } | {
        success: boolean;
        error: string;
        data: null;
        timestamp: string;
        executionTime: string | undefined;
    }>;
    assessLoginRisk(userId: number, ip: string, userAgent?: string, sessionId?: string): Promise<{
        success: boolean;
        error: string;
        data: null;
        timestamp: string;
        executionTime: string | undefined;
    } | {
        success: boolean;
        data: import("../dto/responses/risk-assessment.response.dto").RiskAssessmentResponseDto;
        message: string;
        timestamp: string;
        executionTime: string | undefined;
        fromCache: boolean | undefined;
    }>;
    getUserLocationStats(userId: number, days?: number): Promise<{
        success: boolean;
        error: string;
        data: null;
        timestamp: string;
        executionTime: string | undefined;
    } | {
        success: boolean;
        data: import("../dto/responses/location-stats.response.dto").LocationStatsResponseDto;
        message: string;
        timestamp: string;
        executionTime: string | undefined;
        fromCache: boolean | undefined;
    }>;
    setTrustedLocation(userId: number, province: string, city: string, reason?: string): Promise<{
        success: boolean;
        error: string;
        data: null;
        timestamp: string;
        executionTime: string | undefined;
    } | {
        success: boolean;
        data: {
            userId: number;
            province: string;
            city: string;
        };
        message: string;
        timestamp: string;
        executionTime: string | undefined;
        fromCache: boolean | undefined;
    }>;
    updateUserCommonLocation(userId: number, ip: string): Promise<{
        success: boolean;
        error: string;
        data: null;
        timestamp: string;
        executionTime: string | undefined;
    } | {
        success: boolean;
        data: {
            userId: number;
            ip: string;
            location: import("../dto/responses/location-info.response.dto").LocationInfoResponseDto;
        };
        message: string;
        timestamp: string;
        executionTime: string | undefined;
        fromCache: boolean | undefined;
    }>;
    testIpResolution(testIp?: string): Promise<{
        success: boolean;
        error: string;
        data: null;
        timestamp: string;
        executionTime: string | undefined;
    } | {
        success: boolean;
        data: {
            success: boolean;
            ipAddress?: import("../../domain/value-objects/ip-address.vo").IpAddress;
            location?: import("../../domain/value-objects/geographic-location.vo").GeographicLocation;
            error?: string;
            performanceMs: number;
        };
        message: string;
        timestamp: string;
        executionTime: string | undefined;
        fromCache: boolean | undefined;
    }>;
    private createSuccessResponse;
    private createErrorResponse;
}
