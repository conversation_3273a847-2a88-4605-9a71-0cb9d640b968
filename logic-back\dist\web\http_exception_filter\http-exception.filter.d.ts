import { ExceptionFilter, ArgumentsHost, HttpException } from '@nestjs/common';
import { HttpResponseResultService } from '../http_response_result/http_response_result.service';
import { LoggerService } from '../../common/logger/logger.service';
export declare class HttpExceptionFilter implements ExceptionFilter {
    private readonly httpResponseResultService;
    private readonly loggerService;
    constructor(httpResponseResultService: HttpResponseResultService, loggerService: LoggerService);
    catch(exception: HttpException, host: ArgumentsHost): void;
    private logExceptionDetails;
    private extractClientIP;
    private sanitizeRequestBody;
}
