{"c": ["app/layout", "webpack"], "r": ["app/test-ip/page"], "m": ["(app-pages-browser)/./app/test-ip/page.tsx", "(app-pages-browser)/./node_modules/antd/es/card/Card.js", "(app-pages-browser)/./node_modules/antd/es/card/Grid.js", "(app-pages-browser)/./node_modules/antd/es/card/Meta.js", "(app-pages-browser)/./node_modules/antd/es/card/index.js", "(app-pages-browser)/./node_modules/antd/es/card/style/index.js", "(app-pages-browser)/./node_modules/antd/es/divider/index.js", "(app-pages-browser)/./node_modules/antd/es/divider/style/index.js", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Clogicleap2%5C%5Clogicleapweb%5C%5Capp%5C%5Ctest-ip%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"]}