import { OnModuleInit } from '@nestjs/common';
import { Logger } from 'winston';
export declare class ConsoleOverrideService implements OnModuleInit {
    private readonly logger;
    private originalConsole;
    constructor(logger: Logger);
    onModuleInit(): void;
    private overrideConsole;
    private formatMessage;
    private callOriginalInDev;
    restoreConsole(): void;
    getOriginalConsole(): any;
}
