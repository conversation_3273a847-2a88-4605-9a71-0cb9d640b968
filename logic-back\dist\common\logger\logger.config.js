"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createLoggerConfig = createLoggerConfig;
exports.overrideConsole = overrideConsole;
const winston = require("winston");
const DailyRotateFile = require("winston-daily-rotate-file");
const path = require("path");
const fs = require("fs");
const logsDir = path.join(process.cwd(), 'logs');
if (!fs.existsSync(logsDir)) {
    fs.mkdirSync(logsDir, { recursive: true });
}
const logFormat = winston.format.combine(winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss.SSS'
}), winston.format.errors({ stack: true }), winston.format.json(), winston.format.printf(({ timestamp, level, message, context, trace, ...meta }) => {
    let logMessage = `${timestamp} [${level.toUpperCase()}]`;
    if (context) {
        logMessage += ` [${context}]`;
    }
    logMessage += ` ${message}`;
    if (Object.keys(meta).length > 0) {
        logMessage += ` ${JSON.stringify(meta)}`;
    }
    if (trace) {
        logMessage += `\n${trace}`;
    }
    return logMessage;
}));
const consoleFormat = winston.format.combine(winston.format.colorize(), winston.format.timestamp({
    format: 'HH:mm:ss.SSS'
}), winston.format.printf(({ timestamp, level, message, context }) => {
    let logMessage = `${timestamp} ${level}`;
    if (context) {
        logMessage += ` [${context}]`;
    }
    logMessage += ` ${message}`;
    return logMessage;
}));
function createLoggerConfig() {
    const isDevelopment = process.env.NODE_ENV === 'dev' || process.env.NODE_ENV === 'development';
    const transports = [];
    if (isDevelopment) {
        transports.push(new winston.transports.Console({
            format: consoleFormat,
            level: 'debug'
        }));
    }
    transports.push(new DailyRotateFile({
        filename: path.join(logsDir, 'application-%DATE%.log'),
        datePattern: 'YYYY-MM-DD',
        zippedArchive: true,
        maxSize: '20m',
        maxFiles: '30d',
        format: logFormat,
        level: 'debug'
    }));
    transports.push(new DailyRotateFile({
        filename: path.join(logsDir, 'error-%DATE%.log'),
        datePattern: 'YYYY-MM-DD',
        zippedArchive: true,
        maxSize: '20m',
        maxFiles: '30d',
        format: logFormat,
        level: 'error'
    }));
    transports.push(new DailyRotateFile({
        filename: path.join(logsDir, 'http-%DATE%.log'),
        datePattern: 'YYYY-MM-DD',
        zippedArchive: true,
        maxSize: '20m',
        maxFiles: '30d',
        format: logFormat,
        level: 'info'
    }));
    transports.push(new DailyRotateFile({
        filename: path.join(logsDir, 'database-%DATE%.log'),
        datePattern: 'YYYY-MM-DD',
        zippedArchive: true,
        maxSize: '20m',
        maxFiles: '30d',
        format: logFormat,
        level: 'debug'
    }));
    transports.push(new DailyRotateFile({
        filename: path.join(logsDir, 'console-%DATE%.log'),
        datePattern: 'YYYY-MM-DD',
        zippedArchive: true,
        maxSize: '20m',
        maxFiles: '30d',
        format: logFormat,
        level: 'debug'
    }));
    return {
        transports,
        format: logFormat,
        defaultMeta: {
            service: 'logic-back',
            version: process.env.npm_package_version || '1.0.0',
            environment: process.env.NODE_ENV || 'development',
            pid: process.pid,
            hostname: require('os').hostname()
        },
        exitOnError: false,
        exceptionHandlers: [
            new DailyRotateFile({
                filename: path.join(logsDir, 'exceptions-%DATE%.log'),
                datePattern: 'YYYY-MM-DD',
                zippedArchive: true,
                maxSize: '20m',
                maxFiles: '30d',
                format: logFormat
            })
        ],
        rejectionHandlers: [
            new DailyRotateFile({
                filename: path.join(logsDir, 'rejections-%DATE%.log'),
                datePattern: 'YYYY-MM-DD',
                zippedArchive: true,
                maxSize: '20m',
                maxFiles: '30d',
                format: logFormat
            })
        ]
    };
}
function overrideConsole(logger) {
    const originalConsole = {
        log: console.log,
        error: console.error,
        warn: console.warn,
        info: console.info,
        debug: console.debug,
        trace: console.trace,
        assert: console.assert,
        dir: console.dir,
        dirxml: console.dirxml,
        group: console.group,
        groupCollapsed: console.groupCollapsed,
        groupEnd: console.groupEnd,
        table: console.table,
        time: console.time,
        timeEnd: console.timeEnd,
        count: console.count,
        clear: console.clear
    };
    console.log = (...args) => {
        const message = args.map(arg => typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)).join(' ');
        logger.info(message, { context: 'Console' });
        if (process.env.NODE_ENV === 'dev' || process.env.NODE_ENV === 'development') {
            originalConsole.log(...args);
        }
    };
    console.error = (...args) => {
        const message = args.map(arg => typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)).join(' ');
        logger.error(message, { context: 'Console' });
        if (process.env.NODE_ENV === 'dev' || process.env.NODE_ENV === 'development') {
            originalConsole.error(...args);
        }
    };
    console.warn = (...args) => {
        const message = args.map(arg => typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)).join(' ');
        logger.warn(message, { context: 'Console' });
        if (process.env.NODE_ENV === 'dev' || process.env.NODE_ENV === 'development') {
            originalConsole.warn(...args);
        }
    };
    console.info = (...args) => {
        const message = args.map(arg => typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)).join(' ');
        logger.info(message, { context: 'Console' });
        if (process.env.NODE_ENV === 'dev' || process.env.NODE_ENV === 'development') {
            originalConsole.info(...args);
        }
    };
    console.debug = (...args) => {
        const message = args.map(arg => typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)).join(' ');
        logger.debug(message, { context: 'Console' });
        if (process.env.NODE_ENV === 'dev' || process.env.NODE_ENV === 'development') {
            originalConsole.debug(...args);
        }
    };
    console.trace = (...args) => {
        const message = args.map(arg => typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)).join(' ');
        const stack = new Error().stack;
        logger.debug(message, { context: 'Console', trace: stack });
        if (process.env.NODE_ENV === 'dev' || process.env.NODE_ENV === 'development') {
            originalConsole.trace(...args);
        }
    };
    console.assert = (condition, ...args) => {
        if (!condition) {
            const message = args.map(arg => typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)).join(' ');
            logger.error(`Assertion failed: ${message}`, { context: 'Console' });
        }
        if (process.env.NODE_ENV === 'dev' || process.env.NODE_ENV === 'development') {
            originalConsole.assert(condition, ...args);
        }
    };
    console.dir = (obj, options) => {
        const message = JSON.stringify(obj, null, 2);
        logger.info(`Dir: ${message}`, { context: 'Console' });
        if (process.env.NODE_ENV === 'dev' || process.env.NODE_ENV === 'development') {
            originalConsole.dir(obj, options);
        }
    };
    console.table = (data) => {
        const message = JSON.stringify(data, null, 2);
        logger.info(`Table: ${message}`, { context: 'Console' });
        if (process.env.NODE_ENV === 'dev' || process.env.NODE_ENV === 'development') {
            originalConsole.table(data);
        }
    };
    console.group = (...args) => {
        const message = args.join(' ');
        logger.info(`Group: ${message}`, { context: 'Console' });
        if (process.env.NODE_ENV === 'dev' || process.env.NODE_ENV === 'development') {
            originalConsole.group(...args);
        }
    };
    console.groupCollapsed = (...args) => {
        const message = args.join(' ');
        logger.info(`GroupCollapsed: ${message}`, { context: 'Console' });
        if (process.env.NODE_ENV === 'dev' || process.env.NODE_ENV === 'development') {
            originalConsole.groupCollapsed(...args);
        }
    };
    console.groupEnd = () => {
        logger.info('GroupEnd', { context: 'Console' });
        if (process.env.NODE_ENV === 'dev' || process.env.NODE_ENV === 'development') {
            originalConsole.groupEnd();
        }
    };
    const timers = new Map();
    console.time = (label = 'default') => {
        timers.set(label, Date.now());
        logger.info(`Timer started: ${label}`, { context: 'Console' });
        if (process.env.NODE_ENV === 'dev' || process.env.NODE_ENV === 'development') {
            originalConsole.time(label);
        }
    };
    console.timeEnd = (label = 'default') => {
        const startTime = timers.get(label);
        if (startTime) {
            const duration = Date.now() - startTime;
            timers.delete(label);
            logger.info(`Timer ended: ${label} - ${duration}ms`, { context: 'Console' });
        }
        if (process.env.NODE_ENV === 'dev' || process.env.NODE_ENV === 'development') {
            originalConsole.timeEnd(label);
        }
    };
    const counters = new Map();
    console.count = (label = 'default') => {
        const count = (counters.get(label) || 0) + 1;
        counters.set(label, count);
        logger.info(`Count: ${label} - ${count}`, { context: 'Console' });
        if (process.env.NODE_ENV === 'dev' || process.env.NODE_ENV === 'development') {
            originalConsole.count(label);
        }
    };
    console.clear = () => {
        logger.info('Console cleared', { context: 'Console' });
        if (process.env.NODE_ENV === 'dev' || process.env.NODE_ENV === 'development') {
            originalConsole.clear();
        }
    };
    return originalConsole;
}
//# sourceMappingURL=logger.config.js.map