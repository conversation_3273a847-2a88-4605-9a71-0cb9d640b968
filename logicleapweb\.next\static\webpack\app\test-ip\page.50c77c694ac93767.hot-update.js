"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/test-ip/page",{

/***/ "(app-pages-browser)/./app/test-ip/page.tsx":
/*!******************************!*\
  !*** ./app/test-ip/page.tsx ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ TestIPPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Divider,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/typography/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Divider,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/space/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Divider,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/card/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Divider,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Divider,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/divider/index.js\");\n/* harmony import */ var _lib_request__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/request */ \"(app-pages-browser)/./lib/request.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst { Title, Text, Paragraph } = _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_3__[\"default\"];\nfunction TestIPPage() {\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [result, setResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [realPublicIP, setRealPublicIP] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [logs, setLogs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 添加日志的辅助函数\n    const addLog = (message, data)=>{\n        const timestamp = new Date().toLocaleTimeString();\n        const logEntry = \"[\".concat(timestamp, \"] \").concat(message);\n        // 输出到控制台\n        if (data) {\n            console.log(message, data);\n        } else {\n            console.log(message);\n        }\n        // 添加到页面日志\n        setLogs((prev)=>[\n                ...prev.slice(-19),\n                logEntry\n            ]); // 保留最近20条日志\n    };\n    // 清空日志\n    const clearLogs = ()=>{\n        setLogs([]);\n        console.clear();\n        addLog(\"\\uD83E\\uDDF9 [Frontend] 日志已清空\");\n    };\n    // 页面加载时输出环境信息\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        var _navigator_connection, _navigator_connection1;\n        console.log(\"\\uD83C\\uDF10 [Frontend Init] 测试页面初始化...\");\n        console.log(\"\\uD83D\\uDDA5️ [Frontend Init] 浏览器环境信息:\", {\n            页面信息: {\n                URL: window.location.href,\n                域名: window.location.hostname,\n                端口: window.location.port,\n                协议: window.location.protocol,\n                路径: window.location.pathname\n            },\n            访问方式: {\n                是否本地访问: window.location.hostname === \"localhost\",\n                是否内网穿透: window.location.hostname.includes(\"ngrok\") || window.location.hostname.includes(\"tunnel\"),\n                访问类型: window.location.hostname === \"localhost\" ? \"本地开发\" : window.location.hostname.includes(\"ngrok\") ? \"ngrok穿透\" : window.location.hostname.includes(\"tunnel\") ? \"其他穿透\" : \"未知\"\n            },\n            浏览器信息: {\n                用户代理: navigator.userAgent,\n                语言: navigator.language,\n                平台: navigator.platform,\n                在线状态: navigator.onLine\n            },\n            网络信息: {\n                连接类型: ((_navigator_connection = navigator.connection) === null || _navigator_connection === void 0 ? void 0 : _navigator_connection.effectiveType) || \"未知\",\n                网络状态: ((_navigator_connection1 = navigator.connection) === null || _navigator_connection1 === void 0 ? void 0 : _navigator_connection1.downlink) ? \"\".concat(navigator.connection.downlink, \"Mbps\") : \"未知\"\n            },\n            时间信息: {\n                本地时间: new Date().toISOString(),\n                时区: Intl.DateTimeFormat().resolvedOptions().timeZone,\n                时区偏移: new Date().getTimezoneOffset()\n            }\n        });\n        // 输出预期的测试流程\n        console.log(\"\\uD83D\\uDCCB [Frontend Init] 测试流程说明:\", {\n            测试目标: \"IP地址获取和传递功能验证\",\n            测试步骤: [\n                \"1. 获取真实公网IP (通过第三方API)\",\n                \"2. 测试当前IP位置获取 (后端API)\",\n                \"3. 测试指定IP查询 (*******)\",\n                \"4. 测试登录IP记录 (模拟登录失败)\"\n            ],\n            观察要点: [\n                \"前端请求日志 (\\uD83D\\uDCE4 [Frontend Request])\",\n                \"前端响应日志 (\\uD83D\\uDCE5 [Frontend Response])\",\n                \"中间件IP处理 (\\uD83C\\uDF10 [Middleware])\",\n                \"后端IP提取 (\\uD83D\\uDDA5️ [Backend])\",\n                \"登录IP记录 (\\uD83D\\uDD10 [LoginLog])\"\n            ],\n            预期结果: {\n                本地访问: \"IP为模拟值 (**************)\",\n                穿透访问: \"IP为真实公网IP\",\n                地理位置: \"根据IP解析出对应位置\"\n            }\n        });\n    }, []);\n    // 获取真实的公网IP地址\n    const getRealPublicIP = async ()=>{\n        setLoading(true);\n        try {\n            console.log(\"\\uD83C\\uDF0D [Frontend] 开始获取真实公网IP...\");\n            // 使用多个IP查询服务，提高成功率\n            const ipServices = [\n                \"https://api.ipify.org?format=json\",\n                \"https://ipapi.co/json/\",\n                \"https://httpbin.org/ip\",\n                \"https://api.ip.sb/ip\",\n                \"https://ifconfig.me/ip\",\n                \"https://icanhazip.com\"\n            ];\n            for (const service of ipServices){\n                try {\n                    console.log(\"\\uD83D\\uDD0D 尝试获取公网IP: \".concat(service));\n                    const controller = new AbortController();\n                    const timeoutId = setTimeout(()=>controller.abort(), 5000);\n                    const response = await fetch(service, {\n                        method: \"GET\",\n                        signal: controller.signal\n                    });\n                    clearTimeout(timeoutId);\n                    if (!response.ok) continue;\n                    let data;\n                    const contentType = response.headers.get(\"content-type\");\n                    if (contentType && contentType.includes(\"application/json\")) {\n                        data = await response.json();\n                        const ip = data.ip || data.origin || data.query;\n                        if (ip && isValidIPAddress(ip)) {\n                            console.log(\"✅ 成功获取公网IP: \".concat(ip, \" (来源: \").concat(service, \")\"));\n                            setRealPublicIP(ip);\n                            setResult({\n                                type: \"real-public-ip\",\n                                ip: ip,\n                                source: service,\n                                timestamp: new Date().toISOString()\n                            });\n                            return;\n                        }\n                    } else {\n                        const text = await response.text();\n                        const ip = text.trim();\n                        if (isValidIPAddress(ip)) {\n                            console.log(\"✅ 成功获取公网IP: \".concat(ip, \" (来源: \").concat(service, \")\"));\n                            setRealPublicIP(ip);\n                            setResult({\n                                type: \"real-public-ip\",\n                                ip: ip,\n                                source: service,\n                                timestamp: new Date().toISOString()\n                            });\n                            return;\n                        }\n                    }\n                } catch (error) {\n                    console.log(\"❌ 获取公网IP失败: \".concat(service, \" - \").concat(error));\n                    continue;\n                }\n            }\n            throw new Error(\"所有公网IP服务都无法访问\");\n        } catch (error) {\n            console.error(\"❌ [Frontend] 获取真实公网IP失败:\", error);\n            setResult({\n                type: \"error\",\n                error: error.message,\n                timestamp: new Date().toISOString()\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 验证IP地址格式\n    const isValidIPAddress = (ip)=>{\n        const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;\n        return ipv4Regex.test(ip);\n    };\n    // 测试获取当前IP位置\n    const testCurrentIP = async ()=>{\n        setLoading(true);\n        try {\n            console.log(\"\\uD83E\\uDDEA [Frontend Test] 开始测试当前IP获取...\");\n            console.log(\"\\uD83C\\uDF10 [Frontend Test] 当前环境信息:\", {\n                页面URL: window.location.href,\n                域名: window.location.hostname,\n                是否内网穿透: window.location.hostname.includes(\"ngrok\") || window.location.hostname.includes(\"tunnel\"),\n                用户代理: navigator.userAgent.substring(0, 100) + \"...\",\n                时间戳: new Date().toISOString()\n            });\n            console.log(\"\\uD83D\\uDCE4 [Frontend Test] 准备发送请求到: /api/v1/ip-location/current\");\n            const startTime = Date.now();\n            const response = await _lib_request__WEBPACK_IMPORTED_MODULE_2__[\"default\"].get(\"/api/v1/ip-location/current\");\n            const endTime = Date.now();\n            console.log(\"\\uD83D\\uDCE5 [Frontend Test] 请求完成:\", {\n                耗时: \"\".concat(endTime - startTime, \"ms\"),\n                响应状态: response.status,\n                响应数据: response.data,\n                时间戳: new Date().toISOString()\n            });\n            setResult({\n                type: \"current-ip\",\n                data: response.data,\n                requestTime: endTime - startTime,\n                timestamp: new Date().toISOString()\n            });\n        } catch (error) {\n            var _error_response, _error_response1, _error_response2;\n            console.error(\"❌ [Frontend Test] 当前IP测试失败:\", {\n                错误类型: error.name,\n                错误消息: error.message,\n                响应状态: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status,\n                响应数据: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.data,\n                完整错误: error,\n                时间戳: new Date().toISOString()\n            });\n            setResult({\n                type: \"error\",\n                error: error.message,\n                response: (_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.data,\n                timestamp: new Date().toISOString()\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 测试查询指定IP\n    const testQueryIP = async ()=>{\n        setLoading(true);\n        try {\n            const testIP = \"*******\"; // Google DNS\n            console.log(\"\\uD83E\\uDDEA [Frontend Test] 开始测试IP查询...\");\n            console.log(\"\\uD83C\\uDFAF [Frontend Test] 查询参数:\", {\n                目标IP: testIP,\n                IP类型: \"Google DNS服务器\",\n                预期位置: \"美国\",\n                包含风险评估: false,\n                时间戳: new Date().toISOString()\n            });\n            const queryUrl = \"/api/v1/ip-location/query?ip=\".concat(testIP, \"&includeRisk=false\");\n            console.log(\"\\uD83D\\uDCE4 [Frontend Test] 准备发送查询请求:\", queryUrl);\n            const startTime = Date.now();\n            const response = await _lib_request__WEBPACK_IMPORTED_MODULE_2__[\"default\"].get(queryUrl);\n            const endTime = Date.now();\n            console.log(\"\\uD83D\\uDCE5 [Frontend Test] IP查询完成:\", {\n                查询IP: testIP,\n                耗时: \"\".concat(endTime - startTime, \"ms\"),\n                响应状态: response.status,\n                地理位置: response.data ? {\n                    国家: response.data.country,\n                    省份: response.data.province,\n                    城市: response.data.city,\n                    运营商: response.data.isp,\n                    置信度: response.data.confidence\n                } : \"无数据\",\n                完整响应: response.data,\n                时间戳: new Date().toISOString()\n            });\n            setResult({\n                type: \"query-ip\",\n                testIP,\n                data: response.data,\n                requestTime: endTime - startTime,\n                timestamp: new Date().toISOString()\n            });\n        } catch (error) {\n            var _error_response, _error_response1, _error_response2;\n            console.error(\"❌ [Frontend Test] IP查询失败:\", {\n                查询IP: \"*******\",\n                错误类型: error.name,\n                错误消息: error.message,\n                响应状态: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status,\n                响应数据: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.data,\n                可能原因: [\n                    \"后端服务未启动\",\n                    \"IP解析服务异常\",\n                    \"网络连接问题\"\n                ],\n                时间戳: new Date().toISOString()\n            });\n            setResult({\n                type: \"error\",\n                error: error.message,\n                response: (_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.data,\n                timestamp: new Date().toISOString()\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 测试登录接口（观察IP日志）\n    const testLoginIP = async ()=>{\n        setLoading(true);\n        try {\n            console.log(\"\\uD83E\\uDDEA [Frontend Test] 开始测试登录IP获取...\");\n            console.log(\"\\uD83D\\uDD10 [Frontend Test] 登录测试说明:\", {\n                目的: \"观察登录时的IP获取和记录过程\",\n                预期结果: \"登录失败（使用错误凭据）\",\n                观察重点: [\n                    \"IP地址获取\",\n                    \"登录日志记录\",\n                    \"错误处理\"\n                ],\n                测试凭据: {\n                    手机号: \"12345678910\",\n                    密码: \"123456 (错误密码)\"\n                },\n                时间戳: new Date().toISOString()\n            });\n            console.log(\"\\uD83D\\uDCE4 [Frontend Test] 发送登录请求...\");\n            const startTime = Date.now();\n            // 这里故意使用错误的登录信息，只是为了触发IP获取逻辑\n            const response = await _lib_request__WEBPACK_IMPORTED_MODULE_2__[\"default\"].post(\"/api/user-auth/password\", {\n                phone: \"12345678910\",\n                password: \"123456\"\n            });\n            const endTime = Date.now();\n            console.log(\"\\uD83D\\uDCE5 [Frontend Test] 登录响应 (意外成功):\", {\n                耗时: \"\".concat(endTime - startTime, \"ms\"),\n                响应状态: response.status,\n                响应数据: response.data,\n                注意: \"这不应该成功，请检查后端验证逻辑\",\n                时间戳: new Date().toISOString()\n            });\n            setResult({\n                type: \"login-test\",\n                data: response.data,\n                requestTime: endTime - startTime,\n                timestamp: new Date().toISOString()\n            });\n        } catch (error) {\n            var _error_response, _error_response1, _error_response2;\n            console.log(\"\\uD83D\\uDCDD [Frontend Test] 登录测试完成 (预期失败):\", {\n                错误类型: error.name,\n                错误消息: error.message,\n                响应状态: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status,\n                响应数据: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.data,\n                分析: {\n                    是否预期失败: true,\n                    失败原因: \"使用了错误的登录凭据\",\n                    IP获取状态: \"应该已触发IP获取和日志记录\",\n                    后续检查: \"查看后端控制台的 [LoginLog] 日志\"\n                },\n                时间戳: new Date().toISOString()\n            });\n            setResult({\n                type: \"login-test\",\n                error: error.message,\n                response: (_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.data,\n                note: \"这是预期的失败，主要用于观察IP获取日志\",\n                timestamp: new Date().toISOString()\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"24px\",\n            maxWidth: \"1200px\",\n            margin: \"0 auto\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                level: 2,\n                children: \"\\uD83E\\uDDEA IP地址获取测试页面\"\n            }, void 0, false, {\n                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                lineNumber: 370,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                children: \"这个页面用于测试前端到后端的IP地址传递和获取功能。 请打开浏览器开发者工具的控制台，以及后端服务器的日志，观察IP获取过程。\"\n            }, void 0, false, {\n                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                lineNumber: 372,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                direction: \"vertical\",\n                size: \"large\",\n                style: {\n                    width: \"100%\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83C\\uDFAF 测试功能\",\n                        size: \"small\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                wrap: true,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        type: \"primary\",\n                                        loading: loading,\n                                        onClick: getRealPublicIP,\n                                        style: {\n                                            background: \"#52c41a\",\n                                            borderColor: \"#52c41a\"\n                                        },\n                                        children: \"\\uD83C\\uDF0D 获取真实公网IP\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                        lineNumber: 382,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        loading: loading,\n                                        onClick: testCurrentIP,\n                                        children: \"测试获取当前IP位置\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                        lineNumber: 391,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        loading: loading,\n                                        onClick: testQueryIP,\n                                        children: \"测试查询指定IP (*******)\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                        lineNumber: 398,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        loading: loading,\n                                        onClick: testLoginIP,\n                                        danger: true,\n                                        children: \"测试登录IP获取 (会失败)\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        onClick: clearLogs,\n                                        style: {\n                                            marginLeft: \"12px\"\n                                        },\n                                        children: \"\\uD83E\\uDDF9 清空日志\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                        lineNumber: 413,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                lineNumber: 381,\n                                columnNumber: 11\n                            }, this),\n                            realPublicIP && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginTop: \"12px\",\n                                    padding: \"8px\",\n                                    background: \"#f6ffed\",\n                                    border: \"1px solid #b7eb8f\",\n                                    borderRadius: \"6px\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    strong: true,\n                                    style: {\n                                        color: \"#52c41a\"\n                                    },\n                                    children: [\n                                        \"\\uD83C\\uDF0D 你的真实公网IP: \",\n                                        realPublicIP\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 423,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                lineNumber: 422,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 380,\n                        columnNumber: 9\n                    }, this),\n                    result && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83D\\uDCCA 测试结果\",\n                        size: \"small\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                background: \"#f5f5f5\",\n                                padding: \"16px\",\n                                borderRadius: \"6px\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                style: {\n                                    margin: 0,\n                                    fontSize: \"12px\",\n                                    lineHeight: \"1.4\"\n                                },\n                                children: JSON.stringify(result, null, 2)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                lineNumber: 432,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                            lineNumber: 431,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 430,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 439,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83D\\uDCCB 观察要点\",\n                        size: \"small\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            direction: \"vertical\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            children: \"\\uD83C\\uDF10 前端中间件日志：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 445,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"查看浏览器控制台，观察 [Middleware] 标记的日志\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 446,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 444,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            children: \"\\uD83D\\uDDA5️ 后端IP提取日志：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 450,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"查看后端控制台，观察 [Backend] 标记的日志\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 451,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 449,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            children: \"\\uD83D\\uDD10 登录日志：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 455,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"查看后端控制台，观察 [LoginLog] 标记的日志\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 456,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 454,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            children: \"\\uD83D\\uDD0D 重点观察：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 460,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"IP地址是否从前端正确传递到后端，以及各个环节的IP获取情况\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 461,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 459,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                            lineNumber: 443,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 442,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83E\\uDD14 为什么本地开发获取到127.0.0.1？\",\n                        size: \"small\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            direction: \"vertical\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            style: {\n                                                color: \"#fa8c16\"\n                                            },\n                                            children: \"\\uD83C\\uDFE0 本地开发环境：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 470,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"浏览器 → localhost:3000 → 后端API，所有请求都来自本机，所以IP是127.0.0.1\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 471,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 469,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            style: {\n                                                color: \"#52c41a\"\n                                            },\n                                            children: \"\\uD83C\\uDF0D 生产环境：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 475,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"用户浏览器 → CDN/负载均衡 → Web服务器 → 后端API，能获取到真实公网IP\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 476,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 474,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            style: {\n                                                color: \"#1890ff\"\n                                            },\n                                            children: \"\\uD83C\\uDFAD 模拟解决方案：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 480,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"中间件已配置在开发环境使用模拟公网IP (**************) 进行测试\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 481,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 479,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            style: {\n                                                color: \"#722ed1\"\n                                            },\n                                            children: \"\\uD83E\\uDDEA 真实IP对比：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 485,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: '点击\"获取真实公网IP\"按钮，对比你的真实公网IP和后端获取的IP'\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 486,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 484,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                            lineNumber: 468,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 467,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83C\\uDF0D 当前环境信息\",\n                        size: \"small\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            direction: \"vertical\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    children: [\n                                        \"浏览器 User-Agent: \",\n                                         true ? navigator.userAgent : 0\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 494,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    children: [\n                                        \"当前时间: \",\n                                        new Date().toISOString()\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 495,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    children: [\n                                        \"页面URL: \",\n                                         true ? window.location.href : 0\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 496,\n                                    columnNumber: 13\n                                }, this),\n                                 true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    style: {\n                                        color: window.location.hostname.includes(\"ngrok\") || window.location.hostname.includes(\"tunnel\") ? \"#52c41a\" : \"#fa8c16\",\n                                        fontWeight: \"bold\"\n                                    },\n                                    children: [\n                                        \"访问方式: \",\n                                        window.location.hostname.includes(\"ngrok\") || window.location.hostname.includes(\"tunnel\") ? \"\\uD83C\\uDF10 内网穿透访问 (可获取真实IP)\" : \"\\uD83C\\uDFE0 本地访问 (模拟IP)\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 498,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                            lineNumber: 493,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 492,\n                        columnNumber: 9\n                    }, this),\n                     true && !window.location.hostname.includes(\"ngrok\") && !window.location.hostname.includes(\"tunnel\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83D\\uDE80 想要测试真实IP获取？\",\n                        size: \"small\",\n                        style: {\n                            borderColor: \"#52c41a\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            direction: \"vertical\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    strong: true,\n                                    style: {\n                                        color: \"#52c41a\"\n                                    },\n                                    children: \"使用内网穿透获取真实IP：\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 516,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        background: \"#f6ffed\",\n                                        padding: \"12px\",\n                                        borderRadius: \"6px\",\n                                        border: \"1px solid #b7eb8f\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            code: true,\n                                            children: \"1. 安装ngrok: npm install -g ngrok\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 518,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 518,\n                                            columnNumber: 67\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            code: true,\n                                            children: \"2. 穿透前端: ngrok http 3000\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 519,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 519,\n                                            columnNumber: 59\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            code: true,\n                                            children: \"3. 访问ngrok提供的公网地址\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 520,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 520,\n                                            columnNumber: 52\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            code: true,\n                                            children: \"4. 重新测试IP获取功能\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 521,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 517,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    type: \"secondary\",\n                                    children: \"通过内网穿透，你可以模拟真实的生产环境，获取到真实的公网IP地址！\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 523,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                            lineNumber: 515,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 514,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                lineNumber: 377,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n        lineNumber: 369,\n        columnNumber: 5\n    }, this);\n}\n_s(TestIPPage, \"YdJxZSDzzBKbremxKAojQG9Ufuw=\");\n_c = TestIPPage;\nvar _c;\n$RefreshReg$(_c, \"TestIPPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/test-ip/page.tsx\n"));

/***/ })

});