globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/login/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./app/providers.tsx":{"*":{"id":"(ssr)/./app/providers.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/(main)/home/<USER>":{"*":{"id":"(ssr)/./app/(main)/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/(main)/layout.tsx":{"*":{"id":"(ssr)/./app/(main)/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/login/page.tsx":{"*":{"id":"(ssr)/./app/login/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"F:\\logicleap2\\logicleapweb\\app\\providers.tsx":{"id":"(app-pages-browser)/./app/providers.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"F:\\logicleap2\\logicleapweb\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"F:\\logicleap2\\logicleapweb\\app\\globals.css":{"id":"(app-pages-browser)/./app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"F:\\logicleap2\\logicleapweb\\node_modules\\antd\\dist\\reset.css":{"id":"(app-pages-browser)/./node_modules/antd/dist/reset.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"F:\\logicleap2\\logicleapweb\\node_modules\\next\\dist\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"F:\\logicleap2\\logicleapweb\\node_modules\\next\\dist\\esm\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"F:\\logicleap2\\logicleapweb\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"F:\\logicleap2\\logicleapweb\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"F:\\logicleap2\\logicleapweb\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"F:\\logicleap2\\logicleapweb\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"F:\\logicleap2\\logicleapweb\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"F:\\logicleap2\\logicleapweb\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"F:\\logicleap2\\logicleapweb\\node_modules\\next\\dist\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"F:\\logicleap2\\logicleapweb\\node_modules\\next\\dist\\esm\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"F:\\logicleap2\\logicleapweb\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"F:\\logicleap2\\logicleapweb\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"F:\\logicleap2\\logicleapweb\\app\\(main)\\home\\page.tsx":{"id":"(app-pages-browser)/./app/(main)/home/<USER>","name":"*","chunks":[],"async":false},"F:\\logicleap2\\logicleapweb\\app\\(main)\\layout.tsx":{"id":"(app-pages-browser)/./app/(main)/layout.tsx","name":"*","chunks":["app/(main)/layout","static/chunks/app/(main)/layout.js"],"async":false},"F:\\logicleap2\\logicleapweb\\app\\login\\page.tsx":{"id":"(app-pages-browser)/./app/login/page.tsx","name":"*","chunks":["app/login/page","static/chunks/app/login/page.js"],"async":false}},"entryCSSFiles":{"F:\\logicleap2\\logicleapweb\\":[],"F:\\logicleap2\\logicleapweb\\app\\page":[],"F:\\logicleap2\\logicleapweb\\app\\layout":["static/css/app/layout.css"],"F:\\logicleap2\\logicleapweb\\app\\(main)\\layout":["static/css/app/(main)/layout.css"],"F:\\logicleap2\\logicleapweb\\app\\login\\page":["static/css/app/login/page.css"]}}