"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/debug/headers/route";
exports.ids = ["app/api/debug/headers/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdebug%2Fheaders%2Froute&page=%2Fapi%2Fdebug%2Fheaders%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdebug%2Fheaders%2Froute.ts&appDir=F%3A%5Clogicleap2%5Clogicleapweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5Clogicleap2%5Clogicleapweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdebug%2Fheaders%2Froute&page=%2Fapi%2Fdebug%2Fheaders%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdebug%2Fheaders%2Froute.ts&appDir=F%3A%5Clogicleap2%5Clogicleapweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5Clogicleap2%5Clogicleapweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var F_logicleap2_logicleapweb_app_api_debug_headers_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/debug/headers/route.ts */ \"(rsc)/./app/api/debug/headers/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/debug/headers/route\",\n        pathname: \"/api/debug/headers\",\n        filename: \"route\",\n        bundlePath: \"app/api/debug/headers/route\"\n    },\n    resolvedPagePath: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\api\\\\debug\\\\headers\\\\route.ts\",\n    nextConfigOutput,\n    userland: F_logicleap2_logicleapweb_app_api_debug_headers_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/debug/headers/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdebug%2Fheaders%2Froute&page=%2Fapi%2Fdebug%2Fheaders%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdebug%2Fheaders%2Froute.ts&appDir=F%3A%5Clogicleap2%5Clogicleapweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5Clogicleap2%5Clogicleapweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/debug/headers/route.ts":
/*!****************************************!*\
  !*** ./app/api/debug/headers/route.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\nasync function GET(request) {\n    try {\n        // 获取所有请求头\n        const headers = {};\n        request.headers.forEach((value, key)=>{\n            headers[key] = value;\n        });\n        // 获取连接信息\n        const connectionInfo = {\n            ip: request.ip,\n            geo: request.geo,\n            nextUrl: {\n                href: request.nextUrl.href,\n                origin: request.nextUrl.origin,\n                pathname: request.nextUrl.pathname,\n                search: request.nextUrl.search\n            }\n        };\n        // 分析IP相关头部\n        const ipHeaders = [\n            \"x-forwarded-for\",\n            \"x-real-ip\",\n            \"cf-connecting-ip\",\n            \"x-client-ip\",\n            \"x-cluster-client-ip\",\n            \"x-original-forwarded-for\",\n            \"x-forwarded-for-original\",\n            \"x-client-ip-original\",\n            \"x-tunnel-client-ip\",\n            \"true-client-ip\",\n            \"remote-addr\"\n        ];\n        const ipAnalysis = {};\n        ipHeaders.forEach((header)=>{\n            ipAnalysis[header] = headers[header] || null;\n        });\n        // 检测访问方式\n        const host = headers.host || \"\";\n        const accessType = host.includes(\"ngrok\") ? \"ngrok\" : host.includes(\"vicp.fun\") ? \"vicp.fun\" : host.includes(\"tunnel\") ? \"tunnel\" : host === \"localhost:3000\" ? \"localhost\" : \"unknown\";\n        const response = {\n            success: true,\n            timestamp: new Date().toISOString(),\n            accessType,\n            headers,\n            connectionInfo,\n            ipAnalysis,\n            summary: {\n                totalHeaders: Object.keys(headers).length,\n                ipRelatedHeaders: Object.values(ipAnalysis).filter((v)=>v !== null).length,\n                detectedIp: ipAnalysis[\"x-forwarded-for\"] || ipAnalysis[\"x-real-ip\"] || ipAnalysis[\"cf-connecting-ip\"] || connectionInfo.ip || \"未检测到\",\n                isRealIp: ![\n                    \"127.0.0.1\",\n                    \"localhost\",\n                    \"::1\"\n                ].includes(ipAnalysis[\"x-forwarded-for\"] || ipAnalysis[\"x-real-ip\"] || connectionInfo.ip || \"\")\n            }\n        };\n        console.log(\"\\uD83D\\uDD0D [Debug API] 请求头调试信息:\", response);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(response);\n    } catch (error) {\n        console.error(\"\\uD83D\\uDD0D [Debug API] 获取请求头失败:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: error instanceof Error ? error.message : \"未知错误\",\n            timestamp: new Date().toISOString()\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/debug/headers/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdebug%2Fheaders%2Froute&page=%2Fapi%2Fdebug%2Fheaders%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdebug%2Fheaders%2Froute.ts&appDir=F%3A%5Clogicleap2%5Clogicleapweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5Clogicleap2%5Clogicleapweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();