"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/test-ip/page",{

/***/ "(app-pages-browser)/./app/test-ip/page.tsx":
/*!******************************!*\
  !*** ./app/test-ip/page.tsx ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ TestIPPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Divider,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/typography/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Divider,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/space/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Divider,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/card/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Divider,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Divider,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/divider/index.js\");\n/* harmony import */ var _lib_request__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/request */ \"(app-pages-browser)/./lib/request.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst { Title, Text, Paragraph } = _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_3__[\"default\"];\nfunction TestIPPage() {\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [result, setResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [realPublicIP, setRealPublicIP] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 获取真实的公网IP地址\n    const getRealPublicIP = async ()=>{\n        setLoading(true);\n        try {\n            console.log(\"\\uD83C\\uDF0D [Frontend] 开始获取真实公网IP...\");\n            // 使用多个IP查询服务，提高成功率\n            const ipServices = [\n                \"https://api.ipify.org?format=json\",\n                \"https://ipapi.co/json/\",\n                \"https://httpbin.org/ip\",\n                \"https://api.ip.sb/ip\",\n                \"https://ifconfig.me/ip\",\n                \"https://icanhazip.com\"\n            ];\n            for (const service of ipServices){\n                try {\n                    console.log(\"\\uD83D\\uDD0D 尝试获取公网IP: \".concat(service));\n                    const controller = new AbortController();\n                    const timeoutId = setTimeout(()=>controller.abort(), 5000);\n                    const response = await fetch(service, {\n                        method: \"GET\",\n                        signal: controller.signal\n                    });\n                    clearTimeout(timeoutId);\n                    if (!response.ok) continue;\n                    let data;\n                    const contentType = response.headers.get(\"content-type\");\n                    if (contentType && contentType.includes(\"application/json\")) {\n                        data = await response.json();\n                        const ip = data.ip || data.origin || data.query;\n                        if (ip && isValidIPAddress(ip)) {\n                            console.log(\"✅ 成功获取公网IP: \".concat(ip, \" (来源: \").concat(service, \")\"));\n                            setRealPublicIP(ip);\n                            setResult({\n                                type: \"real-public-ip\",\n                                ip: ip,\n                                source: service,\n                                timestamp: new Date().toISOString()\n                            });\n                            return;\n                        }\n                    } else {\n                        const text = await response.text();\n                        const ip = text.trim();\n                        if (isValidIPAddress(ip)) {\n                            console.log(\"✅ 成功获取公网IP: \".concat(ip, \" (来源: \").concat(service, \")\"));\n                            setRealPublicIP(ip);\n                            setResult({\n                                type: \"real-public-ip\",\n                                ip: ip,\n                                source: service,\n                                timestamp: new Date().toISOString()\n                            });\n                            return;\n                        }\n                    }\n                } catch (error) {\n                    console.log(\"❌ 获取公网IP失败: \".concat(service, \" - \").concat(error));\n                    continue;\n                }\n            }\n            throw new Error(\"所有公网IP服务都无法访问\");\n        } catch (error) {\n            console.error(\"❌ [Frontend] 获取真实公网IP失败:\", error);\n            setResult({\n                type: \"error\",\n                error: error.message,\n                timestamp: new Date().toISOString()\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 验证IP地址格式\n    const isValidIPAddress = (ip)=>{\n        const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;\n        return ipv4Regex.test(ip);\n    };\n    // 测试获取当前IP位置\n    const testCurrentIP = async ()=>{\n        setLoading(true);\n        try {\n            console.log(\"\\uD83E\\uDDEA [Frontend Test] 开始测试当前IP获取...\");\n            console.log(\"\\uD83C\\uDF10 [Frontend Test] 当前环境信息:\", {\n                页面URL: window.location.href,\n                域名: window.location.hostname,\n                是否内网穿透: window.location.hostname.includes(\"ngrok\") || window.location.hostname.includes(\"tunnel\"),\n                用户代理: navigator.userAgent.substring(0, 100) + \"...\",\n                时间戳: new Date().toISOString()\n            });\n            console.log(\"\\uD83D\\uDCE4 [Frontend Test] 准备发送请求到: /api/v1/ip-location/current\");\n            const startTime = Date.now();\n            const response = await _lib_request__WEBPACK_IMPORTED_MODULE_2__[\"default\"].get(\"/api/v1/ip-location/current\");\n            const endTime = Date.now();\n            console.log(\"\\uD83D\\uDCE5 [Frontend Test] 请求完成:\", {\n                耗时: \"\".concat(endTime - startTime, \"ms\"),\n                响应状态: response.status,\n                响应数据: response.data,\n                时间戳: new Date().toISOString()\n            });\n            setResult({\n                type: \"current-ip\",\n                data: response.data,\n                requestTime: endTime - startTime,\n                timestamp: new Date().toISOString()\n            });\n        } catch (error) {\n            var _error_response, _error_response1, _error_response2;\n            console.error(\"❌ [Frontend Test] 当前IP测试失败:\", {\n                错误类型: error.name,\n                错误消息: error.message,\n                响应状态: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status,\n                响应数据: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.data,\n                完整错误: error,\n                时间戳: new Date().toISOString()\n            });\n            setResult({\n                type: \"error\",\n                error: error.message,\n                response: (_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.data,\n                timestamp: new Date().toISOString()\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 测试查询指定IP\n    const testQueryIP = async ()=>{\n        setLoading(true);\n        try {\n            console.log(\"\\uD83E\\uDDEA [Frontend] 开始测试IP查询...\");\n            const testIP = \"*******\"; // Google DNS\n            const response = await _lib_request__WEBPACK_IMPORTED_MODULE_2__[\"default\"].get(\"/api/v1/ip-location/query?ip=\".concat(testIP, \"&includeRisk=false\"));\n            console.log(\"\\uD83D\\uDCE5 [Frontend] 收到响应:\", response);\n            setResult({\n                type: \"query-ip\",\n                testIP,\n                data: response.data,\n                timestamp: new Date().toISOString()\n            });\n        } catch (error) {\n            var _error_response;\n            console.error(\"❌ [Frontend] 请求失败:\", error);\n            setResult({\n                type: \"error\",\n                error: error.message,\n                response: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data,\n                timestamp: new Date().toISOString()\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 测试登录接口（观察IP日志）\n    const testLoginIP = async ()=>{\n        setLoading(true);\n        try {\n            console.log(\"\\uD83E\\uDDEA [Frontend] 开始测试登录IP获取...\");\n            // 这里故意使用错误的登录信息，只是为了触发IP获取逻辑\n            const response = await _lib_request__WEBPACK_IMPORTED_MODULE_2__[\"default\"].post(\"/api/user-auth/password\", {\n                phone: \"12345678910\",\n                password: \"123456\"\n            });\n            console.log(\"\\uD83D\\uDCE5 [Frontend] 登录响应:\", response);\n            setResult({\n                type: \"login-test\",\n                data: response.data,\n                timestamp: new Date().toISOString()\n            });\n        } catch (error) {\n            var _error_response;\n            console.error(\"\\uD83D\\uDCDD [Frontend] 登录测试完成 (预期失败):\", error);\n            setResult({\n                type: \"login-test\",\n                error: error.message,\n                response: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data,\n                note: \"这是预期的失败，主要用于观察IP获取日志\",\n                timestamp: new Date().toISOString()\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"24px\",\n            maxWidth: \"1200px\",\n            margin: \"0 auto\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                level: 2,\n                children: \"\\uD83E\\uDDEA IP地址获取测试页面\"\n            }, void 0, false, {\n                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                lineNumber: 220,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                children: \"这个页面用于测试前端到后端的IP地址传递和获取功能。 请打开浏览器开发者工具的控制台，以及后端服务器的日志，观察IP获取过程。\"\n            }, void 0, false, {\n                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                lineNumber: 222,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                direction: \"vertical\",\n                size: \"large\",\n                style: {\n                    width: \"100%\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83C\\uDFAF 测试功能\",\n                        size: \"small\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                wrap: true,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        type: \"primary\",\n                                        loading: loading,\n                                        onClick: getRealPublicIP,\n                                        style: {\n                                            background: \"#52c41a\",\n                                            borderColor: \"#52c41a\"\n                                        },\n                                        children: \"\\uD83C\\uDF0D 获取真实公网IP\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        loading: loading,\n                                        onClick: testCurrentIP,\n                                        children: \"测试获取当前IP位置\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        loading: loading,\n                                        onClick: testQueryIP,\n                                        children: \"测试查询指定IP (*******)\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        loading: loading,\n                                        onClick: testLoginIP,\n                                        danger: true,\n                                        children: \"测试登录IP获取 (会失败)\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 11\n                            }, this),\n                            realPublicIP && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginTop: \"12px\",\n                                    padding: \"8px\",\n                                    background: \"#f6ffed\",\n                                    border: \"1px solid #b7eb8f\",\n                                    borderRadius: \"6px\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    strong: true,\n                                    style: {\n                                        color: \"#52c41a\"\n                                    },\n                                    children: [\n                                        \"\\uD83C\\uDF0D 你的真实公网IP: \",\n                                        realPublicIP\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 9\n                    }, this),\n                    result && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83D\\uDCCA 测试结果\",\n                        size: \"small\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                background: \"#f5f5f5\",\n                                padding: \"16px\",\n                                borderRadius: \"6px\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                style: {\n                                    margin: 0,\n                                    fontSize: \"12px\",\n                                    lineHeight: \"1.4\"\n                                },\n                                children: JSON.stringify(result, null, 2)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                lineNumber: 275,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                            lineNumber: 274,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 273,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 282,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83D\\uDCCB 观察要点\",\n                        size: \"small\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            direction: \"vertical\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            children: \"\\uD83C\\uDF10 前端中间件日志：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"查看浏览器控制台，观察 [Middleware] 标记的日志\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            children: \"\\uD83D\\uDDA5️ 后端IP提取日志：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 293,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"查看后端控制台，观察 [Backend] 标记的日志\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 294,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            children: \"\\uD83D\\uDD10 登录日志：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"查看后端控制台，观察 [LoginLog] 标记的日志\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            children: \"\\uD83D\\uDD0D 重点观察：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"IP地址是否从前端正确传递到后端，以及各个环节的IP获取情况\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 302,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                            lineNumber: 286,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 285,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83E\\uDD14 为什么本地开发获取到127.0.0.1？\",\n                        size: \"small\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            direction: \"vertical\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            style: {\n                                                color: \"#fa8c16\"\n                                            },\n                                            children: \"\\uD83C\\uDFE0 本地开发环境：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"浏览器 → localhost:3000 → 后端API，所有请求都来自本机，所以IP是127.0.0.1\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            style: {\n                                                color: \"#52c41a\"\n                                            },\n                                            children: \"\\uD83C\\uDF0D 生产环境：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"用户浏览器 → CDN/负载均衡 → Web服务器 → 后端API，能获取到真实公网IP\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 317,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            style: {\n                                                color: \"#1890ff\"\n                                            },\n                                            children: \"\\uD83C\\uDFAD 模拟解决方案：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"中间件已配置在开发环境使用模拟公网IP (**************) 进行测试\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 324,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            style: {\n                                                color: \"#722ed1\"\n                                            },\n                                            children: \"\\uD83E\\uDDEA 真实IP对比：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: '点击\"获取真实公网IP\"按钮，对比你的真实公网IP和后端获取的IP'\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                            lineNumber: 311,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 310,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83C\\uDF0D 当前环境信息\",\n                        size: \"small\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            direction: \"vertical\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    children: [\n                                        \"浏览器 User-Agent: \",\n                                         true ? navigator.userAgent : 0\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    children: [\n                                        \"当前时间: \",\n                                        new Date().toISOString()\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 338,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    children: [\n                                        \"页面URL: \",\n                                         true ? window.location.href : 0\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 339,\n                                    columnNumber: 13\n                                }, this),\n                                 true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    style: {\n                                        color: window.location.hostname.includes(\"ngrok\") || window.location.hostname.includes(\"tunnel\") ? \"#52c41a\" : \"#fa8c16\",\n                                        fontWeight: \"bold\"\n                                    },\n                                    children: [\n                                        \"访问方式: \",\n                                        window.location.hostname.includes(\"ngrok\") || window.location.hostname.includes(\"tunnel\") ? \"\\uD83C\\uDF10 内网穿透访问 (可获取真实IP)\" : \"\\uD83C\\uDFE0 本地访问 (模拟IP)\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                            lineNumber: 336,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 335,\n                        columnNumber: 9\n                    }, this),\n                     true && !window.location.hostname.includes(\"ngrok\") && !window.location.hostname.includes(\"tunnel\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83D\\uDE80 想要测试真实IP获取？\",\n                        size: \"small\",\n                        style: {\n                            borderColor: \"#52c41a\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            direction: \"vertical\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    strong: true,\n                                    style: {\n                                        color: \"#52c41a\"\n                                    },\n                                    children: \"使用内网穿透获取真实IP：\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 359,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        background: \"#f6ffed\",\n                                        padding: \"12px\",\n                                        borderRadius: \"6px\",\n                                        border: \"1px solid #b7eb8f\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            code: true,\n                                            children: \"1. 安装ngrok: npm install -g ngrok\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 361,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 361,\n                                            columnNumber: 67\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            code: true,\n                                            children: \"2. 穿透前端: ngrok http 3000\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 59\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            code: true,\n                                            children: \"3. 访问ngrok提供的公网地址\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 52\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            code: true,\n                                            children: \"4. 重新测试IP获取功能\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 364,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 360,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    type: \"secondary\",\n                                    children: \"通过内网穿透，你可以模拟真实的生产环境，获取到真实的公网IP地址！\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 366,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                            lineNumber: 358,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 357,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                lineNumber: 227,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n        lineNumber: 219,\n        columnNumber: 5\n    }, this);\n}\n_s(TestIPPage, \"X1Msb4W44QQRH4Yvzh/LKJZfd50=\");\n_c = TestIPPage;\nvar _c;\n$RefreshReg$(_c, \"TestIPPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/test-ip/page.tsx\n"));

/***/ })

});