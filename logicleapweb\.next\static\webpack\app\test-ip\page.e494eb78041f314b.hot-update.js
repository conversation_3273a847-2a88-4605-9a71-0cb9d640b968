"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/test-ip/page",{

/***/ "(app-pages-browser)/./app/test-ip/page.tsx":
/*!******************************!*\
  !*** ./app/test-ip/page.tsx ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ TestIPPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Divider,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/typography/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Divider,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/space/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Divider,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/card/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Divider,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Divider,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/divider/index.js\");\n/* harmony import */ var _lib_request__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/request */ \"(app-pages-browser)/./lib/request.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst { Title, Text, Paragraph } = _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_3__[\"default\"];\nfunction TestIPPage() {\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [result, setResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [realPublicIP, setRealPublicIP] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [logs, setLogs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [debugInfo, setDebugInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 添加日志的辅助函数\n    const addLog = (message, data)=>{\n        const timestamp = new Date().toLocaleTimeString();\n        const logEntry = \"[\".concat(timestamp, \"] \").concat(message);\n        // 输出到控制台\n        if (data) {\n            console.log(message, data);\n        } else {\n            console.log(message);\n        }\n        // 添加到页面日志\n        setLogs((prev)=>[\n                ...prev.slice(-19),\n                logEntry\n            ]); // 保留最近20条日志\n    };\n    // 清空日志\n    const clearLogs = ()=>{\n        setLogs([]);\n        console.clear();\n        addLog(\"\\uD83E\\uDDF9 [Frontend] 日志已清空\");\n    };\n    // 诊断内网穿透问题\n    const diagnoseTunnel = async ()=>{\n        setLoading(true);\n        try {\n            var _data_summary, _data_summary1, _data_summary2, _data_summary3, _data_summary4;\n            addLog(\"\\uD83D\\uDD0D [Frontend Diagnose] 开始诊断内网穿透问题...\");\n            const response = await fetch(\"/api/debug/headers\");\n            const data = await response.json();\n            addLog(\"\\uD83D\\uDD0D [Frontend Diagnose] 服务器调试信息:\", data);\n            setDebugInfo(data);\n            // 分析结果\n            const analysis = {\n                访问方式: data.accessType,\n                检测到的IP: (_data_summary = data.summary) === null || _data_summary === void 0 ? void 0 : _data_summary.detectedIp,\n                是否真实IP: (_data_summary1 = data.summary) === null || _data_summary1 === void 0 ? void 0 : _data_summary1.isRealIp,\n                IP相关头部数量: (_data_summary2 = data.summary) === null || _data_summary2 === void 0 ? void 0 : _data_summary2.ipRelatedHeaders,\n                主要问题: ((_data_summary3 = data.summary) === null || _data_summary3 === void 0 ? void 0 : _data_summary3.detectedIp) === \"127.0.0.1\" ? \"内网穿透未正确转发真实IP\" : \"正常\",\n                建议: ((_data_summary4 = data.summary) === null || _data_summary4 === void 0 ? void 0 : _data_summary4.detectedIp) === \"127.0.0.1\" ? \"尝试其他内网穿透服务或检查穿透配置\" : \"当前配置正常\"\n            };\n            addLog(\"\\uD83D\\uDCCA [Frontend Diagnose] 诊断分析结果:\", analysis);\n            setResult({\n                type: \"tunnel-diagnose\",\n                data: data,\n                analysis: analysis,\n                timestamp: new Date().toISOString()\n            });\n        } catch (error) {\n            addLog(\"❌ [Frontend Diagnose] 诊断失败:\", error);\n            setResult({\n                type: \"error\",\n                error: error.message,\n                timestamp: new Date().toISOString()\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 页面加载时输出环境信息\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        var _navigator_connection, _navigator_connection1;\n        addLog(\"\\uD83C\\uDF10 [Frontend Init] 测试页面初始化...\");\n        console.log(\"\\uD83D\\uDDA5️ [Frontend Init] 浏览器环境信息:\", {\n            页面信息: {\n                URL: window.location.href,\n                域名: window.location.hostname,\n                端口: window.location.port,\n                协议: window.location.protocol,\n                路径: window.location.pathname\n            },\n            访问方式: {\n                是否本地访问: window.location.hostname === \"localhost\",\n                是否内网穿透: window.location.hostname.includes(\"ngrok\") || window.location.hostname.includes(\"tunnel\"),\n                访问类型: window.location.hostname === \"localhost\" ? \"本地开发\" : window.location.hostname.includes(\"ngrok\") ? \"ngrok穿透\" : window.location.hostname.includes(\"tunnel\") ? \"其他穿透\" : \"未知\"\n            },\n            浏览器信息: {\n                用户代理: navigator.userAgent,\n                语言: navigator.language,\n                平台: navigator.platform,\n                在线状态: navigator.onLine\n            },\n            网络信息: {\n                连接类型: ((_navigator_connection = navigator.connection) === null || _navigator_connection === void 0 ? void 0 : _navigator_connection.effectiveType) || \"未知\",\n                网络状态: ((_navigator_connection1 = navigator.connection) === null || _navigator_connection1 === void 0 ? void 0 : _navigator_connection1.downlink) ? \"\".concat(navigator.connection.downlink, \"Mbps\") : \"未知\"\n            },\n            时间信息: {\n                本地时间: new Date().toISOString(),\n                时区: Intl.DateTimeFormat().resolvedOptions().timeZone,\n                时区偏移: new Date().getTimezoneOffset()\n            }\n        });\n        // 输出预期的测试流程\n        console.log(\"\\uD83D\\uDCCB [Frontend Init] 测试流程说明:\", {\n            测试目标: \"IP地址获取和传递功能验证\",\n            测试步骤: [\n                \"1. 获取真实公网IP (通过第三方API)\",\n                \"2. 测试当前IP位置获取 (后端API)\",\n                \"3. 测试指定IP查询 (*******)\",\n                \"4. 测试登录IP记录 (模拟登录失败)\"\n            ],\n            观察要点: [\n                \"前端请求日志 (\\uD83D\\uDCE4 [Frontend Request])\",\n                \"前端响应日志 (\\uD83D\\uDCE5 [Frontend Response])\",\n                \"中间件IP处理 (\\uD83C\\uDF10 [Middleware])\",\n                \"后端IP提取 (\\uD83D\\uDDA5️ [Backend])\",\n                \"登录IP记录 (\\uD83D\\uDD10 [LoginLog])\"\n            ],\n            预期结果: {\n                本地访问: \"IP为模拟值 (**************)\",\n                穿透访问: \"IP为真实公网IP\",\n                地理位置: \"根据IP解析出对应位置\"\n            }\n        });\n    }, []);\n    // 获取真实的公网IP地址\n    const getRealPublicIP = async ()=>{\n        setLoading(true);\n        try {\n            console.log(\"\\uD83C\\uDF0D [Frontend] 开始获取真实公网IP...\");\n            // 使用多个IP查询服务，提高成功率\n            const ipServices = [\n                \"https://api.ipify.org?format=json\",\n                \"https://ipapi.co/json/\",\n                \"https://httpbin.org/ip\",\n                \"https://api.ip.sb/ip\",\n                \"https://ifconfig.me/ip\",\n                \"https://icanhazip.com\"\n            ];\n            for (const service of ipServices){\n                try {\n                    console.log(\"\\uD83D\\uDD0D 尝试获取公网IP: \".concat(service));\n                    const controller = new AbortController();\n                    const timeoutId = setTimeout(()=>controller.abort(), 5000);\n                    const response = await fetch(service, {\n                        method: \"GET\",\n                        signal: controller.signal\n                    });\n                    clearTimeout(timeoutId);\n                    if (!response.ok) continue;\n                    let data;\n                    const contentType = response.headers.get(\"content-type\");\n                    if (contentType && contentType.includes(\"application/json\")) {\n                        data = await response.json();\n                        const ip = data.ip || data.origin || data.query;\n                        if (ip && isValidIPAddress(ip)) {\n                            console.log(\"✅ 成功获取公网IP: \".concat(ip, \" (来源: \").concat(service, \")\"));\n                            setRealPublicIP(ip);\n                            setResult({\n                                type: \"real-public-ip\",\n                                ip: ip,\n                                source: service,\n                                timestamp: new Date().toISOString()\n                            });\n                            return;\n                        }\n                    } else {\n                        const text = await response.text();\n                        const ip = text.trim();\n                        if (isValidIPAddress(ip)) {\n                            console.log(\"✅ 成功获取公网IP: \".concat(ip, \" (来源: \").concat(service, \")\"));\n                            setRealPublicIP(ip);\n                            setResult({\n                                type: \"real-public-ip\",\n                                ip: ip,\n                                source: service,\n                                timestamp: new Date().toISOString()\n                            });\n                            return;\n                        }\n                    }\n                } catch (error) {\n                    console.log(\"❌ 获取公网IP失败: \".concat(service, \" - \").concat(error));\n                    continue;\n                }\n            }\n            throw new Error(\"所有公网IP服务都无法访问\");\n        } catch (error) {\n            console.error(\"❌ [Frontend] 获取真实公网IP失败:\", error);\n            setResult({\n                type: \"error\",\n                error: error.message,\n                timestamp: new Date().toISOString()\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 验证IP地址格式\n    const isValidIPAddress = (ip)=>{\n        const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;\n        return ipv4Regex.test(ip);\n    };\n    // 测试获取当前IP位置\n    const testCurrentIP = async ()=>{\n        setLoading(true);\n        try {\n            addLog(\"\\uD83E\\uDDEA [Frontend Test] 开始测试当前IP获取...\");\n            addLog(\"\\uD83C\\uDF10 [Frontend Test] 当前环境信息:\", {\n                页面URL: window.location.href,\n                域名: window.location.hostname,\n                是否内网穿透: window.location.hostname.includes(\"ngrok\") || window.location.hostname.includes(\"tunnel\"),\n                用户代理: navigator.userAgent.substring(0, 100) + \"...\",\n                时间戳: new Date().toISOString()\n            });\n            console.log(\"\\uD83D\\uDCE4 [Frontend Test] 准备发送请求到: /api/v1/ip-location/current\");\n            const startTime = Date.now();\n            const response = await _lib_request__WEBPACK_IMPORTED_MODULE_2__[\"default\"].get(\"/api/v1/ip-location/current\");\n            const endTime = Date.now();\n            console.log(\"\\uD83D\\uDCE5 [Frontend Test] 请求完成:\", {\n                耗时: \"\".concat(endTime - startTime, \"ms\"),\n                响应状态: response.status,\n                响应数据: response.data,\n                时间戳: new Date().toISOString()\n            });\n            setResult({\n                type: \"current-ip\",\n                data: response.data,\n                requestTime: endTime - startTime,\n                timestamp: new Date().toISOString()\n            });\n        } catch (error) {\n            var _error_response, _error_response1, _error_response2;\n            console.error(\"❌ [Frontend Test] 当前IP测试失败:\", {\n                错误类型: error.name,\n                错误消息: error.message,\n                响应状态: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status,\n                响应数据: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.data,\n                完整错误: error,\n                时间戳: new Date().toISOString()\n            });\n            setResult({\n                type: \"error\",\n                error: error.message,\n                response: (_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.data,\n                timestamp: new Date().toISOString()\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 测试查询指定IP\n    const testQueryIP = async ()=>{\n        setLoading(true);\n        try {\n            const testIP = \"*******\"; // Google DNS\n            console.log(\"\\uD83E\\uDDEA [Frontend Test] 开始测试IP查询...\");\n            console.log(\"\\uD83C\\uDFAF [Frontend Test] 查询参数:\", {\n                目标IP: testIP,\n                IP类型: \"Google DNS服务器\",\n                预期位置: \"美国\",\n                包含风险评估: false,\n                时间戳: new Date().toISOString()\n            });\n            const queryUrl = \"/api/v1/ip-location/query?ip=\".concat(testIP, \"&includeRisk=false\");\n            console.log(\"\\uD83D\\uDCE4 [Frontend Test] 准备发送查询请求:\", queryUrl);\n            const startTime = Date.now();\n            const response = await _lib_request__WEBPACK_IMPORTED_MODULE_2__[\"default\"].get(queryUrl);\n            const endTime = Date.now();\n            console.log(\"\\uD83D\\uDCE5 [Frontend Test] IP查询完成:\", {\n                查询IP: testIP,\n                耗时: \"\".concat(endTime - startTime, \"ms\"),\n                响应状态: response.status,\n                地理位置: response.data ? {\n                    国家: response.data.country,\n                    省份: response.data.province,\n                    城市: response.data.city,\n                    运营商: response.data.isp,\n                    置信度: response.data.confidence\n                } : \"无数据\",\n                完整响应: response.data,\n                时间戳: new Date().toISOString()\n            });\n            setResult({\n                type: \"query-ip\",\n                testIP,\n                data: response.data,\n                requestTime: endTime - startTime,\n                timestamp: new Date().toISOString()\n            });\n        } catch (error) {\n            var _error_response, _error_response1, _error_response2;\n            console.error(\"❌ [Frontend Test] IP查询失败:\", {\n                查询IP: \"*******\",\n                错误类型: error.name,\n                错误消息: error.message,\n                响应状态: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status,\n                响应数据: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.data,\n                可能原因: [\n                    \"后端服务未启动\",\n                    \"IP解析服务异常\",\n                    \"网络连接问题\"\n                ],\n                时间戳: new Date().toISOString()\n            });\n            setResult({\n                type: \"error\",\n                error: error.message,\n                response: (_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.data,\n                timestamp: new Date().toISOString()\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 测试登录接口（观察IP日志）\n    const testLoginIP = async ()=>{\n        setLoading(true);\n        try {\n            console.log(\"\\uD83E\\uDDEA [Frontend Test] 开始测试登录IP获取...\");\n            console.log(\"\\uD83D\\uDD10 [Frontend Test] 登录测试说明:\", {\n                目的: \"观察登录时的IP获取和记录过程\",\n                预期结果: \"登录失败（使用错误凭据）\",\n                观察重点: [\n                    \"IP地址获取\",\n                    \"登录日志记录\",\n                    \"错误处理\"\n                ],\n                测试凭据: {\n                    手机号: \"12345678910\",\n                    密码: \"123456 (错误密码)\"\n                },\n                时间戳: new Date().toISOString()\n            });\n            console.log(\"\\uD83D\\uDCE4 [Frontend Test] 发送登录请求...\");\n            const startTime = Date.now();\n            // 这里故意使用错误的登录信息，只是为了触发IP获取逻辑\n            const response = await _lib_request__WEBPACK_IMPORTED_MODULE_2__[\"default\"].post(\"/api/user-auth/password\", {\n                phone: \"12345678910\",\n                password: \"123456\"\n            });\n            const endTime = Date.now();\n            console.log(\"\\uD83D\\uDCE5 [Frontend Test] 登录响应 (意外成功):\", {\n                耗时: \"\".concat(endTime - startTime, \"ms\"),\n                响应状态: response.status,\n                响应数据: response.data,\n                注意: \"这不应该成功，请检查后端验证逻辑\",\n                时间戳: new Date().toISOString()\n            });\n            setResult({\n                type: \"login-test\",\n                data: response.data,\n                requestTime: endTime - startTime,\n                timestamp: new Date().toISOString()\n            });\n        } catch (error) {\n            var _error_response, _error_response1, _error_response2;\n            console.log(\"\\uD83D\\uDCDD [Frontend Test] 登录测试完成 (预期失败):\", {\n                错误类型: error.name,\n                错误消息: error.message,\n                响应状态: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status,\n                响应数据: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.data,\n                分析: {\n                    是否预期失败: true,\n                    失败原因: \"使用了错误的登录凭据\",\n                    IP获取状态: \"应该已触发IP获取和日志记录\",\n                    后续检查: \"查看后端控制台的 [LoginLog] 日志\"\n                },\n                时间戳: new Date().toISOString()\n            });\n            setResult({\n                type: \"login-test\",\n                error: error.message,\n                response: (_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.data,\n                note: \"这是预期的失败，主要用于观察IP获取日志\",\n                timestamp: new Date().toISOString()\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"24px\",\n            maxWidth: \"1200px\",\n            margin: \"0 auto\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                level: 2,\n                children: \"\\uD83E\\uDDEA IP地址获取测试页面\"\n            }, void 0, false, {\n                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                lineNumber: 416,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                children: \"这个页面用于测试前端到后端的IP地址传递和获取功能。 请打开浏览器开发者工具的控制台，以及后端服务器的日志，观察IP获取过程。\"\n            }, void 0, false, {\n                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                lineNumber: 418,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                direction: \"vertical\",\n                size: \"large\",\n                style: {\n                    width: \"100%\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83C\\uDFAF 测试功能\",\n                        size: \"small\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                wrap: true,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        type: \"primary\",\n                                        loading: loading,\n                                        onClick: getRealPublicIP,\n                                        style: {\n                                            background: \"#52c41a\",\n                                            borderColor: \"#52c41a\"\n                                        },\n                                        children: \"\\uD83C\\uDF0D 获取真实公网IP\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                        lineNumber: 428,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        loading: loading,\n                                        onClick: testCurrentIP,\n                                        children: \"测试获取当前IP位置\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                        lineNumber: 437,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        loading: loading,\n                                        onClick: testQueryIP,\n                                        children: \"测试查询指定IP (*******)\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                        lineNumber: 444,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        loading: loading,\n                                        onClick: testLoginIP,\n                                        danger: true,\n                                        children: \"测试登录IP获取 (会失败)\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                        lineNumber: 451,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        onClick: clearLogs,\n                                        style: {\n                                            marginLeft: \"12px\"\n                                        },\n                                        children: \"\\uD83E\\uDDF9 清空日志\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                        lineNumber: 459,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        loading: loading,\n                                        onClick: diagnoseTunnel,\n                                        style: {\n                                            marginLeft: \"12px\",\n                                            borderColor: \"#fa8c16\",\n                                            color: \"#fa8c16\"\n                                        },\n                                        children: \"\\uD83D\\uDD0D 诊断穿透问题\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                        lineNumber: 466,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                lineNumber: 427,\n                                columnNumber: 11\n                            }, this),\n                            realPublicIP && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginTop: \"12px\",\n                                    padding: \"8px\",\n                                    background: \"#f6ffed\",\n                                    border: \"1px solid #b7eb8f\",\n                                    borderRadius: \"6px\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    strong: true,\n                                    style: {\n                                        color: \"#52c41a\"\n                                    },\n                                    children: [\n                                        \"\\uD83C\\uDF0D 你的真实公网IP: \",\n                                        realPublicIP\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 477,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                lineNumber: 476,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 426,\n                        columnNumber: 9\n                    }, this),\n                    result && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83D\\uDCCA 测试结果\",\n                        size: \"small\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                background: \"#f5f5f5\",\n                                padding: \"16px\",\n                                borderRadius: \"6px\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                style: {\n                                    margin: 0,\n                                    fontSize: \"12px\",\n                                    lineHeight: \"1.4\"\n                                },\n                                children: JSON.stringify(result, null, 2)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                lineNumber: 486,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                            lineNumber: 485,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 484,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83D\\uDCDD 实时前端日志\",\n                        size: \"small\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    background: \"#000\",\n                                    color: \"#00ff00\",\n                                    padding: \"12px\",\n                                    borderRadius: \"6px\",\n                                    fontFamily: \"Monaco, Consolas, monospace\",\n                                    fontSize: \"12px\",\n                                    maxHeight: \"300px\",\n                                    overflowY: \"auto\"\n                                },\n                                children: logs.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        color: \"#666\"\n                                    },\n                                    children: \"等待日志输出...\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 506,\n                                    columnNumber: 15\n                                }, this) : logs.map((log, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            marginBottom: \"2px\"\n                                        },\n                                        children: log\n                                    }, index, false, {\n                                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                        lineNumber: 509,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                lineNumber: 495,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginTop: \"8px\",\n                                    fontSize: \"12px\",\n                                    color: \"#666\"\n                                },\n                                children: \"\\uD83D\\uDCA1 提示：这里显示前端日志，完整日志请查看浏览器控制台\"\n                            }, void 0, false, {\n                                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                lineNumber: 515,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 494,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 520,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83D\\uDCCB 观察要点\",\n                        size: \"small\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            direction: \"vertical\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            children: \"\\uD83C\\uDF10 前端中间件日志：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 526,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"查看浏览器控制台，观察 [Middleware] 标记的日志\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 527,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 525,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            children: \"\\uD83D\\uDDA5️ 后端IP提取日志：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 531,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"查看后端控制台，观察 [Backend] 标记的日志\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 532,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 530,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            children: \"\\uD83D\\uDD10 登录日志：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 536,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"查看后端控制台，观察 [LoginLog] 标记的日志\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 537,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 535,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            children: \"\\uD83D\\uDD0D 重点观察：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 541,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"IP地址是否从前端正确传递到后端，以及各个环节的IP获取情况\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 542,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 540,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                            lineNumber: 524,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 523,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83E\\uDD14 为什么本地开发获取到127.0.0.1？\",\n                        size: \"small\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            direction: \"vertical\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            style: {\n                                                color: \"#fa8c16\"\n                                            },\n                                            children: \"\\uD83C\\uDFE0 本地开发环境：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 551,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"浏览器 → localhost:3000 → 后端API，所有请求都来自本机，所以IP是127.0.0.1\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 552,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 550,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            style: {\n                                                color: \"#52c41a\"\n                                            },\n                                            children: \"\\uD83C\\uDF0D 生产环境：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 556,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"用户浏览器 → CDN/负载均衡 → Web服务器 → 后端API，能获取到真实公网IP\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 557,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 555,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            style: {\n                                                color: \"#1890ff\"\n                                            },\n                                            children: \"\\uD83C\\uDFAD 模拟解决方案：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 561,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"中间件已配置在开发环境使用模拟公网IP (**************) 进行测试\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 562,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 560,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            style: {\n                                                color: \"#722ed1\"\n                                            },\n                                            children: \"\\uD83E\\uDDEA 真实IP对比：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 566,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: '点击\"获取真实公网IP\"按钮，对比你的真实公网IP和后端获取的IP'\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 567,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 565,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                            lineNumber: 549,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 548,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83C\\uDF0D 当前环境信息\",\n                        size: \"small\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            direction: \"vertical\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    children: [\n                                        \"浏览器 User-Agent: \",\n                                         true ? navigator.userAgent : 0\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 575,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    children: [\n                                        \"当前时间: \",\n                                        new Date().toISOString()\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 576,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    children: [\n                                        \"页面URL: \",\n                                         true ? window.location.href : 0\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 577,\n                                    columnNumber: 13\n                                }, this),\n                                 true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    style: {\n                                        color: window.location.hostname.includes(\"ngrok\") || window.location.hostname.includes(\"tunnel\") ? \"#52c41a\" : \"#fa8c16\",\n                                        fontWeight: \"bold\"\n                                    },\n                                    children: [\n                                        \"访问方式: \",\n                                        window.location.hostname.includes(\"ngrok\") || window.location.hostname.includes(\"tunnel\") ? \"\\uD83C\\uDF10 内网穿透访问 (可获取真实IP)\" : \"\\uD83C\\uDFE0 本地访问 (模拟IP)\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 579,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                            lineNumber: 574,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 573,\n                        columnNumber: 9\n                    }, this),\n                     true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: window.location.hostname.includes(\"vicp.fun\") ? \"\\uD83C\\uDF10 当前使用vicp.fun内网穿透\" : window.location.hostname.includes(\"ngrok\") ? \"\\uD83C\\uDF10 当前使用ngrok内网穿透\" : window.location.hostname.includes(\"tunnel\") ? \"\\uD83C\\uDF10 当前使用内网穿透\" : \"\\uD83D\\uDE80 想要测试真实IP获取？\",\n                        size: \"small\",\n                        style: {\n                            borderColor: window.location.hostname.includes(\"vicp.fun\") || window.location.hostname.includes(\"ngrok\") || window.location.hostname.includes(\"tunnel\") ? \"#fa8c16\" : \"#52c41a\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            direction: \"vertical\",\n                            children: window.location.hostname.includes(\"vicp.fun\") ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                        strong: true,\n                                        style: {\n                                            color: \"#fa8c16\"\n                                        },\n                                        children: \"⚠️ vicp.fun穿透IP问题说明：\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                        lineNumber: 612,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            background: \"#fff7e6\",\n                                            padding: \"12px\",\n                                            borderRadius: \"6px\",\n                                            border: \"1px solid #ffd591\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                children: \"• vicp.fun等免费穿透服务可能不会转发真实的客户端IP\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                                lineNumber: 614,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                                lineNumber: 614,\n                                                columnNumber: 65\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                children: \"• 这是穿透服务的限制，不是代码问题\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                                lineNumber: 615,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                                lineNumber: 615,\n                                                columnNumber: 52\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                children: '• 点击\"\\uD83D\\uDD0D 诊断穿透问题\"按钮查看详细信息'\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                                lineNumber: 616,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                                lineNumber: 616,\n                                                columnNumber: 57\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                children: \"• 建议尝试其他穿透服务或直接部署到云服务器测试\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                                lineNumber: 617,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                        lineNumber: 613,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                        type: \"secondary\",\n                                        children: \"如果仍显示127.0.0.1，这是正常现象，生产环境会正确获取用户IP。\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                        lineNumber: 619,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true) : window.location.hostname.includes(\"ngrok\") || window.location.hostname.includes(\"tunnel\") ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                        strong: true,\n                                        style: {\n                                            color: \"#fa8c16\"\n                                        },\n                                        children: \"\\uD83C\\uDF10 内网穿透环境检测：\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                        lineNumber: 623,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            background: \"#fff7e6\",\n                                            padding: \"12px\",\n                                            borderRadius: \"6px\",\n                                            border: \"1px solid #ffd591\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                children: \"• 当前通过内网穿透访问\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                                lineNumber: 625,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                                lineNumber: 625,\n                                                columnNumber: 46\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                children: '• 如果IP仍是127.0.0.1，点击\"\\uD83D\\uDD0D 诊断穿透问题\"'\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                                lineNumber: 626,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                                lineNumber: 626,\n                                                columnNumber: 65\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                children: \"• 不同穿透服务的IP转发能力不同\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                                lineNumber: 627,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                        lineNumber: 624,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                        strong: true,\n                                        style: {\n                                            color: \"#52c41a\"\n                                        },\n                                        children: \"使用内网穿透获取真实IP：\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                        lineNumber: 632,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            background: \"#f6ffed\",\n                                            padding: \"12px\",\n                                            borderRadius: \"6px\",\n                                            border: \"1px solid #b7eb8f\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                code: true,\n                                                children: \"1. 安装ngrok: npm install -g ngrok\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                                lineNumber: 634,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                                lineNumber: 634,\n                                                columnNumber: 71\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                code: true,\n                                                children: \"2. 穿透前端: ngrok http 3000\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                                lineNumber: 635,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                                lineNumber: 635,\n                                                columnNumber: 63\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                code: true,\n                                                children: \"3. 访问ngrok提供的公网地址\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                                lineNumber: 636,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                                lineNumber: 636,\n                                                columnNumber: 56\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                code: true,\n                                                children: \"4. 重新测试IP获取功能\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                                lineNumber: 637,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                        lineNumber: 633,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                        type: \"secondary\",\n                                        children: \"通过内网穿透，你可以模拟真实的生产环境，获取到真实的公网IP地址！\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                        lineNumber: 639,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                            lineNumber: 609,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 595,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                lineNumber: 423,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n        lineNumber: 415,\n        columnNumber: 5\n    }, this);\n}\n_s(TestIPPage, \"UKdI/aIOtKUiZXV4GhGqcrMqaTI=\");\n_c = TestIPPage;\nvar _c;\n$RefreshReg$(_c, \"TestIPPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/test-ip/page.tsx\n"));

/***/ })

});