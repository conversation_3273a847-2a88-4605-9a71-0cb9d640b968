import { HttpResponse } from './http-response.interface';
import { LoggerService } from '../../common/logger/logger.service';
export interface LogOptions {
    path?: string;
    userId?: number;
    requestId?: string;
    executionTime?: number;
    context?: string;
    enableLog?: boolean;
}
export declare class HttpResponseResultService {
    private readonly loggerService?;
    constructor(loggerService?: LoggerService | undefined);
    success<T = any>(data?: T, msg?: string, code?: number, logOptions?: LogOptions): HttpResponse<T>;
    error<T = any>(msg?: string, data?: T, code?: number, logOptions?: LogOptions): HttpResponse<T>;
    custom<T = any>(code: number, msg: string, data?: T, logOptions?: LogOptions): HttpResponse<T>;
    private logSuccess;
    private logError;
    private buildLogMessage;
    private shouldLog;
}
