"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/test-ip/page",{

/***/ "(app-pages-browser)/./app/test-ip/page.tsx":
/*!******************************!*\
  !*** ./app/test-ip/page.tsx ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ TestIPPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Divider,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/typography/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Divider,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/space/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Divider,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/card/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Divider,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Divider,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/divider/index.js\");\n/* harmony import */ var _lib_request__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/request */ \"(app-pages-browser)/./lib/request.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst { Title, Text, Paragraph } = _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_3__[\"default\"];\nfunction TestIPPage() {\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [result, setResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [realPublicIP, setRealPublicIP] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [logs, setLogs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 添加日志的辅助函数\n    const addLog = (message, data)=>{\n        const timestamp = new Date().toLocaleTimeString();\n        const logEntry = \"[\".concat(timestamp, \"] \").concat(message);\n        // 输出到控制台\n        if (data) {\n            console.log(message, data);\n        } else {\n            console.log(message);\n        }\n        // 添加到页面日志\n        setLogs((prev)=>[\n                ...prev.slice(-19),\n                logEntry\n            ]); // 保留最近20条日志\n    };\n    // 清空日志\n    const clearLogs = ()=>{\n        setLogs([]);\n        console.clear();\n        addLog(\"\\uD83E\\uDDF9 [Frontend] 日志已清空\");\n    };\n    // 页面加载时输出环境信息\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        var _navigator_connection, _navigator_connection1;\n        addLog(\"\\uD83C\\uDF10 [Frontend Init] 测试页面初始化...\");\n        console.log(\"\\uD83D\\uDDA5️ [Frontend Init] 浏览器环境信息:\", {\n            页面信息: {\n                URL: window.location.href,\n                域名: window.location.hostname,\n                端口: window.location.port,\n                协议: window.location.protocol,\n                路径: window.location.pathname\n            },\n            访问方式: {\n                是否本地访问: window.location.hostname === \"localhost\",\n                是否内网穿透: window.location.hostname.includes(\"ngrok\") || window.location.hostname.includes(\"tunnel\"),\n                访问类型: window.location.hostname === \"localhost\" ? \"本地开发\" : window.location.hostname.includes(\"ngrok\") ? \"ngrok穿透\" : window.location.hostname.includes(\"tunnel\") ? \"其他穿透\" : \"未知\"\n            },\n            浏览器信息: {\n                用户代理: navigator.userAgent,\n                语言: navigator.language,\n                平台: navigator.platform,\n                在线状态: navigator.onLine\n            },\n            网络信息: {\n                连接类型: ((_navigator_connection = navigator.connection) === null || _navigator_connection === void 0 ? void 0 : _navigator_connection.effectiveType) || \"未知\",\n                网络状态: ((_navigator_connection1 = navigator.connection) === null || _navigator_connection1 === void 0 ? void 0 : _navigator_connection1.downlink) ? \"\".concat(navigator.connection.downlink, \"Mbps\") : \"未知\"\n            },\n            时间信息: {\n                本地时间: new Date().toISOString(),\n                时区: Intl.DateTimeFormat().resolvedOptions().timeZone,\n                时区偏移: new Date().getTimezoneOffset()\n            }\n        });\n        // 输出预期的测试流程\n        console.log(\"\\uD83D\\uDCCB [Frontend Init] 测试流程说明:\", {\n            测试目标: \"IP地址获取和传递功能验证\",\n            测试步骤: [\n                \"1. 获取真实公网IP (通过第三方API)\",\n                \"2. 测试当前IP位置获取 (后端API)\",\n                \"3. 测试指定IP查询 (*******)\",\n                \"4. 测试登录IP记录 (模拟登录失败)\"\n            ],\n            观察要点: [\n                \"前端请求日志 (\\uD83D\\uDCE4 [Frontend Request])\",\n                \"前端响应日志 (\\uD83D\\uDCE5 [Frontend Response])\",\n                \"中间件IP处理 (\\uD83C\\uDF10 [Middleware])\",\n                \"后端IP提取 (\\uD83D\\uDDA5️ [Backend])\",\n                \"登录IP记录 (\\uD83D\\uDD10 [LoginLog])\"\n            ],\n            预期结果: {\n                本地访问: \"IP为模拟值 (**************)\",\n                穿透访问: \"IP为真实公网IP\",\n                地理位置: \"根据IP解析出对应位置\"\n            }\n        });\n    }, []);\n    // 获取真实的公网IP地址\n    const getRealPublicIP = async ()=>{\n        setLoading(true);\n        try {\n            console.log(\"\\uD83C\\uDF0D [Frontend] 开始获取真实公网IP...\");\n            // 使用多个IP查询服务，提高成功率\n            const ipServices = [\n                \"https://api.ipify.org?format=json\",\n                \"https://ipapi.co/json/\",\n                \"https://httpbin.org/ip\",\n                \"https://api.ip.sb/ip\",\n                \"https://ifconfig.me/ip\",\n                \"https://icanhazip.com\"\n            ];\n            for (const service of ipServices){\n                try {\n                    console.log(\"\\uD83D\\uDD0D 尝试获取公网IP: \".concat(service));\n                    const controller = new AbortController();\n                    const timeoutId = setTimeout(()=>controller.abort(), 5000);\n                    const response = await fetch(service, {\n                        method: \"GET\",\n                        signal: controller.signal\n                    });\n                    clearTimeout(timeoutId);\n                    if (!response.ok) continue;\n                    let data;\n                    const contentType = response.headers.get(\"content-type\");\n                    if (contentType && contentType.includes(\"application/json\")) {\n                        data = await response.json();\n                        const ip = data.ip || data.origin || data.query;\n                        if (ip && isValidIPAddress(ip)) {\n                            console.log(\"✅ 成功获取公网IP: \".concat(ip, \" (来源: \").concat(service, \")\"));\n                            setRealPublicIP(ip);\n                            setResult({\n                                type: \"real-public-ip\",\n                                ip: ip,\n                                source: service,\n                                timestamp: new Date().toISOString()\n                            });\n                            return;\n                        }\n                    } else {\n                        const text = await response.text();\n                        const ip = text.trim();\n                        if (isValidIPAddress(ip)) {\n                            console.log(\"✅ 成功获取公网IP: \".concat(ip, \" (来源: \").concat(service, \")\"));\n                            setRealPublicIP(ip);\n                            setResult({\n                                type: \"real-public-ip\",\n                                ip: ip,\n                                source: service,\n                                timestamp: new Date().toISOString()\n                            });\n                            return;\n                        }\n                    }\n                } catch (error) {\n                    console.log(\"❌ 获取公网IP失败: \".concat(service, \" - \").concat(error));\n                    continue;\n                }\n            }\n            throw new Error(\"所有公网IP服务都无法访问\");\n        } catch (error) {\n            console.error(\"❌ [Frontend] 获取真实公网IP失败:\", error);\n            setResult({\n                type: \"error\",\n                error: error.message,\n                timestamp: new Date().toISOString()\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 验证IP地址格式\n    const isValidIPAddress = (ip)=>{\n        const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;\n        return ipv4Regex.test(ip);\n    };\n    // 测试获取当前IP位置\n    const testCurrentIP = async ()=>{\n        setLoading(true);\n        try {\n            addLog(\"\\uD83E\\uDDEA [Frontend Test] 开始测试当前IP获取...\");\n            addLog(\"\\uD83C\\uDF10 [Frontend Test] 当前环境信息:\", {\n                页面URL: window.location.href,\n                域名: window.location.hostname,\n                是否内网穿透: window.location.hostname.includes(\"ngrok\") || window.location.hostname.includes(\"tunnel\"),\n                用户代理: navigator.userAgent.substring(0, 100) + \"...\",\n                时间戳: new Date().toISOString()\n            });\n            console.log(\"\\uD83D\\uDCE4 [Frontend Test] 准备发送请求到: /api/v1/ip-location/current\");\n            const startTime = Date.now();\n            const response = await _lib_request__WEBPACK_IMPORTED_MODULE_2__[\"default\"].get(\"/api/v1/ip-location/current\");\n            const endTime = Date.now();\n            console.log(\"\\uD83D\\uDCE5 [Frontend Test] 请求完成:\", {\n                耗时: \"\".concat(endTime - startTime, \"ms\"),\n                响应状态: response.status,\n                响应数据: response.data,\n                时间戳: new Date().toISOString()\n            });\n            setResult({\n                type: \"current-ip\",\n                data: response.data,\n                requestTime: endTime - startTime,\n                timestamp: new Date().toISOString()\n            });\n        } catch (error) {\n            var _error_response, _error_response1, _error_response2;\n            console.error(\"❌ [Frontend Test] 当前IP测试失败:\", {\n                错误类型: error.name,\n                错误消息: error.message,\n                响应状态: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status,\n                响应数据: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.data,\n                完整错误: error,\n                时间戳: new Date().toISOString()\n            });\n            setResult({\n                type: \"error\",\n                error: error.message,\n                response: (_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.data,\n                timestamp: new Date().toISOString()\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 测试查询指定IP\n    const testQueryIP = async ()=>{\n        setLoading(true);\n        try {\n            const testIP = \"*******\"; // Google DNS\n            console.log(\"\\uD83E\\uDDEA [Frontend Test] 开始测试IP查询...\");\n            console.log(\"\\uD83C\\uDFAF [Frontend Test] 查询参数:\", {\n                目标IP: testIP,\n                IP类型: \"Google DNS服务器\",\n                预期位置: \"美国\",\n                包含风险评估: false,\n                时间戳: new Date().toISOString()\n            });\n            const queryUrl = \"/api/v1/ip-location/query?ip=\".concat(testIP, \"&includeRisk=false\");\n            console.log(\"\\uD83D\\uDCE4 [Frontend Test] 准备发送查询请求:\", queryUrl);\n            const startTime = Date.now();\n            const response = await _lib_request__WEBPACK_IMPORTED_MODULE_2__[\"default\"].get(queryUrl);\n            const endTime = Date.now();\n            console.log(\"\\uD83D\\uDCE5 [Frontend Test] IP查询完成:\", {\n                查询IP: testIP,\n                耗时: \"\".concat(endTime - startTime, \"ms\"),\n                响应状态: response.status,\n                地理位置: response.data ? {\n                    国家: response.data.country,\n                    省份: response.data.province,\n                    城市: response.data.city,\n                    运营商: response.data.isp,\n                    置信度: response.data.confidence\n                } : \"无数据\",\n                完整响应: response.data,\n                时间戳: new Date().toISOString()\n            });\n            setResult({\n                type: \"query-ip\",\n                testIP,\n                data: response.data,\n                requestTime: endTime - startTime,\n                timestamp: new Date().toISOString()\n            });\n        } catch (error) {\n            var _error_response, _error_response1, _error_response2;\n            console.error(\"❌ [Frontend Test] IP查询失败:\", {\n                查询IP: \"*******\",\n                错误类型: error.name,\n                错误消息: error.message,\n                响应状态: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status,\n                响应数据: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.data,\n                可能原因: [\n                    \"后端服务未启动\",\n                    \"IP解析服务异常\",\n                    \"网络连接问题\"\n                ],\n                时间戳: new Date().toISOString()\n            });\n            setResult({\n                type: \"error\",\n                error: error.message,\n                response: (_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.data,\n                timestamp: new Date().toISOString()\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 测试登录接口（观察IP日志）\n    const testLoginIP = async ()=>{\n        setLoading(true);\n        try {\n            console.log(\"\\uD83E\\uDDEA [Frontend Test] 开始测试登录IP获取...\");\n            console.log(\"\\uD83D\\uDD10 [Frontend Test] 登录测试说明:\", {\n                目的: \"观察登录时的IP获取和记录过程\",\n                预期结果: \"登录失败（使用错误凭据）\",\n                观察重点: [\n                    \"IP地址获取\",\n                    \"登录日志记录\",\n                    \"错误处理\"\n                ],\n                测试凭据: {\n                    手机号: \"12345678910\",\n                    密码: \"123456 (错误密码)\"\n                },\n                时间戳: new Date().toISOString()\n            });\n            console.log(\"\\uD83D\\uDCE4 [Frontend Test] 发送登录请求...\");\n            const startTime = Date.now();\n            // 这里故意使用错误的登录信息，只是为了触发IP获取逻辑\n            const response = await _lib_request__WEBPACK_IMPORTED_MODULE_2__[\"default\"].post(\"/api/user-auth/password\", {\n                phone: \"12345678901\",\n                password: \"123456\"\n            });\n            const endTime = Date.now();\n            console.log(\"\\uD83D\\uDCE5 [Frontend Test] 登录响应 (意外成功):\", {\n                耗时: \"\".concat(endTime - startTime, \"ms\"),\n                响应状态: response.status,\n                响应数据: response.data,\n                注意: \"这不应该成功，请检查后端验证逻辑\",\n                时间戳: new Date().toISOString()\n            });\n            setResult({\n                type: \"login-test\",\n                data: response.data,\n                requestTime: endTime - startTime,\n                timestamp: new Date().toISOString()\n            });\n        } catch (error) {\n            var _error_response, _error_response1, _error_response2;\n            console.log(\"\\uD83D\\uDCDD [Frontend Test] 登录测试完成 (预期失败):\", {\n                错误类型: error.name,\n                错误消息: error.message,\n                响应状态: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status,\n                响应数据: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.data,\n                分析: {\n                    是否预期失败: true,\n                    失败原因: \"使用了错误的登录凭据\",\n                    IP获取状态: \"应该已触发IP获取和日志记录\",\n                    后续检查: \"查看后端控制台的 [LoginLog] 日志\"\n                },\n                时间戳: new Date().toISOString()\n            });\n            setResult({\n                type: \"login-test\",\n                error: error.message,\n                response: (_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.data,\n                note: \"这是预期的失败，主要用于观察IP获取日志\",\n                timestamp: new Date().toISOString()\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"24px\",\n            maxWidth: \"1200px\",\n            margin: \"0 auto\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                level: 2,\n                children: \"\\uD83E\\uDDEA IP地址获取测试页面\"\n            }, void 0, false, {\n                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                lineNumber: 371,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                children: \"这个页面用于测试前端到后端的IP地址传递和获取功能。 请打开浏览器开发者工具的控制台，以及后端服务器的日志，观察IP获取过程。\"\n            }, void 0, false, {\n                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                lineNumber: 373,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                direction: \"vertical\",\n                size: \"large\",\n                style: {\n                    width: \"100%\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83C\\uDFAF 测试功能\",\n                        size: \"small\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                wrap: true,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        type: \"primary\",\n                                        loading: loading,\n                                        onClick: getRealPublicIP,\n                                        style: {\n                                            background: \"#52c41a\",\n                                            borderColor: \"#52c41a\"\n                                        },\n                                        children: \"\\uD83C\\uDF0D 获取真实公网IP\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                        lineNumber: 383,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        loading: loading,\n                                        onClick: testCurrentIP,\n                                        children: \"测试获取当前IP位置\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        loading: loading,\n                                        onClick: testQueryIP,\n                                        children: \"测试查询指定IP (*******)\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                        lineNumber: 399,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        loading: loading,\n                                        onClick: testLoginIP,\n                                        danger: true,\n                                        children: \"测试登录IP获取 (会失败)\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                        lineNumber: 406,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        onClick: clearLogs,\n                                        style: {\n                                            marginLeft: \"12px\"\n                                        },\n                                        children: \"\\uD83E\\uDDF9 清空日志\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                        lineNumber: 414,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                lineNumber: 382,\n                                columnNumber: 11\n                            }, this),\n                            realPublicIP && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginTop: \"12px\",\n                                    padding: \"8px\",\n                                    background: \"#f6ffed\",\n                                    border: \"1px solid #b7eb8f\",\n                                    borderRadius: \"6px\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    strong: true,\n                                    style: {\n                                        color: \"#52c41a\"\n                                    },\n                                    children: [\n                                        \"\\uD83C\\uDF0D 你的真实公网IP: \",\n                                        realPublicIP\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 424,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                lineNumber: 423,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 381,\n                        columnNumber: 9\n                    }, this),\n                    result && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83D\\uDCCA 测试结果\",\n                        size: \"small\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                background: \"#f5f5f5\",\n                                padding: \"16px\",\n                                borderRadius: \"6px\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                style: {\n                                    margin: 0,\n                                    fontSize: \"12px\",\n                                    lineHeight: \"1.4\"\n                                },\n                                children: JSON.stringify(result, null, 2)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                lineNumber: 433,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                            lineNumber: 432,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 431,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83D\\uDCDD 实时前端日志\",\n                        size: \"small\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    background: \"#000\",\n                                    color: \"#00ff00\",\n                                    padding: \"12px\",\n                                    borderRadius: \"6px\",\n                                    fontFamily: \"Monaco, Consolas, monospace\",\n                                    fontSize: \"12px\",\n                                    maxHeight: \"300px\",\n                                    overflowY: \"auto\"\n                                },\n                                children: logs.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        color: \"#666\"\n                                    },\n                                    children: \"等待日志输出...\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 453,\n                                    columnNumber: 15\n                                }, this) : logs.map((log, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            marginBottom: \"2px\"\n                                        },\n                                        children: log\n                                    }, index, false, {\n                                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                        lineNumber: 456,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                lineNumber: 442,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginTop: \"8px\",\n                                    fontSize: \"12px\",\n                                    color: \"#666\"\n                                },\n                                children: \"\\uD83D\\uDCA1 提示：这里显示前端日志，完整日志请查看浏览器控制台\"\n                            }, void 0, false, {\n                                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                lineNumber: 462,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 441,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 467,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83D\\uDCCB 观察要点\",\n                        size: \"small\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            direction: \"vertical\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            children: \"\\uD83C\\uDF10 前端中间件日志：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 473,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"查看浏览器控制台，观察 [Middleware] 标记的日志\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 474,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 472,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            children: \"\\uD83D\\uDDA5️ 后端IP提取日志：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 478,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"查看后端控制台，观察 [Backend] 标记的日志\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 479,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 477,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            children: \"\\uD83D\\uDD10 登录日志：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 483,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"查看后端控制台，观察 [LoginLog] 标记的日志\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 484,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 482,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            children: \"\\uD83D\\uDD0D 重点观察：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 488,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"IP地址是否从前端正确传递到后端，以及各个环节的IP获取情况\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 489,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 487,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                            lineNumber: 471,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 470,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83E\\uDD14 为什么本地开发获取到127.0.0.1？\",\n                        size: \"small\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            direction: \"vertical\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            style: {\n                                                color: \"#fa8c16\"\n                                            },\n                                            children: \"\\uD83C\\uDFE0 本地开发环境：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 498,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"浏览器 → localhost:3000 → 后端API，所有请求都来自本机，所以IP是127.0.0.1\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 499,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 497,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            style: {\n                                                color: \"#52c41a\"\n                                            },\n                                            children: \"\\uD83C\\uDF0D 生产环境：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 503,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"用户浏览器 → CDN/负载均衡 → Web服务器 → 后端API，能获取到真实公网IP\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 504,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 502,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            style: {\n                                                color: \"#1890ff\"\n                                            },\n                                            children: \"\\uD83C\\uDFAD 模拟解决方案：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 508,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"中间件已配置在开发环境使用模拟公网IP (**************) 进行测试\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 509,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 507,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            style: {\n                                                color: \"#722ed1\"\n                                            },\n                                            children: \"\\uD83E\\uDDEA 真实IP对比：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 513,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: '点击\"获取真实公网IP\"按钮，对比你的真实公网IP和后端获取的IP'\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 514,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 512,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                            lineNumber: 496,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 495,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83C\\uDF0D 当前环境信息\",\n                        size: \"small\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            direction: \"vertical\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    children: [\n                                        \"浏览器 User-Agent: \",\n                                         true ? navigator.userAgent : 0\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 522,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    children: [\n                                        \"当前时间: \",\n                                        new Date().toISOString()\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 523,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    children: [\n                                        \"页面URL: \",\n                                         true ? window.location.href : 0\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 524,\n                                    columnNumber: 13\n                                }, this),\n                                 true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    style: {\n                                        color: window.location.hostname.includes(\"ngrok\") || window.location.hostname.includes(\"tunnel\") ? \"#52c41a\" : \"#fa8c16\",\n                                        fontWeight: \"bold\"\n                                    },\n                                    children: [\n                                        \"访问方式: \",\n                                        window.location.hostname.includes(\"ngrok\") || window.location.hostname.includes(\"tunnel\") ? \"\\uD83C\\uDF10 内网穿透访问 (可获取真实IP)\" : \"\\uD83C\\uDFE0 本地访问 (模拟IP)\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 526,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                            lineNumber: 521,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 520,\n                        columnNumber: 9\n                    }, this),\n                     true && !window.location.hostname.includes(\"ngrok\") && !window.location.hostname.includes(\"tunnel\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83D\\uDE80 想要测试真实IP获取？\",\n                        size: \"small\",\n                        style: {\n                            borderColor: \"#52c41a\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            direction: \"vertical\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    strong: true,\n                                    style: {\n                                        color: \"#52c41a\"\n                                    },\n                                    children: \"使用内网穿透获取真实IP：\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 544,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        background: \"#f6ffed\",\n                                        padding: \"12px\",\n                                        borderRadius: \"6px\",\n                                        border: \"1px solid #b7eb8f\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            code: true,\n                                            children: \"1. 安装ngrok: npm install -g ngrok\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 546,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 546,\n                                            columnNumber: 67\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            code: true,\n                                            children: \"2. 穿透前端: ngrok http 3000\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 547,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 547,\n                                            columnNumber: 59\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            code: true,\n                                            children: \"3. 访问ngrok提供的公网地址\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 548,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 548,\n                                            columnNumber: 52\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            code: true,\n                                            children: \"4. 重新测试IP获取功能\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 549,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 545,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    type: \"secondary\",\n                                    children: \"通过内网穿透，你可以模拟真实的生产环境，获取到真实的公网IP地址！\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 551,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                            lineNumber: 543,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 542,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                lineNumber: 378,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n        lineNumber: 370,\n        columnNumber: 5\n    }, this);\n}\n_s(TestIPPage, \"YdJxZSDzzBKbremxKAojQG9Ufuw=\");\n_c = TestIPPage;\nvar _c;\n$RefreshReg$(_c, \"TestIPPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/test-ip/page.tsx\n"));

/***/ })

});