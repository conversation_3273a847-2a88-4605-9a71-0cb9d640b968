"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConsoleOverrideService = void 0;
const common_1 = require("@nestjs/common");
const winston_1 = require("winston");
const nest_winston_1 = require("nest-winston");
const common_2 = require("@nestjs/common");
let ConsoleOverrideService = class ConsoleOverrideService {
    logger;
    originalConsole;
    constructor(logger) {
        this.logger = logger;
    }
    onModuleInit() {
        this.overrideConsole();
    }
    overrideConsole() {
        this.originalConsole = {
            log: console.log,
            error: console.error,
            warn: console.warn,
            info: console.info,
            debug: console.debug,
            trace: console.trace,
            assert: console.assert,
            dir: console.dir,
            table: console.table,
            group: console.group,
            groupCollapsed: console.groupCollapsed,
            groupEnd: console.groupEnd,
            time: console.time,
            timeEnd: console.timeEnd,
            count: console.count,
            clear: console.clear
        };
        console.log = (...args) => {
            const message = this.formatMessage(args);
            this.logger.info(message, { context: 'Console' });
            this.callOriginalInDev('log', args);
        };
        console.error = (...args) => {
            const message = this.formatMessage(args);
            this.logger.error(message, { context: 'Console' });
            this.callOriginalInDev('error', args);
        };
        console.warn = (...args) => {
            const message = this.formatMessage(args);
            this.logger.warn(message, { context: 'Console' });
            this.callOriginalInDev('warn', args);
        };
        console.info = (...args) => {
            const message = this.formatMessage(args);
            this.logger.info(message, { context: 'Console' });
            this.callOriginalInDev('info', args);
        };
        console.debug = (...args) => {
            const message = this.formatMessage(args);
            this.logger.debug(message, { context: 'Console' });
            this.callOriginalInDev('debug', args);
        };
        console.trace = (...args) => {
            const message = this.formatMessage(args);
            const stack = new Error().stack;
            this.logger.debug(message, { context: 'Console', trace: stack });
            this.callOriginalInDev('trace', args);
        };
        console.assert = (condition, ...args) => {
            if (!condition) {
                const message = this.formatMessage(args);
                this.logger.error(`Assertion failed: ${message}`, { context: 'Console' });
            }
            this.callOriginalInDev('assert', [condition, ...args]);
        };
        console.dir = (obj, options) => {
            const message = JSON.stringify(obj, null, 2);
            this.logger.info(`Dir: ${message}`, { context: 'Console' });
            this.callOriginalInDev('dir', [obj, options]);
        };
        console.table = (data) => {
            const message = JSON.stringify(data, null, 2);
            this.logger.info(`Table: ${message}`, { context: 'Console' });
            this.callOriginalInDev('table', [data]);
        };
        console.group = (...args) => {
            const message = this.formatMessage(args);
            this.logger.info(`Group: ${message}`, { context: 'Console' });
            this.callOriginalInDev('group', args);
        };
        console.groupCollapsed = (...args) => {
            const message = this.formatMessage(args);
            this.logger.info(`GroupCollapsed: ${message}`, { context: 'Console' });
            this.callOriginalInDev('groupCollapsed', args);
        };
        console.groupEnd = () => {
            this.logger.info('GroupEnd', { context: 'Console' });
            this.callOriginalInDev('groupEnd', []);
        };
        const timers = new Map();
        console.time = (label = 'default') => {
            timers.set(label, Date.now());
            this.logger.info(`Timer started: ${label}`, { context: 'Console' });
            this.callOriginalInDev('time', [label]);
        };
        console.timeEnd = (label = 'default') => {
            const startTime = timers.get(label);
            if (startTime) {
                const duration = Date.now() - startTime;
                timers.delete(label);
                this.logger.info(`Timer ended: ${label} - ${duration}ms`, { context: 'Console' });
            }
            this.callOriginalInDev('timeEnd', [label]);
        };
        const counters = new Map();
        console.count = (label = 'default') => {
            const count = (counters.get(label) || 0) + 1;
            counters.set(label, count);
            this.logger.info(`Count: ${label} - ${count}`, { context: 'Console' });
            this.callOriginalInDev('count', [label]);
        };
        console.clear = () => {
            this.logger.info('Console cleared', { context: 'Console' });
            this.callOriginalInDev('clear', []);
        };
        this.logger.info('Console methods have been overridden to use Winston logger', { context: 'ConsoleOverride' });
    }
    formatMessage(args) {
        return args.map(arg => {
            if (typeof arg === 'object' && arg !== null) {
                try {
                    return JSON.stringify(arg, null, 2);
                }
                catch (error) {
                    return String(arg);
                }
            }
            return String(arg);
        }).join(' ');
    }
    callOriginalInDev(method, args) {
        if (process.env.NODE_ENV === 'dev' || process.env.NODE_ENV === 'development') {
            if (this.originalConsole[method]) {
                this.originalConsole[method](...args);
            }
        }
    }
    restoreConsole() {
        if (this.originalConsole) {
            Object.keys(this.originalConsole).forEach(method => {
                console[method] = this.originalConsole[method];
            });
            this.logger.info('Console methods have been restored to original', { context: 'ConsoleOverride' });
        }
    }
    getOriginalConsole() {
        return this.originalConsole;
    }
};
exports.ConsoleOverrideService = ConsoleOverrideService;
exports.ConsoleOverrideService = ConsoleOverrideService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_2.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER)),
    __metadata("design:paramtypes", [winston_1.Logger])
], ConsoleOverrideService);
//# sourceMappingURL=console-override.service.js.map