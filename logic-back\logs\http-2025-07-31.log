2025-07-31 11:47:05.144 [INFO] [Business] Business IpLocationUtil.initialize {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","module":"IpLocationUtil","action":"initialize","data":"{\"message\":\"ip2region初始化成功\"}"}
2025-07-31 11:47:05.717 [INFO] [Startup] Application is starting... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","nodeVersion":"v20.17.0","platform":"win32","port":8003}
2025-07-31 11:47:08.210 [INFO] [Ip2RegionService] IP2Region服务初始化成功 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.212 [INFO] [ConsoleOverride] Console methods have been overridden to use Winston logger {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.562 [INFO] [AppModule] App模块初始化... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.563 [INFO] [AppModule] 通过App模块手动触发模板更新... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.849 [INFO] [NestFactory] Starting Nest application... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.850 [INFO] [PaymentTemplateService] 已加载模板: payment_fail {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.850 [INFO] [PaymentTemplateService] 已加载模板: payment_success {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.851 [INFO] [PaymentTemplateService] 已加载模板: refund_fail {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.851 [INFO] [PaymentTemplateService] 已加载模板: refund_success {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.852 [INFO] [PaymentTemplateService] 已加载 4 个模板 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.852 [INFO] [InstanceLoader] WebModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.853 [INFO] [InstanceLoader] YamlModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.853 [INFO] [InstanceLoader] AliServiceModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.854 [INFO] [InstanceLoader] ScratchModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.855 [INFO] [InstanceLoader] UtilModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.856 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.857 [INFO] [InstanceLoader] WebWeixinScanModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.858 [INFO] [InstanceLoader] UserCommonLocationModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.859 [INFO] [InstanceLoader] CommonServicesModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.859 [INFO] [InstanceLoader] WinstonModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.860 [INFO] [InstanceLoader] ConfigHostModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.860 [INFO] [InstanceLoader] HttpResponseResultModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.861 [INFO] [InstanceLoader] WebTemplateFolderModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.861 [INFO] [InstanceLoader] ZwwModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.862 [INFO] [InstanceLoader] DiscoveryModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.862 [INFO] [InstanceLoader] LoggerModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.863 [INFO] [InstanceLoader] ScratchConfigModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.863 [INFO] [InstanceLoader] AiProvidersConfigModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.864 [INFO] [InstanceLoader] JwtModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.864 [INFO] [InstanceLoader] AliConfigModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.865 [INFO] [InstanceLoader] DatabaseConfigModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.866 [INFO] [XunfeiSpeechRecognitionService] 初始化讯飞语音识别服务 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.866 [INFO] [XunfeiSpeechRecognitionService] AppID: 0292**** {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.867 [INFO] [XunfeiSpeechRecognitionService] WebSocket URL: ws://iat.xf-yun.com/v1 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.867 [INFO] [XunfeiVoiceprintRecognitionService] 初始化讯飞声纹识别服务 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.867 [INFO] [XunfeiVoiceprintRecognitionService] AppID: 4f01**** {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.868 [INFO] [XunfeiVoiceprintRecognitionService] API URL: https://api.xf-yun.com/v1/private/s782b4996 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.868 [INFO] [InstanceLoader] ConfigModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.868 [INFO] [InstanceLoader] ConfigModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.869 [INFO] [InstanceLoader] ScheduleModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.869 [INFO] [InstanceLoader] ScheduleModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.870 [INFO] [InstanceLoader] ScheduleModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.870 [INFO] [RedisSessionService] Redis会话存储服务已初始化 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.871 [INFO] [InstanceLoader] AliGreenModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.871 [INFO] [InstanceLoader] BullModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.871 [INFO] [InstanceLoader] MinimaxImageModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.872 [INFO] [InstanceLoader] ZhipuLlmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.872 [INFO] [InstanceLoader] BaiduImageEnhanceModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.873 [INFO] [InstanceLoader] AliyunImageScoreModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.873 [INFO] [InstanceLoader] AliyunSegmentImageModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.874 [INFO] [InstanceLoader] MinimaxTtsModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.874 [INFO] [InstanceLoader] AliSmsModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.874 [INFO] [AlipayStrategy] 初始化支付宝支付策略... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.875 [INFO] [AlipayStrategy] 支付宝支付策略初始化成功 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.876 [INFO] [WechatPayStrategy] 微信支付客户端初始化成功 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.877 [INFO] [InstanceLoader] RedisModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.877 [INFO] [InstanceLoader] AliQwenVisionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.877 [INFO] [InstanceLoader] AliyunExpressionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.878 [INFO] [InstanceLoader] AliyunFaceCompareModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.878 [INFO] [InstanceLoader] AliyunFaceRecognitionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.879 [INFO] [InstanceLoader] AliyunObjectDetectionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.879 [INFO] [InstanceLoader] XunfeiVoiceprintRecognitionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.879 [INFO] [InstanceLoader] SessionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.880 [INFO] [InstanceLoader] KeyManagementModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.880 [INFO] [InstanceLoader] AliyunStaticGestureRecognitionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.880 [INFO] [InstanceLoader] AliQwenTurboModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.881 [INFO] [InstanceLoader] QueueModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.881 [INFO] [EncryptionService] 【初始化】加密服务开始初始化... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.882 [WARN] [EncryptionService] 【初始化】获取RSA密钥对失败: RSA密钥对未初始化，请稍后重试，服务将在首次请求时重试 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.882 [INFO] [EncryptionService] 【初始化】加密服务初始化完成 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.884 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-31 11:47:08.886 [INFO] [KeyManagementService] 生成了新的RSA密钥对，ID: key-80867834，指纹: 80867834 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.887 [INFO] [InstanceLoader] WebSocketModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.889 [INFO] [EncryptionCleanupTask] 【初始化】会话清理任务已启动 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.890 [INFO] [InstanceLoader] OssModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.891 [INFO] [InstanceLoader] XunfeiSpeechRecognitionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.891 [INFO] [InstanceLoader] MysqlModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.892 [INFO] [InstanceLoader] RouterGuardModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.892 [INFO] [InstanceLoader] AliOssModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.893 [INFO] [InstanceLoader] EncryptModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.893 [INFO] [InstanceLoader] TypeOrmCoreModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.894 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.895 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.895 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.896 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.896 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.897 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.897 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.898 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.898 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.899 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.899 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.899 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.900 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.900 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.901 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.901 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.901 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.902 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.902 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.902 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.903 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.903 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.904 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.904 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.904 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.905 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.905 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.905 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.906 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.906 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.907 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.907 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.908 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.908 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.908 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.909 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.909 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.910 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.910 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.910 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.911 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.911 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.912 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.912 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.913 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.913 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.913 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.914 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.914 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.915 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.915 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.915 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.916 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.916 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.916 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.917 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.917 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.918 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.918 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.918 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.919 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.919 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.919 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.920 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.920 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.921 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.921 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.922 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.922 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.923 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.923 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.924 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.924 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.925 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.926 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.926 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.926 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.927 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.927 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.928 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.928 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.929 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.929 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.929 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.930 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.930 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.933 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.933 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.934 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.934 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.935 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.936 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.937 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.938 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.938 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.939 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.939 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.940 [INFO] [InstanceLoader] TeacherAuditAttachmentModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.940 [INFO] [InstanceLoader] UserSrchImageModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.941 [INFO] [InstanceLoader] UserSrchEnhanceModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.941 [INFO] [InstanceLoader] UserSrchAudioModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.941 [INFO] [InstanceLoader] UserSrchImageSegmentModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.942 [INFO] [InstanceLoader] TrainImageModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.942 [INFO] [InstanceLoader] TrainPoseModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.943 [INFO] [InstanceLoader] TrainSoundModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.943 [INFO] [InstanceLoader] WebWorkAuditModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.944 [INFO] [InstanceLoader] UserRoleRelationModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.944 [INFO] [InstanceLoader] UserJoinRoleModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.944 [INFO] [InstanceLoader] TeacherTaskAssignmentModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.945 [INFO] [InstanceLoader] RolePermissionTemplatesModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.945 [INFO] [InstanceLoader] UserPointsModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.945 [INFO] [InstanceLoader] UserPackageModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.946 [INFO] [InstanceLoader] PackageInfoModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.946 [INFO] [InstanceLoader] UserPointsPermissionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.947 [INFO] [InstanceLoader] UserClassModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.947 [INFO] [InstanceLoader] UserSchoolModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.947 [INFO] [InstanceLoader] UserStudentModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.947 [INFO] [InstanceLoader] KeyPackageModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.948 [INFO] [InstanceLoader] UserRoleRelationModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.948 [INFO] [InstanceLoader] UserJoinRoleModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.949 [INFO] [InstanceLoader] RoleTemplateExtensionPermissionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.949 [INFO] [InstanceLoader] RoleTemplateBlockPermissionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.950 [INFO] [InstanceLoader] RoleTemplateFolderModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.950 [INFO] [InstanceLoader] RoleTemplateFolderJoinTemplateModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.951 [INFO] [InstanceLoader] TableJoingModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.951 [INFO] [InstanceLoader] UserWorkInfoModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.952 [INFO] [InstanceLoader] WorkModelModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.952 [INFO] [InstanceLoader] TeacherTaskAssignmentModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.953 [INFO] [InstanceLoader] TeacherTaskModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.953 [INFO] [InstanceLoader] UserImageInfoModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.953 [INFO] [InstanceLoader] TaskSelfAssessmentItemModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.954 [INFO] [InstanceLoader] UserPasswordResetRequestModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.954 [INFO] [InstanceLoader] UserImageEnhanceModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.955 [INFO] [InstanceLoader] UserVoiceInfoModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.955 [INFO] [InstanceLoader] UserSchoolRelationModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.956 [INFO] [InstanceLoader] AnnouncementReadRecordModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.956 [INFO] [InstanceLoader] AnnouncementModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.957 [INFO] [InstanceLoader] AnnouncementAuditModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.957 [INFO] [InstanceLoader] SpaceCarouselMapModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.958 [INFO] [InstanceLoader] CarouselAuditModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.958 [INFO] [InstanceLoader] WebRoleModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.958 [INFO] [InstanceLoader] BlockModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.959 [INFO] [InstanceLoader] ExtensionsModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.959 [INFO] [InstanceLoader] ExtensionPermissionsModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.959 [INFO] [InstanceLoader] BlockPermissionsModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.960 [INFO] [InstanceLoader] UserReportModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.960 [INFO] [InstanceLoader] DocModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.961 [INFO] [InstanceLoader] KeyPackageRecordModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.961 [INFO] [InstanceLoader] UserImageSegmentModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.961 [INFO] [InstanceLoader] AttachmentModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.962 [INFO] [InstanceLoader] SelfAssessmentItemModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.962 [INFO] [InstanceLoader] PackageOrderModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.962 [INFO] [InstanceLoader] PackagePricingModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.963 [INFO] [InstanceLoader] PaymentRecordModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.963 [INFO] [InstanceLoader] NotificationRecordModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.963 [INFO] [InstanceLoader] PaymentLogModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.964 [INFO] [InstanceLoader] PaymentOrderModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.964 [INFO] [InstanceLoader] PaymentRefundModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.964 [INFO] [InstanceLoader] UserLoginLogModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.965 [INFO] [InstanceLoader] TagModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.965 [INFO] [InstanceLoader] ActivityModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.965 [INFO] [InstanceLoader] ActivityTagModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.966 [INFO] [InstanceLoader] ActivityWorkModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.966 [INFO] [InstanceLoader] ImageTrainModelModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.967 [INFO] [InstanceLoader] PoseTrainModelModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.967 [INFO] [InstanceLoader] AudioTrainModelModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.968 [INFO] [InstanceLoader] UserPointsOfflineMessageModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.969 [INFO] [InstanceLoader] UserRolePermissionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.969 [INFO] [InstanceLoader] UserRoleModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.969 [INFO] [InstanceLoader] UserSchoolRelationModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.970 [INFO] [InstanceLoader] UserWorkLikeModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.970 [INFO] [InstanceLoader] ActivityAuditModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.970 [INFO] [InstanceLoader] ParticipationAuditModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.971 [INFO] [InstanceLoader] WorkAuditModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.971 [INFO] [InstanceLoader] StudentSelfAssessmentSubmissionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.971 [INFO] [InstanceLoader] VoiceprintGroupModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.972 [INFO] [InstanceLoader] VoiceprintFeatureModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.972 [INFO] [InstanceLoader] UserRoleModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.973 [INFO] [InstanceLoader] UserLoginLogModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.973 [INFO] [InstanceLoader] WebUserInfoModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.973 [INFO] [InstanceLoader] UserSrchWorkModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.974 [INFO] [InstanceLoader] UserSrchTaskModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.974 [INFO] [InstanceLoader] UserSchoolModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.976 [INFO] [InstanceLoader] UserImageInfoModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.977 [INFO] [InstanceLoader] UserInfoModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.977 [INFO] [InstanceLoader] AppModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.978 [INFO] [InstanceLoader] TeacherAuditModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.978 [INFO] [InstanceLoader] WebPackageModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.979 [INFO] [InstanceLoader] IpLocationModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.979 [INFO] [InstanceLoader] ActivitySubmitModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.979 [INFO] [InstanceLoader] ActivityEventsTaskModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.980 [INFO] [InstanceLoader] UserPointPackageModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.980 [INFO] [InstanceLoader] UserStudentModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.981 [INFO] [InstanceLoader] WeixinModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.981 [INFO] [InstanceLoader] WebAnnouncementModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.981 [INFO] [InstanceLoader] WebCarouselModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.982 [INFO] [InstanceLoader] WebDocModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.982 [INFO] [InstanceLoader] WebKeyPackageModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.982 [INFO] [InstanceLoader] UserClassModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.982 [INFO] [InstanceLoader] UserTagModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.983 [INFO] [InstanceLoader] WebActivityTagModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.983 [INFO] [InstanceLoader] WebActivityWorkModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.983 [INFO] [InstanceLoader] UserWorkLikeModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.984 [INFO] [InstanceLoader] TPSModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.984 [INFO] [InstanceLoader] UserWorkInfoModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.984 [INFO] [InstanceLoader] TeacherTaskModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.985 [INFO] [InstanceLoader] WebPermissionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.985 [INFO] [InstanceLoader] UserInfoModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.986 [INFO] [InstanceLoader] UserSrchTemplatesModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.986 [INFO] [InstanceLoader] WebReportModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.987 [INFO] [InstanceLoader] UserPasswordResetModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.987 [INFO] [InstanceLoader] WebWeixinScanModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.988 [INFO] [InstanceLoader] WeixinUtilsModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.988 [INFO] [InstanceLoader] PackageOrderBusinessModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.989 [INFO] [InstanceLoader] UserPointModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.989 [INFO] [InstanceLoader] WebActivityModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.989 [INFO] [InstanceLoader] UserAuthModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.990 [INFO] [InstanceLoader] WebEventsTaskModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.990 [INFO] [InstanceLoader] WeixinMessageModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.990 [INFO] [InstanceLoader] WebPointPermissionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.991 [INFO] [InstanceLoader] WebPointModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.991 [INFO] [InstanceLoader] PaymentModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.991 [INFO] [InstanceLoader] CourseModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.992 [INFO] [InstanceLoader] AiVisualRecognitionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.992 [INFO] [InstanceLoader] AiImageScoreModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.992 [INFO] [InstanceLoader] AiStaticGestureRecognitionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.993 [INFO] [InstanceLoader] AiExpressionRecognitionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.993 [INFO] [InstanceLoader] AiFaceCompareModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.993 [INFO] [InstanceLoader] AiFaceRecognitionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.994 [INFO] [InstanceLoader] AiSpeechRecognitionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.994 [INFO] [InstanceLoader] AiObjectDetectionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.994 [INFO] [InstanceLoader] AiTextDialogueModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.995 [INFO] [InstanceLoader] AiImageEnhanceModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.995 [INFO] [InstanceLoader] AiVoiceprintRecognitionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.995 [INFO] [InstanceLoader] AiSpeechSynthesisModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.996 [INFO] [InstanceLoader] AiImageGenerateModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.996 [INFO] [InstanceLoader] AiImageSegmentModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:08.997 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:47:08.997 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:47:08.998 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:47:08.998 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:47:08.999 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:47:08.999 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:47:08.999 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:47:09.000 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:47:09.000 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:47:09.001 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:47:09.001 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:47:09.002 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:47:09.002 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:47:09.002 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:47:09.003 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:47:09.003 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:47:09.003 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:47:09.004 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:47:09.004 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:47:09.005 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:47:09.006 [INFO] [WebSocketsController] WebSocketGateway subscribed to the "add_tab" message {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.006 [INFO] [WebSocketsController] WebSocketGateway subscribed to the "check_tab" message {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.007 [INFO] [WebSocketsController] WebSocketGateway subscribed to the "message_to_tab" message {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.007 [INFO] [RoutesResolver] AppController {/}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.007 [INFO] [RouterExplorer] Mapped {/, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.008 [INFO] [RoutesResolver] WorkAuditController {/api/work-audit}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.008 [INFO] [RouterExplorer] Mapped {/api/work-audit/listWithFilter, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.008 [INFO] [RouterExplorer] Mapped {/api/work-audit/listByWork/:workId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.009 [INFO] [RouterExplorer] Mapped {/api/work-audit, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.010 [INFO] [RouterExplorer] Mapped {/api/work-audit, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.010 [INFO] [RouterExplorer] Mapped {/api/work-audit/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.011 [INFO] [RouterExplorer] Mapped {/api/work-audit/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.012 [INFO] [RouterExplorer] Mapped {/api/work-audit/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.012 [INFO] [RoutesResolver] UserRoleRelationController {/api/user-role-relation}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.012 [INFO] [RouterExplorer] Mapped {/api/user-role-relation, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.013 [INFO] [RouterExplorer] Mapped {/api/user-role-relation/batch-assign-roles, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.013 [INFO] [RouterExplorer] Mapped {/api/user-role-relation/batch-assign-users, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.013 [INFO] [RouterExplorer] Mapped {/api/user-role-relation, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.014 [INFO] [RouterExplorer] Mapped {/api/user-role-relation/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.014 [INFO] [RouterExplorer] Mapped {/api/user-role-relation/role/:roleId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.020 [INFO] [RouterExplorer] Mapped {/api/user-role-relation/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.020 [INFO] [RouterExplorer] Mapped {/api/user-role-relation/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.021 [INFO] [RouterExplorer] Mapped {/api/user-role-relation/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.021 [INFO] [RouterExplorer] Mapped {/api/user-role-relation/user/:userId/role/:roleId, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.022 [INFO] [RouterExplorer] Mapped {/api/user-role-relation/user/:userId/all, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.022 [INFO] [RouterExplorer] Mapped {/api/user-role-relation/role/:roleId/all, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.023 [INFO] [RoutesResolver] UserJoinRoleController {/api/user-join-role}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.023 [INFO] [RouterExplorer] Mapped {/api/user-join-role/createUserJoinRole, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.023 [INFO] [RouterExplorer] Mapped {/api/user-join-role/batchCreateUserJoinRole, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.024 [INFO] [RouterExplorer] Mapped {/api/user-join-role/getUserRoleAndTemplateId, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.024 [INFO] [RouterExplorer] Mapped {/api/user-join-role, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.024 [INFO] [RouterExplorer] Mapped {/api/user-join-role, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.025 [INFO] [RouterExplorer] Mapped {/api/user-join-role/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.025 [INFO] [RouterExplorer] Mapped {/api/user-join-role/role/:roleId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.025 [INFO] [RouterExplorer] Mapped {/api/user-join-role/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.026 [INFO] [RouterExplorer] Mapped {/api/user-join-role/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.026 [INFO] [RouterExplorer] Mapped {/api/user-join-role/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.026 [INFO] [RouterExplorer] Mapped {/api/user-join-role/user/:userId/role/:roleId, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.027 [INFO] [RoutesResolver] TeacherTaskAssignmentController {/api/teacher-task-assignment}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.027 [INFO] [RouterExplorer] Mapped {/api/teacher-task-assignment/mark-read, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.027 [INFO] [RouterExplorer] Mapped {/api/teacher-task-assignment/return-revision, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.028 [INFO] [RouterExplorer] Mapped {/api/teacher-task-assignment/studentSubmitedWork, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.028 [INFO] [RouterExplorer] Mapped {/api/teacher-task-assignment/publish-to-class, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.028 [INFO] [RouterExplorer] Mapped {/api/teacher-task-assignment/grade, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.029 [INFO] [RouterExplorer] Mapped {/api/teacher-task-assignment/studentCommitWork, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.029 [INFO] [RouterExplorer] Mapped {/api/teacher-task-assignment/submitWork, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.030 [INFO] [RouterExplorer] Mapped {/api/teacher-task-assignment/workSubmissionsRecord, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.030 [INFO] [RouterExplorer] Mapped {/api/teacher-task-assignment, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.030 [INFO] [RouterExplorer] Mapped {/api/teacher-task-assignment, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.031 [INFO] [RouterExplorer] Mapped {/api/teacher-task-assignment/task/:taskId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.031 [INFO] [RouterExplorer] Mapped {/api/teacher-task-assignment/student/:studentId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.031 [INFO] [RouterExplorer] Mapped {/api/teacher-task-assignment/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.032 [INFO] [RouterExplorer] Mapped {/api/teacher-task-assignment/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.032 [INFO] [RouterExplorer] Mapped {/api/teacher-task-assignment/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.032 [INFO] [RouterExplorer] Mapped {/api/teacher-task-assignment/update-status, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.033 [INFO] [RoutesResolver] UserWorkInfoController {/api/user-work-info}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.033 [INFO] [RouterExplorer] Mapped {/api/user-work-info/reviewList, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.033 [INFO] [RouterExplorer] Mapped {/api/user-work-info/review, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.034 [INFO] [RouterExplorer] Mapped {/api/user-work-info/searchClassprojects, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.034 [INFO] [RouterExplorer] Mapped {/api/user-work-info, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.035 [INFO] [RouterExplorer] Mapped {/api/user-work-info, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.035 [INFO] [RouterExplorer] Mapped {/api/user-work-info/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.035 [INFO] [RouterExplorer] Mapped {/api/user-work-info/user/:userId/type/:type, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.036 [INFO] [RouterExplorer] Mapped {/api/user-work-info/public, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.036 [INFO] [RouterExplorer] Mapped {/api/user-work-info/class/:classId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.036 [INFO] [RouterExplorer] Mapped {/api/user-work-info/:id/view, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.037 [INFO] [RouterExplorer] Mapped {/api/user-work-info/:id/like, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.037 [INFO] [RouterExplorer] Mapped {/api/user-work-info/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.037 [INFO] [RouterExplorer] Mapped {/api/user-work-info/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.038 [INFO] [RouterExplorer] Mapped {/api/user-work-info/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.038 [INFO] [RouterExplorer] Mapped {/api/user-work-info/like/toggle, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.038 [INFO] [RouterExplorer] Mapped {/api/user-work-info/class/projects, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.039 [INFO] [RoutesResolver] UserWorkLikeController {/api/user-work-like}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.039 [INFO] [RouterExplorer] Mapped {/api/user-work-like/likeList, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.039 [INFO] [RouterExplorer] Mapped {/api/user-work-like, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.040 [INFO] [RouterExplorer] Mapped {/api/user-work-like, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.040 [INFO] [RouterExplorer] Mapped {/api/user-work-like/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.040 [INFO] [RouterExplorer] Mapped {/api/user-work-like/target/:targetId/type/:targetType, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.041 [INFO] [RouterExplorer] Mapped {/api/user-work-like/check, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.041 [INFO] [RouterExplorer] Mapped {/api/user-work-like/toggle, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.041 [INFO] [RouterExplorer] Mapped {/api/user-work-like/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.042 [INFO] [RouterExplorer] Mapped {/api/user-work-like/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.042 [INFO] [RouterExplorer] Mapped {/api/user-work-like/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.042 [INFO] [RoutesResolver] UserImageInfoController {/api/user-image-info}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.043 [INFO] [RouterExplorer] Mapped {/api/user-image-info, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.043 [INFO] [RouterExplorer] Mapped {/api/user-image-info, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.043 [INFO] [RouterExplorer] Mapped {/api/user-image-info/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.044 [INFO] [RouterExplorer] Mapped {/api/user-image-info/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.044 [INFO] [RouterExplorer] Mapped {/api/user-image-info/task/:taskId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.044 [INFO] [RouterExplorer] Mapped {/api/user-image-info/:id/view, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.044 [INFO] [RouterExplorer] Mapped {/api/user-image-info/:id/like, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.044 [INFO] [RouterExplorer] Mapped {/api/user-image-info/:id/status/:status, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.045 [INFO] [RouterExplorer] Mapped {/api/user-image-info/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.045 [INFO] [RouterExplorer] Mapped {/api/user-image-info/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.045 [INFO] [RouterExplorer] Mapped {/api/user-image-info/like/status, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.046 [INFO] [RoutesResolver] UserInfoController {/user-info}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.046 [INFO] [RouterExplorer] Mapped {/user-info/condition/search, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.046 [INFO] [RouterExplorer] Mapped {/user-info, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.047 [INFO] [RouterExplorer] Mapped {/user-info, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.047 [INFO] [RouterExplorer] Mapped {/user-info/search, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.047 [INFO] [RouterExplorer] Mapped {/user-info/phone/:phone, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.048 [INFO] [RouterExplorer] Mapped {/user-info/role/:roleId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.048 [INFO] [RouterExplorer] Mapped {/user-info/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.048 [INFO] [RouterExplorer] Mapped {/user-info/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.048 [INFO] [RouterExplorer] Mapped {/user-info/:id/points/:points, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.049 [INFO] [RouterExplorer] Mapped {/user-info/:id/role/:roleId, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.049 [INFO] [RouterExplorer] Mapped {/user-info/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.049 [INFO] [RoutesResolver] RolePermissionTemplatesController {/role-permission-templates}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.049 [INFO] [RouterExplorer] Mapped {/role-permission-templates, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.050 [INFO] [RouterExplorer] Mapped {/role-permission-templates, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.050 [INFO] [RouterExplorer] Mapped {/role-permission-templates/role/:roleId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.050 [INFO] [RouterExplorer] Mapped {/role-permission-templates/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.050 [INFO] [RouterExplorer] Mapped {/role-permission-templates/role/:roleId/default, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.051 [INFO] [RouterExplorer] Mapped {/role-permission-templates/official, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.051 [INFO] [RouterExplorer] Mapped {/role-permission-templates/:id/set-default, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.051 [INFO] [RouterExplorer] Mapped {/role-permission-templates/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.052 [INFO] [RouterExplorer] Mapped {/role-permission-templates/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.052 [INFO] [RouterExplorer] Mapped {/role-permission-templates/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.053 [INFO] [RoutesResolver] UserStudentController {/api/user-student}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.053 [INFO] [RouterExplorer] Mapped {/api/user-student/export, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.055 [INFO] [RouterExplorer] Mapped {/api/user-student/search, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.055 [INFO] [RouterExplorer] Mapped {/api/user-student/match-by-ids, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.056 [INFO] [RouterExplorer] Mapped {/api/user-student/batch-info, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.056 [INFO] [RouterExplorer] Mapped {/api/user-student, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.057 [INFO] [RouterExplorer] Mapped {/api/user-student, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.057 [INFO] [RouterExplorer] Mapped {/api/user-student/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.058 [INFO] [RouterExplorer] Mapped {/api/user-student/school/:schoolId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.058 [INFO] [RouterExplorer] Mapped {/api/user-student/class/:classId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.058 [INFO] [RouterExplorer] Mapped {/api/user-student/school/:schoolId/class/:classId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.059 [INFO] [RouterExplorer] Mapped {/api/user-student/number, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.059 [INFO] [RouterExplorer] Mapped {/api/user-student/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.059 [INFO] [RouterExplorer] Mapped {/api/user-student/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.060 [INFO] [RouterExplorer] Mapped {/api/user-student/:id/class/:classId, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.060 [INFO] [RouterExplorer] Mapped {/api/user-student/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.060 [INFO] [RouterExplorer] Mapped {/api/user-student/user/:userId, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.060 [INFO] [RoutesResolver] UserPointController {/api/user-point}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.061 [INFO] [RouterExplorer] Mapped {/api/user-point/total, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.061 [INFO] [RouterExplorer] Mapped {/api/user-point/:userId/total, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.061 [INFO] [RouterExplorer] Mapped {/api/user-point/details, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.062 [INFO] [RouterExplorer] Mapped {/api/user-point/add, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.062 [INFO] [RouterExplorer] Mapped {/api/user-point/use, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.062 [INFO] [RouterExplorer] Mapped {/api/user-point/check, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.063 [INFO] [RouterExplorer] Mapped {/api/user-point/assignPoints, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.063 [INFO] [RouterExplorer] Mapped {/api/user-point/batch-total, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.063 [INFO] [RouterExplorer] Mapped {/api/user-point/student/detail, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.063 [INFO] [RouterExplorer] Mapped {/api/user-point/student/check, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.064 [INFO] [RouterExplorer] Mapped {/api/user-point/teacher/student/:studentId/detail, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.064 [INFO] [RouterExplorer] Mapped {/api/user-point/packages, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.064 [INFO] [RoutesResolver] UserPointPackageController {/user-point-package}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.065 [INFO] [RouterExplorer] Mapped {/user-point-package/assign, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.065 [INFO] [RouterExplorer] Mapped {/user-point-package/status, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.065 [INFO] [RouterExplorer] Mapped {/user-point-package/details/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.066 [INFO] [RouterExplorer] Mapped {/user-point-package/clean-expired, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.066 [INFO] [RouterExplorer] Mapped {/user-point-package/use, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.066 [INFO] [RoutesResolver] UserPointsController {/user-points}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.067 [INFO] [RouterExplorer] Mapped {/user-points, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.067 [INFO] [RouterExplorer] Mapped {/user-points, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.067 [INFO] [RouterExplorer] Mapped {/user-points/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.067 [INFO] [RouterExplorer] Mapped {/user-points/user/:userId/date-range, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.068 [INFO] [RouterExplorer] Mapped {/user-points/user/:userId/type/:type, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.068 [INFO] [RouterExplorer] Mapped {/user-points/user/:userId/source/:source, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.068 [INFO] [RouterExplorer] Mapped {/user-points/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.069 [INFO] [RouterExplorer] Mapped {/user-points/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.069 [INFO] [RouterExplorer] Mapped {/user-points/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.069 [INFO] [RoutesResolver] HttpResponseResultController {/http-response}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.070 [INFO] [RouterExplorer] Mapped {/http-response/success, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.070 [INFO] [RouterExplorer] Mapped {/http-response/error, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.070 [INFO] [RouterExplorer] Mapped {/http-response/custom, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.070 [INFO] [RoutesResolver] UserPackageController {/user-package}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.071 [INFO] [RouterExplorer] Mapped {/user-package, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.071 [INFO] [RouterExplorer] Mapped {/user-package, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.071 [INFO] [RouterExplorer] Mapped {/user-package/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.072 [INFO] [RouterExplorer] Mapped {/user-package/user/:userId/active, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.072 [INFO] [RouterExplorer] Mapped {/user-package/package/:packageId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.072 [INFO] [RouterExplorer] Mapped {/user-package/check-expired, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.072 [INFO] [RouterExplorer] Mapped {/user-package/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.073 [INFO] [RouterExplorer] Mapped {/user-package/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.073 [INFO] [RouterExplorer] Mapped {/user-package/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.073 [INFO] [RoutesResolver] PackageInfoController {/package-info}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.074 [INFO] [RouterExplorer] Mapped {/package-info, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.074 [INFO] [RouterExplorer] Mapped {/package-info, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.074 [INFO] [RouterExplorer] Mapped {/package-info/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.077 [INFO] [RouterExplorer] Mapped {/package-info/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.077 [INFO] [RouterExplorer] Mapped {/package-info/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.077 [INFO] [RoutesResolver] UserPointsPermissionController {/user-points-permission}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.077 [INFO] [RouterExplorer] Mapped {/user-points-permission, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.078 [INFO] [RouterExplorer] Mapped {/user-points-permission, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.078 [INFO] [RouterExplorer] Mapped {/user-points-permission/student/:studentUserId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.078 [INFO] [RouterExplorer] Mapped {/user-points-permission/teacher/:teacherUserId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.078 [INFO] [RouterExplorer] Mapped {/user-points-permission/valid, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.079 [INFO] [RouterExplorer] Mapped {/user-points-permission/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.079 [INFO] [RouterExplorer] Mapped {/user-points-permission/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.079 [INFO] [RouterExplorer] Mapped {/user-points-permission/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.079 [INFO] [RoutesResolver] UserClassController {/api/user/class}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.080 [INFO] [RouterExplorer] Mapped {/api/user/class, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.080 [INFO] [RouterExplorer] Mapped {/api/user/class/info/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.080 [INFO] [RouterExplorer] Mapped {/api/user/class, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.080 [INFO] [RouterExplorer] Mapped {/api/user/class/update/:id, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.080 [INFO] [RouterExplorer] Mapped {/api/user/class/school/:schoolId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.081 [INFO] [RouterExplorer] Mapped {/api/user/class/teacher/:teacherId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.081 [INFO] [RouterExplorer] Mapped {/api/user/class/teacher/:teacherId/stats, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.081 [INFO] [RouterExplorer] Mapped {/api/user/class/assistant/:teacherId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.081 [INFO] [RouterExplorer] Mapped {/api/user/class/:id/students, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.082 [INFO] [RouterExplorer] Mapped {/api/user/class/:id/export, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.082 [INFO] [RouterExplorer] Mapped {/api/user/class/:id/invite, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.082 [INFO] [RouterExplorer] Mapped {/api/user/class/student/create, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.082 [INFO] [RouterExplorer] Mapped {/api/user/class/invite_join, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.083 [INFO] [RouterExplorer] Mapped {/api/user/class/transfer, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.083 [INFO] [RouterExplorer] Mapped {/api/user/class/import, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.083 [INFO] [RouterExplorer] Mapped {/api/user/class/create, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.083 [INFO] [RouterExplorer] Mapped {/api/user/class/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.083 [INFO] [RouterExplorer] Mapped {/api/user/class/teacher/classes/:schoolId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.084 [INFO] [RouterExplorer] Mapped {/api/user/class/student/remove, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.084 [INFO] [RouterExplorer] Mapped {/api/user/class/assistant/remove, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.084 [INFO] [RouterExplorer] Mapped {/api/user/class/teacher/search/:phone, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.085 [INFO] [RouterExplorer] Mapped {/api/user/class/school/:schoolId/all, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.085 [INFO] [RoutesResolver] UserClassController {/user-class}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.085 [INFO] [RouterExplorer] Mapped {/user-class, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.085 [INFO] [RouterExplorer] Mapped {/user-class, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.086 [INFO] [RouterExplorer] Mapped {/user-class/school/:schoolId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.086 [INFO] [RouterExplorer] Mapped {/user-class/school/:schoolId/grade/:grade, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.086 [INFO] [RouterExplorer] Mapped {/user-class/teacher/:teacherId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.087 [INFO] [RouterExplorer] Mapped {/user-class/assistant-teacher/:assistantTeacherId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.087 [INFO] [RouterExplorer] Mapped {/user-class/invite-code/:inviteCode, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.087 [INFO] [RouterExplorer] Mapped {/user-class/:id/regenerate-invite-code, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.087 [INFO] [RouterExplorer] Mapped {/user-class/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.088 [INFO] [RouterExplorer] Mapped {/user-class/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.088 [INFO] [RouterExplorer] Mapped {/user-class/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.088 [INFO] [RoutesResolver] UserSchoolController {/user-school}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.088 [INFO] [RouterExplorer] Mapped {/user-school, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.089 [INFO] [RouterExplorer] Mapped {/user-school, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.089 [INFO] [RouterExplorer] Mapped {/user-school/search, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.089 [INFO] [RouterExplorer] Mapped {/user-school/area, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.090 [INFO] [RouterExplorer] Mapped {/user-school/provinces, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.090 [INFO] [RouterExplorer] Mapped {/user-school/cities/:province, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.090 [INFO] [RouterExplorer] Mapped {/user-school/districts/:province/:city, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.090 [INFO] [RouterExplorer] Mapped {/user-school/province/:province, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.091 [INFO] [RouterExplorer] Mapped {/user-school/province/:province/city/:city, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.091 [INFO] [RouterExplorer] Mapped {/user-school/province/:province/city/:city/district/:district, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.091 [INFO] [RouterExplorer] Mapped {/user-school/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.091 [INFO] [RouterExplorer] Mapped {/user-school/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.091 [INFO] [RouterExplorer] Mapped {/user-school/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.092 [INFO] [RoutesResolver] UserStudentController {/user-student}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.092 [INFO] [RouterExplorer] Mapped {/user-student, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.092 [INFO] [RouterExplorer] Mapped {/user-student, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.093 [INFO] [RouterExplorer] Mapped {/user-student/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.093 [INFO] [RouterExplorer] Mapped {/user-student/school/:schoolId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.094 [INFO] [RouterExplorer] Mapped {/user-student/class/:classId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.094 [INFO] [RouterExplorer] Mapped {/user-student/school/:schoolId/class/:classId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.094 [INFO] [RouterExplorer] Mapped {/user-student/number, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.095 [INFO] [RouterExplorer] Mapped {/user-student/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.095 [INFO] [RouterExplorer] Mapped {/user-student/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.095 [INFO] [RouterExplorer] Mapped {/user-student/:id/class/:classId, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.095 [INFO] [RouterExplorer] Mapped {/user-student/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.096 [INFO] [RouterExplorer] Mapped {/user-student/user/:userId, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.096 [INFO] [RoutesResolver] RedisController {/redis}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.096 [INFO] [RouterExplorer] Mapped {/redis/set, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.096 [INFO] [RouterExplorer] Mapped {/redis/get, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.097 [INFO] [RouterExplorer] Mapped {/redis/del, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.097 [INFO] [RouterExplorer] Mapped {/redis/exists, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.097 [INFO] [RouterExplorer] Mapped {/redis/expire, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.097 [INFO] [RoutesResolver] KeyPackageController {/key-package}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.097 [INFO] [RouterExplorer] Mapped {/key-package, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.097 [INFO] [RouterExplorer] Mapped {/key-package, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.098 [INFO] [RouterExplorer] Mapped {/key-package/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.098 [INFO] [RouterExplorer] Mapped {/key-package/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.098 [INFO] [RouterExplorer] Mapped {/key-package/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.098 [INFO] [RoutesResolver] UserAuthController {/api/user-auth}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.098 [INFO] [RouterExplorer] Mapped {/api/user-auth/bindPhone, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.099 [INFO] [RouterExplorer] Mapped {/api/user-auth/setPasswordByPhone, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.099 [INFO] [RouterExplorer] Mapped {/api/user-auth/setPasswordByUserId, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.099 [INFO] [RouterExplorer] Mapped {/api/user-auth/password, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.099 [INFO] [RouterExplorer] Mapped {/api/user-auth/select-identity, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.099 [INFO] [RouterExplorer] Mapped {/api/user-auth/refreshToken, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.100 [INFO] [RouterExplorer] Mapped {/api/user-auth/updatePassword, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.100 [INFO] [RouterExplorer] Mapped {/api/user-auth/logout, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.100 [INFO] [RouterExplorer] Mapped {/api/user-auth/student, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.101 [INFO] [RouterExplorer] Mapped {/api/user-auth/smsSend, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.101 [INFO] [RouterExplorer] Mapped {/api/user-auth/smsSendUpdatePhone, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.101 [INFO] [RouterExplorer] Mapped {/api/user-auth/smsLogin, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.102 [INFO] [RouterExplorer] Mapped {/api/user-auth/resetPasswordByCode, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.102 [INFO] [RouterExplorer] Mapped {/api/user-auth/student/reset-password, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.102 [INFO] [RouterExplorer] Mapped {/api/user-auth/check-phone-accounts, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.102 [INFO] [RouterExplorer] Mapped {/api/user-auth/findAllBindByPhone, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.103 [INFO] [RouterExplorer] Mapped {/api/user-auth/bind-weixin-to-user, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.103 [INFO] [RouterExplorer] Mapped {/api/user-auth/register-and-bind-weixin, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.103 [INFO] [RouterExplorer] Mapped {/api/user-auth/bind-weixin, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.104 [INFO] [RouterExplorer] Mapped {/api/user-auth/reset-password-by-phone, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.104 [INFO] [RouterExplorer] Mapped {/api/user-auth/switch-role, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.104 [INFO] [RouterExplorer] Mapped {/api/user-auth/verify-sms-code, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.104 [INFO] [RouterExplorer] Mapped {/api/user-auth/transfer-weixin-openid, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.105 [INFO] [RouterExplorer] Mapped {/api/user-auth/auto-login-by-userid, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.105 [INFO] [RoutesResolver] AliSmsController {/ali-sms}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.105 [INFO] [RouterExplorer] Mapped {/ali-sms/send-code, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.105 [INFO] [RoutesResolver] UserInfoController {/api/user-info}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.106 [INFO] [RouterExplorer] Mapped {/api/user-info/updatePhone, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.106 [INFO] [RouterExplorer] Mapped {/api/user-info/condition/search, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.106 [INFO] [RouterExplorer] Mapped {/api/user-info, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.107 [INFO] [RouterExplorer] Mapped {/api/user-info, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.107 [INFO] [RouterExplorer] Mapped {/api/user-info/search, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.107 [INFO] [RouterExplorer] Mapped {/api/user-info/phone/:phone, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.108 [INFO] [RouterExplorer] Mapped {/api/user-info/role/:roleId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.108 [INFO] [RouterExplorer] Mapped {/api/user-info/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.108 [INFO] [RouterExplorer] Mapped {/api/user-info/detail/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.108 [INFO] [RouterExplorer] Mapped {/api/user-info/person/info, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.109 [INFO] [RouterExplorer] Mapped {/api/user-info/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.109 [INFO] [RouterExplorer] Mapped {/api/user-info/:id/points/:points, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.109 [INFO] [RouterExplorer] Mapped {/api/user-info/:id/role/:roleId, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.109 [INFO] [RouterExplorer] Mapped {/api/user-info/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.109 [INFO] [RoutesResolver] UserRoleRelationController {/user-role-relation}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.110 [INFO] [RouterExplorer] Mapped {/user-role-relation, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.110 [INFO] [RouterExplorer] Mapped {/user-role-relation/batch-assign-roles, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.110 [INFO] [RouterExplorer] Mapped {/user-role-relation/batch-assign-users, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.110 [INFO] [RouterExplorer] Mapped {/user-role-relation, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.111 [INFO] [RouterExplorer] Mapped {/user-role-relation/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.111 [INFO] [RouterExplorer] Mapped {/user-role-relation/role/:roleId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.111 [INFO] [RouterExplorer] Mapped {/user-role-relation/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.112 [INFO] [RouterExplorer] Mapped {/user-role-relation/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.112 [INFO] [RouterExplorer] Mapped {/user-role-relation/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.112 [INFO] [RouterExplorer] Mapped {/user-role-relation/user/:userId/role/:roleId, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.112 [INFO] [RouterExplorer] Mapped {/user-role-relation/user/:userId/all, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.113 [INFO] [RouterExplorer] Mapped {/user-role-relation/role/:roleId/all, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.113 [INFO] [RoutesResolver] WebWeixinScanController {/weixin-scan}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.113 [INFO] [RouterExplorer] Mapped {/weixin-scan/qrcode, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.113 [INFO] [RouterExplorer] Mapped {/weixin-scan/check-status/:scene_str, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.114 [INFO] [RouterExplorer] Mapped {/weixin-scan/confirm, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.114 [INFO] [RouterExplorer] Mapped {/weixin-scan/callback, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.114 [INFO] [RoutesResolver] AliOssController {/ali-oss}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.114 [INFO] [RouterExplorer] Mapped {/ali-oss/upload, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.115 [INFO] [RouterExplorer] Mapped {/ali-oss/upload-form, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.115 [INFO] [RouterExplorer] Mapped {/ali-oss/delete, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.115 [INFO] [RouterExplorer] Mapped {/ali-oss/buckets, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.115 [INFO] [RouterExplorer] Mapped {/ali-oss/check-image, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.116 [INFO] [RoutesResolver] UserJoinRoleController {/user-join-role}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.116 [INFO] [RouterExplorer] Mapped {/user-join-role, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.116 [INFO] [RouterExplorer] Mapped {/user-join-role, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.116 [INFO] [RouterExplorer] Mapped {/user-join-role/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.116 [INFO] [RouterExplorer] Mapped {/user-join-role/role/:roleId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.117 [INFO] [RouterExplorer] Mapped {/user-join-role/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.117 [INFO] [RouterExplorer] Mapped {/user-join-role/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.117 [INFO] [RouterExplorer] Mapped {/user-join-role/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.117 [INFO] [RouterExplorer] Mapped {/user-join-role/user/:userId/role/:roleId, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.118 [INFO] [RoutesResolver] UserSrchTemplatesController {/api/user/srch/templates}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.118 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/create, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.118 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/update, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.118 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.119 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/current/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.119 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/batch-current, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.119 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.119 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/list/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.119 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/list, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.120 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/:id/default, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.120 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/official/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.120 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/scratch/permissions, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.121 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/scratchs/permissions, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.121 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/folders/tree, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.121 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/folders/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.121 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/folders/create, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.122 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/folders/update, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.122 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/folders/delete, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.122 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/folders/templates, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.122 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/folders/addTemplate, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.123 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/folders/removeTemplate, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.123 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/folders/addTemplates, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.123 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/teacher_students, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.123 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/folders/:folderId/templates, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.124 [INFO] [RoutesResolver] RoleTemplateExtensionPermissionController {/role-template-extension-permission}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.124 [INFO] [RouterExplorer] Mapped {/role-template-extension-permission, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.124 [INFO] [RouterExplorer] Mapped {/role-template-extension-permission/batch, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.125 [INFO] [RouterExplorer] Mapped {/role-template-extension-permission, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.125 [INFO] [RouterExplorer] Mapped {/role-template-extension-permission/template/:templateId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.126 [INFO] [RouterExplorer] Mapped {/role-template-extension-permission/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.126 [INFO] [RouterExplorer] Mapped {/role-template-extension-permission/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.126 [INFO] [RouterExplorer] Mapped {/role-template-extension-permission/:id/toggle, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.126 [INFO] [RouterExplorer] Mapped {/role-template-extension-permission/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.127 [INFO] [RouterExplorer] Mapped {/role-template-extension-permission/template/:templateId/extension/:extensionId, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.127 [INFO] [RouterExplorer] Mapped {/role-template-extension-permission/template/:templateId/all, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.127 [INFO] [RoutesResolver] RoleTemplateBlockPermissionController {/role-template-block-permission}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.127 [INFO] [RouterExplorer] Mapped {/role-template-block-permission, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.127 [INFO] [RouterExplorer] Mapped {/role-template-block-permission/batch, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.128 [INFO] [RouterExplorer] Mapped {/role-template-block-permission, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.128 [INFO] [RouterExplorer] Mapped {/role-template-block-permission/template/:templateId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.128 [INFO] [RouterExplorer] Mapped {/role-template-block-permission/template/:templateId/extension/:extensionId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.128 [INFO] [RouterExplorer] Mapped {/role-template-block-permission/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.128 [INFO] [RouterExplorer] Mapped {/role-template-block-permission/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.129 [INFO] [RouterExplorer] Mapped {/role-template-block-permission/:id/toggle, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.129 [INFO] [RouterExplorer] Mapped {/role-template-block-permission/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.129 [INFO] [RouterExplorer] Mapped {/role-template-block-permission/template/:templateId/extension/:extensionId/block/:blockId, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.129 [INFO] [RouterExplorer] Mapped {/role-template-block-permission/template/:templateId/all, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.130 [INFO] [RoutesResolver] RoleTemplateFolderController {/role-template-folder}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.130 [INFO] [RouterExplorer] Mapped {/role-template-folder, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.130 [INFO] [RouterExplorer] Mapped {/role-template-folder, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.130 [INFO] [RouterExplorer] Mapped {/role-template-folder/root, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.130 [INFO] [RouterExplorer] Mapped {/role-template-folder/tree, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.131 [INFO] [RouterExplorer] Mapped {/role-template-folder/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.131 [INFO] [RouterExplorer] Mapped {/role-template-folder/:id/children, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.131 [INFO] [RouterExplorer] Mapped {/role-template-folder/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.131 [INFO] [RouterExplorer] Mapped {/role-template-folder/:id/sort/:sort, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.132 [INFO] [RouterExplorer] Mapped {/role-template-folder/:id/move, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.132 [INFO] [RouterExplorer] Mapped {/role-template-folder/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.133 [INFO] [RouterExplorer] Mapped {/role-template-folder/:id/with-children, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.133 [INFO] [RoutesResolver] RoleTemplateFolderJoinTemplateController {/role-template-folder-join-template}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.134 [INFO] [RouterExplorer] Mapped {/role-template-folder-join-template, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.134 [INFO] [RouterExplorer] Mapped {/role-template-folder-join-template/batch-templates-to-folder, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.135 [INFO] [RouterExplorer] Mapped {/role-template-folder-join-template/batch-folders-to-template, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.135 [INFO] [RouterExplorer] Mapped {/role-template-folder-join-template, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.135 [INFO] [RouterExplorer] Mapped {/role-template-folder-join-template/folder/:folderId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.136 [INFO] [RouterExplorer] Mapped {/role-template-folder-join-template/template/:templateId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.138 [INFO] [RouterExplorer] Mapped {/role-template-folder-join-template/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.139 [INFO] [RouterExplorer] Mapped {/role-template-folder-join-template/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.139 [INFO] [RouterExplorer] Mapped {/role-template-folder-join-template/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.140 [INFO] [RouterExplorer] Mapped {/role-template-folder-join-template/folder/:folderId/template/:templateId, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.140 [INFO] [RouterExplorer] Mapped {/role-template-folder-join-template/folder/:folderId/all, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.141 [INFO] [RouterExplorer] Mapped {/role-template-folder-join-template/template/:templateId/all, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.141 [INFO] [RoutesResolver] TableJoingController {/table-joing}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.142 [INFO] [RouterExplorer] Mapped {/table-joing/student-templates, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.142 [INFO] [RoutesResolver] RouterGuardController {/api/router-guard}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.142 [INFO] [RouterExplorer] Mapped {/api/router-guard/protected, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.143 [INFO] [RouterExplorer] Mapped {/api/router-guard/public, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.143 [INFO] [RouterExplorer] Mapped {/api/router-guard/login-check, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.143 [INFO] [RouterExplorer] Mapped {/api/router-guard/refresh-token, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.144 [INFO] [RouterExplorer] Mapped {/api/router-guard/protected, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.144 [INFO] [RoutesResolver] UserSrchWorkController {/api/user/srch/work}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.144 [INFO] [RouterExplorer] Mapped {/api/user/srch/work/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.144 [INFO] [RouterExplorer] Mapped {/api/user/srch/work/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.145 [INFO] [RouterExplorer] Mapped {/api/user/srch/work/create, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.145 [INFO] [RouterExplorer] Mapped {/api/user/srch/work/update, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.145 [INFO] [RouterExplorer] Mapped {/api/user/srch/work/remove/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.146 [INFO] [RouterExplorer] Mapped {/api/user/srch/work/class/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.146 [INFO] [RouterExplorer] Mapped {/api/user/srch/work/stats/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.146 [INFO] [RoutesResolver] UserWorkInfoController {/user-work-info}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.146 [INFO] [RouterExplorer] Mapped {/user-work-info, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.147 [INFO] [RouterExplorer] Mapped {/user-work-info, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.147 [INFO] [RouterExplorer] Mapped {/user-work-info/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.147 [INFO] [RouterExplorer] Mapped {/user-work-info/user/:userId/type/:type, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.147 [INFO] [RouterExplorer] Mapped {/user-work-info/public, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.148 [INFO] [RouterExplorer] Mapped {/user-work-info/class/:classId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.148 [INFO] [RouterExplorer] Mapped {/user-work-info/:id/view, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.148 [INFO] [RouterExplorer] Mapped {/user-work-info/:id/like, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.148 [INFO] [RouterExplorer] Mapped {/user-work-info/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.149 [INFO] [RouterExplorer] Mapped {/user-work-info/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.149 [INFO] [RouterExplorer] Mapped {/user-work-info/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.149 [INFO] [RoutesResolver] WorkModelController {/work-model}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.150 [INFO] [RouterExplorer] Mapped {/work-model, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.150 [INFO] [RouterExplorer] Mapped {/work-model, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.150 [INFO] [RouterExplorer] Mapped {/work-model/work/:workId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.150 [INFO] [RouterExplorer] Mapped {/work-model/work/:workId/type/:modelType, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.150 [INFO] [RouterExplorer] Mapped {/work-model/work/:workId/number/:modelNumber, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.151 [INFO] [RouterExplorer] Mapped {/work-model/:id/toggle-active, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.151 [INFO] [RouterExplorer] Mapped {/work-model/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.151 [INFO] [RouterExplorer] Mapped {/work-model/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.151 [INFO] [RouterExplorer] Mapped {/work-model/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.151 [INFO] [RoutesResolver] UserSrchTaskController {/api/user/srch/task}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.152 [INFO] [RouterExplorer] Mapped {/api/user/srch/task/work/:workId/submissions, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.152 [INFO] [RouterExplorer] Mapped {/api/user/srch/task/stats/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.152 [INFO] [RouterExplorer] Mapped {/api/user/srch/task/batch-assign, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.152 [INFO] [RouterExplorer] Mapped {/api/user/srch/task/submit, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.152 [INFO] [RouterExplorer] Mapped {/api/user/srch/task/grade, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.153 [INFO] [RouterExplorer] Mapped {/api/user/srch/task/work/:assignmentId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.153 [INFO] [RouterExplorer] Mapped {/api/user/srch/task/return, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.153 [INFO] [RouterExplorer] Mapped {/api/user/srch/task/status/:assignmentId, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.153 [INFO] [RouterExplorer] Mapped {/api/user/srch/task/publish-to-class, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.154 [INFO] [RouterExplorer] Mapped {/api/user/srch/task/mark-as-read, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.154 [INFO] [RouterExplorer] Mapped {/api/user/srch/task/task/stats/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.154 [INFO] [RouterExplorer] Mapped {/api/user/srch/task/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.155 [INFO] [RouterExplorer] Mapped {/api/user/srch/task/publish, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.155 [INFO] [RouterExplorer] Mapped {/api/user/srch/task/:taskId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.155 [INFO] [RouterExplorer] Mapped {/api/user/srch/task/updata, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.155 [INFO] [RouterExplorer] Mapped {/api/user/srch/task/remove/:taskId, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.156 [INFO] [RouterExplorer] Mapped {/api/user/srch/task/search, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.156 [INFO] [RouterExplorer] Mapped {/api/user/srch/task/work/:workId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.156 [INFO] [RoutesResolver] TeacherTaskAssignmentController {/teacher-task-assignment}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.157 [INFO] [RouterExplorer] Mapped {/teacher-task-assignment, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.157 [INFO] [RouterExplorer] Mapped {/teacher-task-assignment, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.157 [INFO] [RouterExplorer] Mapped {/teacher-task-assignment/task/:taskId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.157 [INFO] [RouterExplorer] Mapped {/teacher-task-assignment/student/:studentId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.157 [INFO] [RouterExplorer] Mapped {/teacher-task-assignment/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.158 [INFO] [RouterExplorer] Mapped {/teacher-task-assignment/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.158 [INFO] [RouterExplorer] Mapped {/teacher-task-assignment/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.158 [INFO] [RoutesResolver] TeacherTaskController {/teacher-task}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.158 [INFO] [RouterExplorer] Mapped {/teacher-task, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.158 [INFO] [RouterExplorer] Mapped {/teacher-task, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.159 [INFO] [RouterExplorer] Mapped {/teacher-task/teacher/:teacherId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.159 [INFO] [RouterExplorer] Mapped {/teacher-task/class/:classId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.159 [INFO] [RouterExplorer] Mapped {/teacher-task/check-status, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.159 [INFO] [RouterExplorer] Mapped {/teacher-task/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.159 [INFO] [RouterExplorer] Mapped {/teacher-task/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.159 [INFO] [RouterExplorer] Mapped {/teacher-task/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.160 [INFO] [RoutesResolver] UserImageInfoController {/user-image-info}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.160 [INFO] [RouterExplorer] Mapped {/user-image-info, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.160 [INFO] [RouterExplorer] Mapped {/user-image-info, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.160 [INFO] [RouterExplorer] Mapped {/user-image-info/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.161 [INFO] [RouterExplorer] Mapped {/user-image-info/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.161 [INFO] [RouterExplorer] Mapped {/user-image-info/task/:taskId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.161 [INFO] [RouterExplorer] Mapped {/user-image-info/:id/view, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.162 [INFO] [RouterExplorer] Mapped {/user-image-info/:id/like, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.162 [INFO] [RouterExplorer] Mapped {/user-image-info/:id/status/:status, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.162 [INFO] [RouterExplorer] Mapped {/user-image-info/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.162 [INFO] [RouterExplorer] Mapped {/user-image-info/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.162 [INFO] [RoutesResolver] TaskSelfAssessmentItemController {/api/task-self-assessment-item}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.163 [INFO] [RouterExplorer] Mapped {/api/task-self-assessment-item, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.163 [INFO] [RouterExplorer] Mapped {/api/task-self-assessment-item, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.163 [INFO] [RouterExplorer] Mapped {/api/task-self-assessment-item/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.163 [INFO] [RouterExplorer] Mapped {/api/task-self-assessment-item/task/:taskId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.163 [INFO] [RouterExplorer] Mapped {/api/task-self-assessment-item/search, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.164 [INFO] [RouterExplorer] Mapped {/api/task-self-assessment-item/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.164 [INFO] [RouterExplorer] Mapped {/api/task-self-assessment-item/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.164 [INFO] [RouterExplorer] Mapped {/api/task-self-assessment-item/check-records, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.164 [INFO] [RouterExplorer] Mapped {/api/task-self-assessment-item/update-by-task, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.164 [INFO] [RoutesResolver] WebUserInfoController {/api/web/user/info}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.165 [INFO] [RouterExplorer] Mapped {/api/web/user/info/ping, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.165 [INFO] [RouterExplorer] Mapped {/api/web/user/info/checkHasBindPhone, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.165 [INFO] [RouterExplorer] Mapped {/api/web/user/info/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.165 [INFO] [RouterExplorer] Mapped {/api/web/user/info/update, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.165 [INFO] [RouterExplorer] Mapped {/api/web/user/info/update/password, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.166 [INFO] [RouterExplorer] Mapped {/api/web/user/info/reset_password/request, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.166 [INFO] [RouterExplorer] Mapped {/api/web/user/info/reset/password/handle, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.166 [INFO] [RouterExplorer] Mapped {/api/web/user/info/reset/password/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.166 [INFO] [RoutesResolver] UserPasswordResetRequestController {/user-password-reset-request}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.167 [INFO] [RouterExplorer] Mapped {/user-password-reset-request, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.167 [INFO] [RouterExplorer] Mapped {/user-password-reset-request, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.167 [INFO] [RouterExplorer] Mapped {/user-password-reset-request/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.167 [INFO] [RouterExplorer] Mapped {/user-password-reset-request/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.168 [INFO] [RouterExplorer] Mapped {/user-password-reset-request/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.168 [INFO] [RoutesResolver] UserSrchImageController {/api/user-srch-image}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.168 [INFO] [RouterExplorer] Mapped {/api/user-srch-image/add, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.169 [INFO] [RouterExplorer] Mapped {/api/user-srch-image/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.169 [INFO] [RouterExplorer] Mapped {/api/user-srch-image/status, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.169 [INFO] [RouterExplorer] Mapped {/api/user-srch-image/stats, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.169 [INFO] [RouterExplorer] Mapped {/api/user-srch-image/task-status, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.170 [INFO] [RoutesResolver] UserSrchEnhanceController {/api/user-srch-enhance}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.170 [INFO] [RouterExplorer] Mapped {/api/user-srch-enhance/add, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.170 [INFO] [RouterExplorer] Mapped {/api/user-srch-enhance/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.170 [INFO] [RouterExplorer] Mapped {/api/user-srch-enhance/byImageId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.171 [INFO] [RouterExplorer] Mapped {/api/user-srch-enhance/task-status, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.171 [INFO] [RoutesResolver] UserImageEnhanceController {/user-image-enhance}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.171 [INFO] [RouterExplorer] Mapped {/user-image-enhance, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.171 [INFO] [RouterExplorer] Mapped {/user-image-enhance, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.172 [INFO] [RouterExplorer] Mapped {/user-image-enhance/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.172 [INFO] [RouterExplorer] Mapped {/user-image-enhance/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.172 [INFO] [RouterExplorer] Mapped {/user-image-enhance/image/:imageId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.174 [INFO] [RouterExplorer] Mapped {/user-image-enhance/task/:taskId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.176 [INFO] [RouterExplorer] Mapped {/user-image-enhance/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.176 [INFO] [RouterExplorer] Mapped {/user-image-enhance/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.178 [INFO] [RoutesResolver] UserSrchAudioController {/api/user-srch-audio}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.178 [INFO] [RouterExplorer] Mapped {/api/user-srch-audio/add, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.179 [INFO] [RouterExplorer] Mapped {/api/user-srch-audio/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.179 [INFO] [RouterExplorer] Mapped {/api/user-srch-audio/stats, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.179 [INFO] [RouterExplorer] Mapped {/api/user-srch-audio/task-info, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.179 [INFO] [RoutesResolver] UserVoiceInfoController {/user-voice-info}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.180 [INFO] [RouterExplorer] Mapped {/user-voice-info, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.180 [INFO] [RouterExplorer] Mapped {/user-voice-info, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.180 [INFO] [RouterExplorer] Mapped {/user-voice-info/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.181 [INFO] [RouterExplorer] Mapped {/user-voice-info/task/:taskId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.181 [INFO] [RouterExplorer] Mapped {/user-voice-info/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.181 [INFO] [RouterExplorer] Mapped {/user-voice-info/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.181 [INFO] [RouterExplorer] Mapped {/user-voice-info/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.181 [INFO] [RoutesResolver] UserPointPackageController {/user-point-package}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.182 [INFO] [RouterExplorer] Mapped {/user-point-package/assign, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.182 [INFO] [RouterExplorer] Mapped {/user-point-package/status, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.182 [INFO] [RouterExplorer] Mapped {/user-point-package/details/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.182 [INFO] [RouterExplorer] Mapped {/user-point-package/clean-expired, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.182 [INFO] [RouterExplorer] Mapped {/user-point-package/use, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.183 [INFO] [RoutesResolver] WebPointController {/web/point}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.183 [INFO] [RouterExplorer] Mapped {/web/point/package/assign, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.183 [INFO] [RouterExplorer] Mapped {/web/point/package/status, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.183 [INFO] [RouterExplorer] Mapped {/web/point/package/details/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.183 [INFO] [RouterExplorer] Mapped {/web/point/add, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.183 [INFO] [RouterExplorer] Mapped {/web/point/use, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.184 [INFO] [RouterExplorer] Mapped {/web/point/assign, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.184 [INFO] [RouterExplorer] Mapped {/web/point/assign/batch, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.184 [INFO] [RouterExplorer] Mapped {/web/point/student/:studentId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.184 [INFO] [RouterExplorer] Mapped {/web/point/permission/teacher/:teacherId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.185 [INFO] [RouterExplorer] Mapped {/web/point/check, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.185 [INFO] [RouterExplorer] Mapped {/web/point/details/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.185 [INFO] [RouterExplorer] Mapped {/web/point/clean-expired, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.186 [INFO] [RouterExplorer] Mapped {/web/point/clean-expired-permissions, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.186 [INFO] [RoutesResolver] UserSchoolController {/api/user-school}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.186 [INFO] [RouterExplorer] Mapped {/api/user-school, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.187 [INFO] [RouterExplorer] Mapped {/api/user-school/listByUserId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.187 [INFO] [RouterExplorer] Mapped {/api/user-school, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.187 [INFO] [RouterExplorer] Mapped {/api/user-school/search, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.187 [INFO] [RouterExplorer] Mapped {/api/user-school/area, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.187 [INFO] [RouterExplorer] Mapped {/api/user-school/provinces, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.188 [INFO] [RouterExplorer] Mapped {/api/user-school/cities/:province, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.188 [INFO] [RouterExplorer] Mapped {/api/user-school/districts/:province/:city, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.188 [INFO] [RouterExplorer] Mapped {/api/user-school/province/:province, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.188 [INFO] [RouterExplorer] Mapped {/api/user-school/province/:province/city/:city, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.189 [INFO] [RouterExplorer] Mapped {/api/user-school/province/:province/city/:city/district/:district, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.189 [INFO] [RouterExplorer] Mapped {/api/user-school/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.189 [INFO] [RouterExplorer] Mapped {/api/user-school/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.189 [INFO] [RouterExplorer] Mapped {/api/user-school/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.189 [INFO] [RoutesResolver] UserSchoolRelationController {/api/user-school-relation}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.189 [INFO] [RouterExplorer] Mapped {/api/user-school-relation, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.190 [INFO] [RouterExplorer] Mapped {/api/user-school-relation, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.190 [INFO] [RouterExplorer] Mapped {/api/user-school-relation/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.190 [INFO] [RouterExplorer] Mapped {/api/user-school-relation/school/:schoolId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.190 [INFO] [RouterExplorer] Mapped {/api/user-school-relation/school/:schoolId/students, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.191 [INFO] [RouterExplorer] Mapped {/api/user-school-relation/school/:schoolId/teachers, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.191 [INFO] [RouterExplorer] Mapped {/api/user-school-relation/check, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.191 [INFO] [RouterExplorer] Mapped {/api/user-school-relation/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.191 [INFO] [RouterExplorer] Mapped {/api/user-school-relation/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.192 [INFO] [RouterExplorer] Mapped {/api/user-school-relation/:id/role-type/:roleType, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.192 [INFO] [RouterExplorer] Mapped {/api/user-school-relation/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.192 [INFO] [RouterExplorer] Mapped {/api/user-school-relation/user/:userId/school/:schoolId, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.192 [INFO] [RouterExplorer] Mapped {/api/user-school-relation/user/:userId/all, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.193 [INFO] [RouterExplorer] Mapped {/api/user-school-relation/school/:schoolId/all, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.193 [INFO] [RoutesResolver] WebAnnouncementController {/api/web/announcement}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.193 [INFO] [RouterExplorer] Mapped {/api/web/announcement/read/deleteByUser, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.193 [INFO] [RouterExplorer] Mapped {/api/web/announcement/read/deleteByAnnouncement, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.193 [INFO] [RouterExplorer] Mapped {/api/web/announcement/read/unread, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.194 [INFO] [RouterExplorer] Mapped {/api/web/announcement/read/mark, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.194 [INFO] [RouterExplorer] Mapped {/api/web/announcement/read/add, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.194 [INFO] [RouterExplorer] Mapped {/api/web/announcement/read/delete/:id, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.194 [INFO] [RouterExplorer] Mapped {/api/web/announcement/read/update/:id, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.194 [INFO] [RouterExplorer] Mapped {/api/web/announcement/read/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.195 [INFO] [RouterExplorer] Mapped {/api/web/announcement/read/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.195 [INFO] [RouterExplorer] Mapped {/api/web/announcement/read/page, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.195 [INFO] [RouterExplorer] Mapped {/api/web/announcement/publish, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.195 [INFO] [RouterExplorer] Mapped {/api/web/announcement/list, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.195 [INFO] [RouterExplorer] Mapped {/api/web/announcement/recall, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.196 [INFO] [RouterExplorer] Mapped {/api/web/announcement/allIds, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.196 [INFO] [RouterExplorer] Mapped {/api/web/announcement/publishedIds, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.196 [INFO] [RouterExplorer] Mapped {/api/web/announcement/publishedIdsByTarget, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.196 [INFO] [RouterExplorer] Mapped {/api/web/announcement/detail, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.197 [INFO] [RouterExplorer] Mapped {/api/web/announcement/increaseReadCount, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.197 [INFO] [RouterExplorer] Mapped {/api/web/announcement/review, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.197 [INFO] [RouterExplorer] Mapped {/api/web/announcement/review/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.197 [INFO] [RouterExplorer] Mapped {/api/web/announcement/add, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.198 [INFO] [RouterExplorer] Mapped {/api/web/announcement/delete, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.198 [INFO] [RouterExplorer] Mapped {/api/web/announcement/update, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.198 [INFO] [RouterExplorer] Mapped {/api/web/announcement/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.198 [INFO] [RouterExplorer] Mapped {/api/web/announcement/page, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.198 [INFO] [RouterExplorer] Mapped {/api/web/announcement/audit/listByAnnouncement/:announcementId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.198 [INFO] [RouterExplorer] Mapped {/api/web/announcement/audit/listWithFilter, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.199 [INFO] [RouterExplorer] Mapped {/api/web/announcement/audit/add, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.199 [INFO] [RoutesResolver] AnnouncementReadRecordController {/announcement-read-record}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.199 [INFO] [RouterExplorer] Mapped {/announcement-read-record, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.199 [INFO] [RouterExplorer] Mapped {/announcement-read-record, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.200 [INFO] [RouterExplorer] Mapped {/announcement-read-record/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.200 [INFO] [RouterExplorer] Mapped {/announcement-read-record/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.200 [INFO] [RouterExplorer] Mapped {/announcement-read-record/announcement/:announcementId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.200 [INFO] [RouterExplorer] Mapped {/announcement-read-record/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.200 [INFO] [RouterExplorer] Mapped {/announcement-read-record/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.200 [INFO] [RoutesResolver] AnnouncementController {/announcement}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.201 [INFO] [RouterExplorer] Mapped {/announcement, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.201 [INFO] [RouterExplorer] Mapped {/announcement, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.201 [INFO] [RouterExplorer] Mapped {/announcement/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.201 [INFO] [RouterExplorer] Mapped {/announcement/publisher/:publisherId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.201 [INFO] [RouterExplorer] Mapped {/announcement/type/:type, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.202 [INFO] [RouterExplorer] Mapped {/announcement/top/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.202 [INFO] [RouterExplorer] Mapped {/announcement/status/:status, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.202 [INFO] [RouterExplorer] Mapped {/announcement/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.202 [INFO] [RouterExplorer] Mapped {/announcement/:id/read, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.203 [INFO] [RouterExplorer] Mapped {/announcement/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.203 [INFO] [RoutesResolver] AnnouncementAuditController {/announcement-audit}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.203 [INFO] [RouterExplorer] Mapped {/announcement-audit, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.203 [INFO] [RouterExplorer] Mapped {/announcement-audit, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.204 [INFO] [RouterExplorer] Mapped {/announcement-audit/announcement/:announcementId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.204 [INFO] [RouterExplorer] Mapped {/announcement-audit/auditor/:auditorId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.204 [INFO] [RouterExplorer] Mapped {/announcement-audit/result/:result, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.204 [INFO] [RouterExplorer] Mapped {/announcement-audit/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.205 [INFO] [RouterExplorer] Mapped {/announcement-audit/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.205 [INFO] [RouterExplorer] Mapped {/announcement-audit/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.205 [INFO] [RouterExplorer] Mapped {/announcement-audit/:id/hard, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.205 [INFO] [RoutesResolver] WebCarouselController {/api/web-carousel}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.206 [INFO] [RouterExplorer] Mapped {/api/web-carousel/active, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.206 [INFO] [RouterExplorer] Mapped {/api/web-carousel, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.206 [INFO] [RouterExplorer] Mapped {/api/web-carousel/review, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.206 [INFO] [RouterExplorer] Mapped {/api/web-carousel/review, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.207 [INFO] [RouterExplorer] Mapped {/api/web-carousel/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.207 [INFO] [RouterExplorer] Mapped {/api/web-carousel, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.207 [INFO] [RouterExplorer] Mapped {/api/web-carousel/update, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.207 [INFO] [RouterExplorer] Mapped {/api/web-carousel/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.208 [INFO] [RoutesResolver] WebCarouselAuditController {/api/web-carousel/audit}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.208 [INFO] [RouterExplorer] Mapped {/api/web-carousel/audit/listByCarousel/:carouselId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.208 [INFO] [RouterExplorer] Mapped {/api/web-carousel/audit/listWithFilter, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.208 [INFO] [RouterExplorer] Mapped {/api/web-carousel/audit/info/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.209 [INFO] [RouterExplorer] Mapped {/api/web-carousel/audit/add, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.209 [INFO] [RoutesResolver] SpaceCarouselMapController {/space-carousel-map}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.209 [INFO] [RouterExplorer] Mapped {/space-carousel-map, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.209 [INFO] [RouterExplorer] Mapped {/space-carousel-map, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.210 [INFO] [RouterExplorer] Mapped {/space-carousel-map/active, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.210 [INFO] [RouterExplorer] Mapped {/space-carousel-map/type/:type, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.210 [INFO] [RouterExplorer] Mapped {/space-carousel-map/creator/:creatorId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.210 [INFO] [RouterExplorer] Mapped {/space-carousel-map/:id/status/:status, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.210 [INFO] [RouterExplorer] Mapped {/space-carousel-map/:id/sort/:sort, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.211 [INFO] [RouterExplorer] Mapped {/space-carousel-map/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.211 [INFO] [RouterExplorer] Mapped {/space-carousel-map/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.211 [INFO] [RouterExplorer] Mapped {/space-carousel-map/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.211 [INFO] [RoutesResolver] CarouselAuditController {/carousel-audit}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.211 [INFO] [RouterExplorer] Mapped {/carousel-audit, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.212 [INFO] [RouterExplorer] Mapped {/carousel-audit, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.212 [INFO] [RouterExplorer] Mapped {/carousel-audit/carousel/:carouselId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.212 [INFO] [RouterExplorer] Mapped {/carousel-audit/auditor/:auditorId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.213 [INFO] [RouterExplorer] Mapped {/carousel-audit/result/:result, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.213 [INFO] [RouterExplorer] Mapped {/carousel-audit/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.213 [INFO] [RouterExplorer] Mapped {/carousel-audit/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.214 [INFO] [RouterExplorer] Mapped {/carousel-audit/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.214 [INFO] [RouterExplorer] Mapped {/carousel-audit/:id/hard, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.215 [INFO] [RoutesResolver] TeacherTaskController {/api/teacher-task}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.215 [INFO] [RouterExplorer] Mapped {/api/teacher-task/search, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.215 [INFO] [RouterExplorer] Mapped {/api/teacher-task/delete-task, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.215 [INFO] [RouterExplorer] Mapped {/api/teacher-task/update-task, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.216 [INFO] [RouterExplorer] Mapped {/api/teacher-task/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.216 [INFO] [RouterExplorer] Mapped {/api/teacher-task, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.216 [INFO] [RouterExplorer] Mapped {/api/teacher-task, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.216 [INFO] [RouterExplorer] Mapped {/api/teacher-task/teacher/:teacherId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.216 [INFO] [RouterExplorer] Mapped {/api/teacher-task/class/:classId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.217 [INFO] [RouterExplorer] Mapped {/api/teacher-task/check-status, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.217 [INFO] [RouterExplorer] Mapped {/api/teacher-task/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.217 [INFO] [RouterExplorer] Mapped {/api/teacher-task/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.217 [INFO] [RouterExplorer] Mapped {/api/teacher-task/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.217 [INFO] [RouterExplorer] Mapped {/api/teacher-task/publish, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.218 [INFO] [RoutesResolver] WebRoleController {/api/web-role}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.218 [INFO] [RouterExplorer] Mapped {/api/web-role/user/join/add, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.218 [INFO] [RouterExplorer] Mapped {/api/web-role/template/teacher/default, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.218 [INFO] [RouterExplorer] Mapped {/api/web-role/template/official/delete, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.218 [INFO] [RouterExplorer] Mapped {/api/web-role/template/official/list/page, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.219 [INFO] [RoutesResolver] UserPasswordResetController {/api/user-password-reset}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.219 [INFO] [RouterExplorer] Mapped {/api/user-password-reset/create, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.219 [INFO] [RouterExplorer] Mapped {/api/user-password-reset/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.219 [INFO] [RouterExplorer] Mapped {/api/user-password-reset/handle, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.219 [INFO] [RoutesResolver] WebPermissionController {/api/web-permission}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.219 [INFO] [RouterExplorer] Mapped {/api/web-permission/extension/available, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.220 [INFO] [RouterExplorer] Mapped {/api/web-permission/block/byExtension, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.220 [INFO] [RouterExplorer] Mapped {/api/web-permission/getUserPermissions, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.220 [INFO] [RouterExplorer] Mapped {/api/web-permission/updateExtensionPermission, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.220 [INFO] [RouterExplorer] Mapped {/api/web-permission/updateBlockPermission, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.221 [INFO] [RouterExplorer] Mapped {/api/web-permission/role-templates, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.221 [INFO] [RouterExplorer] Mapped {/api/web-permission/user/:userId/role/:roleId, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.221 [INFO] [RouterExplorer] Mapped {/api/web-permission/template-permissions, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.222 [INFO] [RoutesResolver] BlockController {/block}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.222 [INFO] [RouterExplorer] Mapped {/block, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.222 [INFO] [RouterExplorer] Mapped {/block, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.222 [INFO] [RouterExplorer] Mapped {/block/block-id/:blockId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.223 [INFO] [RouterExplorer] Mapped {/block/extension/:extensionId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.223 [INFO] [RouterExplorer] Mapped {/block/type/:blockType, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.223 [INFO] [RouterExplorer] Mapped {/block/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.223 [INFO] [RouterExplorer] Mapped {/block/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.223 [INFO] [RouterExplorer] Mapped {/block/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.224 [INFO] [RoutesResolver] ExtensionsController {/extensions}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.224 [INFO] [RouterExplorer] Mapped {/extensions, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.224 [INFO] [RouterExplorer] Mapped {/extensions, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.224 [INFO] [RouterExplorer] Mapped {/extensions/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.225 [INFO] [RouterExplorer] Mapped {/extensions/extensionId/:extensionId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.225 [INFO] [RouterExplorer] Mapped {/extensions/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.225 [INFO] [RouterExplorer] Mapped {/extensions/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.225 [INFO] [RoutesResolver] ExtensionPermissionsController {/extension-permissions}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.225 [INFO] [RouterExplorer] Mapped {/extension-permissions, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.226 [INFO] [RouterExplorer] Mapped {/extension-permissions, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.226 [INFO] [RouterExplorer] Mapped {/extension-permissions/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.226 [INFO] [RouterExplorer] Mapped {/extension-permissions/extension/:extensionId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.226 [INFO] [RouterExplorer] Mapped {/extension-permissions/active/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.226 [INFO] [RouterExplorer] Mapped {/extension-permissions/check/:userId/:extensionId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.227 [INFO] [RouterExplorer] Mapped {/extension-permissions/batch/:extensionId, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.227 [INFO] [RouterExplorer] Mapped {/extension-permissions/:id/enable, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.227 [INFO] [RouterExplorer] Mapped {/extension-permissions/:id/disable, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.227 [INFO] [RouterExplorer] Mapped {/extension-permissions/:id/expire, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.228 [INFO] [RouterExplorer] Mapped {/extension-permissions/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.228 [INFO] [RouterExplorer] Mapped {/extension-permissions/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.228 [INFO] [RouterExplorer] Mapped {/extension-permissions/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.228 [INFO] [RoutesResolver] BlockPermissionsController {/block-permissions}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.229 [INFO] [RouterExplorer] Mapped {/block-permissions, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.229 [INFO] [RouterExplorer] Mapped {/block-permissions, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.229 [INFO] [RouterExplorer] Mapped {/block-permissions/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.229 [INFO] [RouterExplorer] Mapped {/block-permissions/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.229 [INFO] [RouterExplorer] Mapped {/block-permissions/extension/:extensionId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.230 [INFO] [RouterExplorer] Mapped {/block-permissions/block/:blockId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.230 [INFO] [RouterExplorer] Mapped {/block-permissions/check/:userId/:blockId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.230 [INFO] [RouterExplorer] Mapped {/block-permissions/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.230 [INFO] [RouterExplorer] Mapped {/block-permissions/:id/toggle, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.230 [INFO] [RouterExplorer] Mapped {/block-permissions/:id/expire, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.230 [INFO] [RouterExplorer] Mapped {/block-permissions/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.231 [INFO] [RoutesResolver] WebReportController {/api/web/report}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.231 [INFO] [RouterExplorer] Mapped {/api/web/report/create, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.231 [INFO] [RouterExplorer] Mapped {/api/web/report/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.231 [INFO] [RouterExplorer] Mapped {/api/web/report/admin/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.231 [INFO] [RouterExplorer] Mapped {/api/web/report/admin/:id/handle, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.231 [INFO] [RouterExplorer] Mapped {/api/web/report/admin/batchHandle, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.232 [INFO] [RouterExplorer] Mapped {/api/web/report/admin/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.232 [INFO] [RouterExplorer] Mapped {/api/web/report/admin/stats, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.232 [INFO] [RoutesResolver] UserReportController {/user-report}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.232 [INFO] [RouterExplorer] Mapped {/user-report, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.233 [INFO] [RouterExplorer] Mapped {/user-report, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.233 [INFO] [RouterExplorer] Mapped {/user-report/status/:status, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.233 [INFO] [RouterExplorer] Mapped {/user-report/reporter/:reporterId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.233 [INFO] [RouterExplorer] Mapped {/user-report/target/:targetId/:targetType, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.233 [INFO] [RouterExplorer] Mapped {/user-report/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.234 [INFO] [RouterExplorer] Mapped {/user-report/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.234 [INFO] [RouterExplorer] Mapped {/user-report/:id/handle, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.234 [INFO] [RouterExplorer] Mapped {/user-report/:id/reject, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.234 [INFO] [RouterExplorer] Mapped {/user-report/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.235 [INFO] [RoutesResolver] WebTemplateFolderController {/web-template-folder}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.235 [INFO] [RoutesResolver] WebDocController {/api/web/doc}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.235 [INFO] [RouterExplorer] Mapped {/api/web/doc/create, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.235 [INFO] [RouterExplorer] Mapped {/api/web/doc/getByDocId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.236 [INFO] [RouterExplorer] Mapped {/api/web/doc/update, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.236 [INFO] [RouterExplorer] Mapped {/api/web/doc/delete, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.236 [INFO] [RouterExplorer] Mapped {/api/web/doc/getUserDocs, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.236 [INFO] [RouterExplorer] Mapped {/api/web/doc/getDocById, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.236 [INFO] [RouterExplorer] Mapped {/api/web/doc/getAllDocs, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.237 [INFO] [RouterExplorer] Mapped {/api/web/doc/findByTitle, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.237 [INFO] [RouterExplorer] Mapped {/api/web/doc/findByTag, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.237 [INFO] [RouterExplorer] Mapped {/api/web/doc/findPublicDocs, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.237 [INFO] [RoutesResolver] DocController {/doc}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.237 [INFO] [RouterExplorer] Mapped {/doc, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.238 [INFO] [RouterExplorer] Mapped {/doc, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.238 [INFO] [RouterExplorer] Mapped {/doc/doc-id/:docId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.238 [INFO] [RouterExplorer] Mapped {/doc/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.238 [INFO] [RouterExplorer] Mapped {/doc/search/title, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.238 [INFO] [RouterExplorer] Mapped {/doc/tag/:tag, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.238 [INFO] [RouterExplorer] Mapped {/doc/public, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.239 [INFO] [RouterExplorer] Mapped {/doc/visible/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.239 [INFO] [RouterExplorer] Mapped {/doc/:id/soft-remove, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.239 [INFO] [RouterExplorer] Mapped {/doc/:id/restore, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.239 [INFO] [RouterExplorer] Mapped {/doc/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.240 [INFO] [RouterExplorer] Mapped {/doc/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.240 [INFO] [RouterExplorer] Mapped {/doc/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.240 [INFO] [RoutesResolver] WebKeyPackageController {/api/web/key_package}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.240 [INFO] [RouterExplorer] Mapped {/api/web/key_package/use, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.240 [INFO] [RouterExplorer] Mapped {/api/web/key_package/records, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.241 [INFO] [RouterExplorer] Mapped {/api/web/key_package/validate, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.241 [INFO] [RouterExplorer] Mapped {/api/web/key_package/add, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.241 [INFO] [RouterExplorer] Mapped {/api/web/key_package/update, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.241 [INFO] [RouterExplorer] Mapped {/api/web/key_package/delete, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.241 [INFO] [RouterExplorer] Mapped {/api/web/key_package/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.242 [INFO] [RouterExplorer] Mapped {/api/web/key_package/info, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.242 [INFO] [RouterExplorer] Mapped {/api/web/key_package/batch-create, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.242 [INFO] [RoutesResolver] KeyPackageRecordController {/key-package-record}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.242 [INFO] [RouterExplorer] Mapped {/key-package-record, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.242 [INFO] [RouterExplorer] Mapped {/key-package-record, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.243 [INFO] [RouterExplorer] Mapped {/key-package-record/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.243 [INFO] [RouterExplorer] Mapped {/key-package-record/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.243 [INFO] [RouterExplorer] Mapped {/key-package-record/package/:packageId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.243 [INFO] [RouterExplorer] Mapped {/key-package-record/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.243 [INFO] [RouterExplorer] Mapped {/key-package-record/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.243 [INFO] [RoutesResolver] WebPackageController {/api/web/package}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.244 [INFO] [RouterExplorer] Mapped {/api/web/package/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.244 [INFO] [RouterExplorer] Mapped {/api/web/package/info/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.244 [INFO] [RouterExplorer] Mapped {/api/web/package/add, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.244 [INFO] [RouterExplorer] Mapped {/api/web/package/update, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.244 [INFO] [RouterExplorer] Mapped {/api/web/package/delete, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.245 [INFO] [RouterExplorer] Mapped {/api/web/package/available, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.245 [INFO] [RouterExplorer] Mapped {/api/web/package/assign, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.245 [INFO] [RouterExplorer] Mapped {/api/web/package/user/records, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.246 [INFO] [RouterExplorer] Mapped {/api/web/package/user/current, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.246 [INFO] [RouterExplorer] Mapped {/api/web/package/user/message-center, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.246 [INFO] [RouterExplorer] Mapped {/api/web/package/user/details/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.246 [INFO] [RouterExplorer] Mapped {/api/web/package/user/check, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.247 [INFO] [RoutesResolver] WebPointPermissionController {/api/web-point-permission}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.247 [INFO] [RouterExplorer] Mapped {/api/web-point-permission/assign, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.247 [INFO] [RouterExplorer] Mapped {/api/web-point-permission/batchAssign, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.247 [INFO] [RouterExplorer] Mapped {/api/web-point-permission/student, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.248 [INFO] [RouterExplorer] Mapped {/api/web-point-permission/teacher, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.248 [INFO] [RouterExplorer] Mapped {/api/web-point-permission/check, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.248 [INFO] [RouterExplorer] Mapped {/api/web-point-permission/use, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.248 [INFO] [RoutesResolver] OssController {/api/oss}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.249 [INFO] [RouterExplorer] Mapped {/api/oss/upload, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.249 [INFO] [RouterExplorer] Mapped {/api/oss/upload-form, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.249 [INFO] [RouterExplorer] Mapped {/api/oss/delete, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.249 [INFO] [RoutesResolver] UserSrchImageSegmentController {/api/user-srch-image-segment}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.249 [INFO] [RouterExplorer] Mapped {/api/user-srch-image-segment/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.250 [INFO] [RouterExplorer] Mapped {/api/user-srch-image-segment/add, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.250 [INFO] [RouterExplorer] Mapped {/api/user-srch-image-segment/status/:taskId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.250 [INFO] [RoutesResolver] UserImageSegmentController {/user-image-segment}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.250 [INFO] [RouterExplorer] Mapped {/user-image-segment, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.250 [INFO] [RouterExplorer] Mapped {/user-image-segment, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.251 [INFO] [RouterExplorer] Mapped {/user-image-segment/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.251 [INFO] [RouterExplorer] Mapped {/user-image-segment/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.251 [INFO] [RouterExplorer] Mapped {/user-image-segment/task/:taskId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.251 [INFO] [RouterExplorer] Mapped {/user-image-segment/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.254 [INFO] [RouterExplorer] Mapped {/user-image-segment/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.255 [INFO] [RoutesResolver] WeixinMessageController {/weixin}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.255 [INFO] [RouterExplorer] Mapped {/weixin/message, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.255 [INFO] [RouterExplorer] Mapped {/weixin/message, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.256 [INFO] [RoutesResolver] WeixinController {/api/weixin}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.256 [INFO] [RouterExplorer] Mapped {/api/weixin/bindPhone, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.256 [INFO] [RouterExplorer] Mapped {/api/weixin/checkBindStatus, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.256 [INFO] [RouterExplorer] Mapped {/api/weixin/bindPage, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.257 [INFO] [RoutesResolver] TeacherAuditController {/api/teacher-audit}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.257 [INFO] [RouterExplorer] Mapped {/api/teacher-audit, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.257 [INFO] [RouterExplorer] Mapped {/api/teacher-audit, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.257 [INFO] [RouterExplorer] Mapped {/api/teacher-audit/getTeacherAuthByCondition, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.258 [INFO] [RouterExplorer] Mapped {/api/teacher-audit/teacher, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.258 [INFO] [RouterExplorer] Mapped {/api/teacher-audit/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.258 [INFO] [RouterExplorer] Mapped {/api/teacher-audit/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.258 [INFO] [RouterExplorer] Mapped {/api/teacher-audit/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.259 [INFO] [RouterExplorer] Mapped {/api/teacher-audit/info/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.259 [INFO] [RouterExplorer] Mapped {/api/teacher-audit/search/name/:name, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.259 [INFO] [RouterExplorer] Mapped {/api/teacher-audit/search/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.259 [INFO] [RouterExplorer] Mapped {/api/teacher-audit/search/name-like/:pattern, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.260 [INFO] [RouterExplorer] Mapped {/api/teacher-audit/review, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.260 [INFO] [RoutesResolver] AttachmentController {/api/attachment}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.260 [INFO] [RouterExplorer] Mapped {/api/attachment, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.260 [INFO] [RouterExplorer] Mapped {/api/attachment/batch, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.260 [INFO] [RouterExplorer] Mapped {/api/attachment, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.260 [INFO] [RouterExplorer] Mapped {/api/attachment/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.261 [INFO] [RouterExplorer] Mapped {/api/attachment/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.261 [INFO] [RouterExplorer] Mapped {/api/attachment/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.261 [INFO] [RoutesResolver] SelfAssessmentItemController {/api/self-assessment-item}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.261 [INFO] [RouterExplorer] Mapped {/api/self-assessment-item/task/:taskId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.261 [INFO] [RouterExplorer] Mapped {/api/self-assessment-item/search, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.262 [INFO] [RoutesResolver] ZwwController {/zww}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.262 [INFO] [RouterExplorer] Mapped {/zww, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.262 [INFO] [RouterExplorer] Mapped {/zww, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.262 [INFO] [RouterExplorer] Mapped {/zww/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.262 [INFO] [RouterExplorer] Mapped {/zww/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.263 [INFO] [RouterExplorer] Mapped {/zww/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.263 [INFO] [RoutesResolver] PackageOrderBusinessController {/api/v1/package-order}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.265 [INFO] [RouterExplorer] Mapped {/api/v1/package-order/purchase, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.265 [INFO] [RouterExplorer] Mapped {/api/v1/package-order/payment-callback, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.265 [INFO] [RouterExplorer] Mapped {/api/v1/package-order/order-status/:orderNo, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.266 [INFO] [RouterExplorer] Mapped {/api/v1/package-order/my-orders, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.266 [INFO] [RouterExplorer] Mapped {/api/v1/package-order/cancel/:orderNo, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.266 [INFO] [RouterExplorer] Mapped {/api/v1/package-order/packages, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.266 [INFO] [RouterExplorer] Mapped {/api/v1/package-order/order/:orderNo, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.266 [INFO] [RoutesResolver] PackageOrderController {/package-order}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.267 [INFO] [RouterExplorer] Mapped {/package-order, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.267 [INFO] [RouterExplorer] Mapped {/package-order, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.267 [INFO] [RouterExplorer] Mapped {/package-order/search, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.267 [INFO] [RouterExplorer] Mapped {/package-order/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.267 [INFO] [RouterExplorer] Mapped {/package-order/user/:userId/stats, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.268 [INFO] [RouterExplorer] Mapped {/package-order/order-no/:orderNo, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.268 [INFO] [RouterExplorer] Mapped {/package-order/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.268 [INFO] [RouterExplorer] Mapped {/package-order/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.268 [INFO] [RouterExplorer] Mapped {/package-order/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.268 [INFO] [RouterExplorer] Mapped {/package-order/payment/:orderNo, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.268 [INFO] [RouterExplorer] Mapped {/package-order/cancel/:orderNo, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.269 [INFO] [RoutesResolver] PackagePricingController {/package-pricing}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.269 [INFO] [RouterExplorer] Mapped {/package-pricing, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.269 [INFO] [RouterExplorer] Mapped {/package-pricing, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.269 [INFO] [RouterExplorer] Mapped {/package-pricing/current-pricings, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.270 [INFO] [RouterExplorer] Mapped {/package-pricing/package/:packageId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.270 [INFO] [RouterExplorer] Mapped {/package-pricing/package/:packageId/all, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.270 [INFO] [RouterExplorer] Mapped {/package-pricing/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.270 [INFO] [RouterExplorer] Mapped {/package-pricing/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.271 [INFO] [RouterExplorer] Mapped {/package-pricing/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.271 [INFO] [RoutesResolver] PaymentController {/v1/payment}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.271 [INFO] [RouterExplorer] Mapped {/v1/payment/create, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.271 [INFO] [RouterExplorer] Mapped {/v1/payment/query/:outTradeNo, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.271 [INFO] [RouterExplorer] Mapped {/v1/payment/close, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.271 [INFO] [RouterExplorer] Mapped {/v1/payment/notify/:channel, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.272 [INFO] [RouterExplorer] Mapped {/v1/payment/refund/notify/:channel, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.272 [INFO] [RouterExplorer] Mapped {/v1/payment/return/:channel, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.272 [INFO] [RouterExplorer] Mapped {/v1/payment/notify/wechatpay, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.272 [INFO] [RouterExplorer] Mapped {/v1/payment/notify/alipay/refund, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.272 [INFO] [RouterExplorer] Mapped {/v1/payment/notify/wechatpay/refund, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.273 [INFO] [RouterExplorer] Mapped {/v1/payment/order/:orderNo, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.273 [INFO] [RouterExplorer] Mapped {/v1/payment/test/wechat/refund, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.273 [INFO] [RoutesResolver] RefundController {/v1/payment/refund}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.273 [INFO] [RouterExplorer] Mapped {/v1/payment/refund, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.273 [INFO] [RouterExplorer] Mapped {/v1/payment/refund/query, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.274 [INFO] [RouterExplorer] Mapped {/v1/payment/refund/:refundNo, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.274 [INFO] [RouterExplorer] Mapped {/v1/payment/refund/:refundNo/check, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.274 [INFO] [RoutesResolver] PaymentRecordController {/api/v1/payment-records}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.274 [INFO] [RouterExplorer] Mapped {/api/v1/payment-records, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.275 [INFO] [RouterExplorer] Mapped {/api/v1/payment-records, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.275 [INFO] [RouterExplorer] Mapped {/api/v1/payment-records/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.275 [INFO] [RouterExplorer] Mapped {/api/v1/payment-records/order/:orderNo, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.275 [INFO] [RouterExplorer] Mapped {/api/v1/payment-records/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.275 [INFO] [RouterExplorer] Mapped {/api/v1/payment-records/:id/status, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.276 [INFO] [RoutesResolver] NotificationRecordController {/api/v1/notification-records}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.276 [INFO] [RouterExplorer] Mapped {/api/v1/notification-records, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.276 [INFO] [RouterExplorer] Mapped {/api/v1/notification-records, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.276 [INFO] [RouterExplorer] Mapped {/api/v1/notification-records/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.276 [INFO] [RouterExplorer] Mapped {/api/v1/notification-records/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.277 [INFO] [RouterExplorer] Mapped {/api/v1/notification-records/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.277 [INFO] [RouterExplorer] Mapped {/api/v1/notification-records/retry/:id, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.277 [INFO] [RouterExplorer] Mapped {/api/v1/notification-records/batch/status, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.277 [INFO] [RoutesResolver] PaymentLogController {/api/v1/payment-logs}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.278 [INFO] [RouterExplorer] Mapped {/api/v1/payment-logs, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.278 [INFO] [RouterExplorer] Mapped {/api/v1/payment-logs, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.278 [INFO] [RouterExplorer] Mapped {/api/v1/payment-logs/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.278 [INFO] [RouterExplorer] Mapped {/api/v1/payment-logs/order/:orderNo, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.279 [INFO] [RouterExplorer] Mapped {/api/v1/payment-logs/refund/:refundNo, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.279 [INFO] [RouterExplorer] Mapped {/api/v1/payment-logs/cleanup, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.279 [INFO] [RoutesResolver] PaymentOrderController {/payment-order}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.279 [INFO] [RouterExplorer] Mapped {/payment-order, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.280 [INFO] [RouterExplorer] Mapped {/payment-order, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.280 [INFO] [RouterExplorer] Mapped {/payment-order/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.280 [INFO] [RouterExplorer] Mapped {/payment-order/business-order-id/:businessOrderId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.280 [INFO] [RouterExplorer] Mapped {/payment-order/channel-order-id/:channelOrderId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.280 [INFO] [RouterExplorer] Mapped {/payment-order/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.281 [INFO] [RouterExplorer] Mapped {/payment-order/status/:status, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.281 [INFO] [RouterExplorer] Mapped {/payment-order/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.281 [INFO] [RouterExplorer] Mapped {/payment-order/:id/status, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.281 [INFO] [RouterExplorer] Mapped {/payment-order/:id/notify, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.282 [INFO] [RouterExplorer] Mapped {/payment-order/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.282 [INFO] [RoutesResolver] PaymentRefundController {/payment-refund}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.282 [INFO] [RouterExplorer] Mapped {/payment-refund, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.282 [INFO] [RouterExplorer] Mapped {/payment-refund, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.283 [INFO] [RouterExplorer] Mapped {/payment-refund/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.283 [INFO] [RouterExplorer] Mapped {/payment-refund/business-refund-id/:businessRefundId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.283 [INFO] [RouterExplorer] Mapped {/payment-refund/payment-order/:paymentOrderId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.283 [INFO] [RouterExplorer] Mapped {/payment-refund/channel-refund-id/:channelRefundId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.283 [INFO] [RouterExplorer] Mapped {/payment-refund/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.284 [INFO] [RouterExplorer] Mapped {/payment-refund/status/:status, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.284 [INFO] [RouterExplorer] Mapped {/payment-refund/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.284 [INFO] [RouterExplorer] Mapped {/payment-refund/:id/status, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.284 [INFO] [RouterExplorer] Mapped {/payment-refund/:id/notify, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.285 [INFO] [RouterExplorer] Mapped {/payment-refund/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.285 [INFO] [RoutesResolver] UserLoginLogController {/api/user-login-log}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.285 [INFO] [RouterExplorer] Mapped {/api/user-login-log, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.285 [INFO] [RouterExplorer] Mapped {/api/user-login-log, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.285 [INFO] [RouterExplorer] Mapped {/api/user-login-log/user/:userId/history, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.285 [INFO] [RouterExplorer] Mapped {/api/user-login-log/user/:userId/last, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.286 [INFO] [RouterExplorer] Mapped {/api/user-login-log/user/:userId/stats, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.286 [INFO] [RouterExplorer] Mapped {/api/user-login-log/user/:userId/check-abnormal, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.286 [INFO] [RouterExplorer] Mapped {/api/user-login-log/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.286 [INFO] [RouterExplorer] Mapped {/api/user-login-log/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.286 [INFO] [RouterExplorer] Mapped {/api/user-login-log/user/:userId/logout, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.287 [INFO] [RouterExplorer] Mapped {/api/user-login-log/debug/user/:userId/recent, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.287 [INFO] [RoutesResolver] UserTagController {/api/v1/tag}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.287 [INFO] [RouterExplorer] Mapped {/api/v1/tag/createTag, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.287 [INFO] [RouterExplorer] Mapped {/api/v1/tag/updateTag/:id, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.287 [INFO] [RouterExplorer] Mapped {/api/v1/tag/deleteTag/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.288 [INFO] [RouterExplorer] Mapped {/api/v1/tag/infoTag/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.288 [INFO] [RouterExplorer] Mapped {/api/v1/tag/listTag, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.288 [INFO] [RoutesResolver] TagController {/tag}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.288 [INFO] [RouterExplorer] Mapped {/tag, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.288 [INFO] [RouterExplorer] Mapped {/tag, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.289 [INFO] [RouterExplorer] Mapped {/tag/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.289 [INFO] [RouterExplorer] Mapped {/tag/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.289 [INFO] [RouterExplorer] Mapped {/tag/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.289 [INFO] [RoutesResolver] WebActivityController {/api/v1/activity}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.289 [INFO] [RouterExplorer] Mapped {/api/v1/activity/createActivity, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.290 [INFO] [RouterExplorer] Mapped {/api/v1/activity/updateActivity/:id, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.290 [INFO] [RouterExplorer] Mapped {/api/v1/activity/deleteActivity/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.290 [INFO] [RouterExplorer] Mapped {/api/v1/activity/infoActivity/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.290 [INFO] [RouterExplorer] Mapped {/api/v1/activity/listActivity, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.290 [INFO] [RouterExplorer] Mapped {/api/v1/activity/infoActivityWithWorks/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.290 [INFO] [RouterExplorer] Mapped {/api/v1/activity/infoActivityContent/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.291 [INFO] [RouterExplorer] Mapped {/api/v1/activity/addWorks/:activityId, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.291 [INFO] [RouterExplorer] Mapped {/api/v1/activity/setAwardedWorks/:activityId, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.291 [INFO] [RouterExplorer] Mapped {/api/v1/activity/uploadSignature, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.291 [INFO] [RouterExplorer] Mapped {/api/v1/activity/submitRegistration, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.292 [INFO] [RouterExplorer] Mapped {/api/v1/activity/checkRegistration/:activityId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.292 [INFO] [RouterExplorer] Mapped {/api/v1/activity/cancelRegistration/:activityId, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.293 [INFO] [RouterExplorer] Mapped {/api/v1/activity/myRegistrations, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.293 [INFO] [RouterExplorer] Mapped {/api/v1/activity/registrationStatistics/:activityId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.293 [INFO] [RoutesResolver] ActivityController {/activity}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.294 [INFO] [RouterExplorer] Mapped {/activity, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.294 [INFO] [RouterExplorer] Mapped {/activity, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.294 [INFO] [RouterExplorer] Mapped {/activity/status/:status, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.295 [INFO] [RouterExplorer] Mapped {/activity/active, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.295 [INFO] [RouterExplorer] Mapped {/activity/upcoming, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.295 [INFO] [RouterExplorer] Mapped {/activity/creator/:creatorId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.295 [INFO] [RouterExplorer] Mapped {/activity/type/:activityType, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.295 [INFO] [RouterExplorer] Mapped {/activity/:id/status/:status, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.296 [INFO] [RouterExplorer] Mapped {/activity/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.296 [INFO] [RouterExplorer] Mapped {/activity/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.296 [INFO] [RouterExplorer] Mapped {/activity/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.296 [INFO] [RouterExplorer] Mapped {/activity/:id/hard, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.296 [INFO] [RoutesResolver] ActivitySubmitController {/activity-submit}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.297 [INFO] [RouterExplorer] Mapped {/activity-submit, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.297 [INFO] [RouterExplorer] Mapped {/activity-submit, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.297 [INFO] [RouterExplorer] Mapped {/activity-submit/activity/:activityId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.297 [INFO] [RouterExplorer] Mapped {/activity-submit/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.297 [INFO] [RouterExplorer] Mapped {/activity-submit/check/:activityId/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.297 [INFO] [RouterExplorer] Mapped {/activity-submit/statistics/:activityId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.297 [INFO] [RouterExplorer] Mapped {/activity-submit/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.298 [INFO] [RouterExplorer] Mapped {/activity-submit/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.298 [INFO] [RouterExplorer] Mapped {/activity-submit/:id/cancel, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.298 [INFO] [RouterExplorer] Mapped {/activity-submit/cancel/:activityId/:userId, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.298 [INFO] [RouterExplorer] Mapped {/activity-submit/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.298 [INFO] [RoutesResolver] ActivityEventsTaskController {/activity-events-task}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.299 [INFO] [RouterExplorer] Mapped {/activity-events-task, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.299 [INFO] [RouterExplorer] Mapped {/activity-events-task, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.299 [INFO] [RouterExplorer] Mapped {/activity-events-task/statistics, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.299 [INFO] [RouterExplorer] Mapped {/activity-events-task/status/:status, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.300 [INFO] [RouterExplorer] Mapped {/activity-events-task/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.300 [INFO] [RouterExplorer] Mapped {/activity-events-task/creator/:creatorId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.300 [INFO] [RouterExplorer] Mapped {/activity-events-task/school, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.300 [INFO] [RouterExplorer] Mapped {/activity-events-task/activity/:activityId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.301 [INFO] [RouterExplorer] Mapped {/activity-events-task/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.301 [INFO] [RouterExplorer] Mapped {/activity-events-task/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.301 [INFO] [RouterExplorer] Mapped {/activity-events-task/:id/status, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.302 [INFO] [RouterExplorer] Mapped {/activity-events-task/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.302 [INFO] [RoutesResolver] WebActivityTagController {/api/v1/activity_tag}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.302 [INFO] [RouterExplorer] Mapped {/api/v1/activity_tag/add-tags, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.303 [INFO] [RouterExplorer] Mapped {/api/v1/activity_tag/edit-tags/:activityId, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.303 [INFO] [RouterExplorer] Mapped {/api/v1/activity_tag/remove-tags/:activityId, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.303 [INFO] [RouterExplorer] Mapped {/api/v1/activity_tag/get-activity/:activityId/tags, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.304 [INFO] [RouterExplorer] Mapped {/api/v1/activity_tag/get-tag/:tagId/activities, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.304 [INFO] [RoutesResolver] ActivityTagController {/activity-tag}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.304 [INFO] [RouterExplorer] Mapped {/activity-tag, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.304 [INFO] [RouterExplorer] Mapped {/activity-tag, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.304 [INFO] [RouterExplorer] Mapped {/activity-tag/activity/:activityId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.305 [INFO] [RouterExplorer] Mapped {/activity-tag/tag/:tagId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.305 [INFO] [RouterExplorer] Mapped {/activity-tag/activity/:activityId, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.306 [INFO] [RouterExplorer] Mapped {/activity-tag/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.307 [INFO] [RouterExplorer] Mapped {/activity-tag/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.307 [INFO] [RouterExplorer] Mapped {/activity-tag/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.308 [INFO] [RouterExplorer] Mapped {/activity-tag/:id/hard, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.308 [INFO] [RoutesResolver] WebActivityWorkController {/api/v1/activity_work}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.309 [INFO] [RouterExplorer] Mapped {/api/v1/activity_work/add-works, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.309 [INFO] [RouterExplorer] Mapped {/api/v1/activity_work/add-image, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.309 [INFO] [RouterExplorer] Mapped {/api/v1/activity_work/update-works/:activityId, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.310 [INFO] [RouterExplorer] Mapped {/api/v1/activity_work/check-submitted/:activityId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.310 [INFO] [RouterExplorer] Mapped {/api/v1/activity_work/list/:activityId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.310 [INFO] [RouterExplorer] Mapped {/api/v1/activity_work/remove/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.310 [INFO] [RouterExplorer] Mapped {/api/v1/activity_work/batch-remove, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.311 [INFO] [RouterExplorer] Mapped {/api/v1/activity_work/set-awarded/:id, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.311 [INFO] [RouterExplorer] Mapped {/api/v1/activity_work/update-category/:id, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.311 [INFO] [RouterExplorer] Mapped {/api/v1/activity_work/update-status/:id, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.313 [INFO] [RouterExplorer] Mapped {/api/v1/activity_work/cancel/:id, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.313 [INFO] [RoutesResolver] ActivityWorkController {/activity-work}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.314 [INFO] [RouterExplorer] Mapped {/activity-work, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.314 [INFO] [RouterExplorer] Mapped {/activity-work, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.314 [INFO] [RouterExplorer] Mapped {/activity-work/activity/:activityId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.315 [INFO] [RouterExplorer] Mapped {/activity-work/work/:workId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.315 [INFO] [RouterExplorer] Mapped {/activity-work/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.315 [INFO] [RouterExplorer] Mapped {/activity-work/selected/:isSelected, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.315 [INFO] [RouterExplorer] Mapped {/activity-work/winners, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.316 [INFO] [RouterExplorer] Mapped {/activity-work/:id/selected/:isSelected, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.316 [INFO] [RouterExplorer] Mapped {/activity-work/:id/winner/:isWinner, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.316 [INFO] [RouterExplorer] Mapped {/activity-work/activity/:activityId, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.316 [INFO] [RouterExplorer] Mapped {/activity-work/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.317 [INFO] [RouterExplorer] Mapped {/activity-work/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.317 [INFO] [RouterExplorer] Mapped {/activity-work/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.317 [INFO] [RouterExplorer] Mapped {/activity-work/:id/hard, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.317 [INFO] [RoutesResolver] WebEventsTaskController {/api/v1/web/events-task}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.318 [INFO] [RouterExplorer] Mapped {/api/v1/web/events-task, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.318 [INFO] [RouterExplorer] Mapped {/api/v1/web/events-task/my-tasks, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.318 [INFO] [RouterExplorer] Mapped {/api/v1/web/events-task/activity/:activityId/tasks, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.318 [INFO] [RouterExplorer] Mapped {/api/v1/web/events-task/my-tasks/statistics, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.319 [INFO] [RouterExplorer] Mapped {/api/v1/web/events-task/my-tasks/upcoming, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.319 [INFO] [RouterExplorer] Mapped {/api/v1/web/events-task/my-tasks/ongoing, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.319 [INFO] [RouterExplorer] Mapped {/api/v1/web/events-task/my-tasks/status/:status, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.320 [INFO] [RouterExplorer] Mapped {/api/v1/web/events-task/my-tasks/search, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.320 [INFO] [RouterExplorer] Mapped {/api/v1/web/events-task/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.321 [INFO] [RouterExplorer] Mapped {/api/v1/web/events-task/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.321 [INFO] [RouterExplorer] Mapped {/api/v1/web/events-task/:id/status, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.321 [INFO] [RouterExplorer] Mapped {/api/v1/web/events-task/:id/admin-review, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.321 [INFO] [RouterExplorer] Mapped {/api/v1/web/events-task/:id/submit, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.321 [INFO] [RouterExplorer] Mapped {/api/v1/web/events-task/:id/submit-status, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.322 [INFO] [RouterExplorer] Mapped {/api/v1/web/events-task/batch/status, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.322 [INFO] [RouterExplorer] Mapped {/api/v1/web/events-task/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.322 [INFO] [RoutesResolver] ManagementController {/api/v1/course-management}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.322 [INFO] [RouterExplorer] Mapped {/api/v1/course-management/my-series, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.322 [INFO] [RouterExplorer] Mapped {/api/v1/course-management/series/:seriesId/courses, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.322 [INFO] [RouterExplorer] Mapped {/api/v1/course-management/series/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.323 [INFO] [RouterExplorer] Mapped {/api/v1/course-management/series, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.323 [INFO] [RouterExplorer] Mapped {/api/v1/course-management/series/:id, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.323 [INFO] [RouterExplorer] Mapped {/api/v1/course-management/series/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.323 [INFO] [RouterExplorer] Mapped {/api/v1/course-management/series/:seriesId/publish, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.324 [INFO] [RouterExplorer] Mapped {/api/v1/course-management/courses, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.324 [INFO] [RouterExplorer] Mapped {/api/v1/course-management/courses/:courseId/settings, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.324 [INFO] [RouterExplorer] Mapped {/api/v1/course-management/courses/:courseId/task-templates, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.324 [INFO] [RouterExplorer] Mapped {/api/v1/course-management/courses/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.325 [INFO] [RouterExplorer] Mapped {/api/v1/course-management/courses/:id, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.325 [INFO] [RouterExplorer] Mapped {/api/v1/course-management/courses/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.325 [INFO] [RouterExplorer] Mapped {/api/v1/course-management/series/:seriesId/course-orders, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.325 [INFO] [RoutesResolver] TeachingController {/api/v1/course-teaching}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.326 [INFO] [RouterExplorer] Mapped {/api/v1/course-teaching/one-click-start, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.326 [INFO] [RouterExplorer] Mapped {/api/v1/course-teaching/course-settings/:courseId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.326 [INFO] [RouterExplorer] Mapped {/api/v1/course-teaching/records, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.326 [INFO] [RoutesResolver] MarketplaceController {/api/v1/course-marketplace}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.326 [INFO] [RouterExplorer] Mapped {/api/v1/course-marketplace/series, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.327 [INFO] [RouterExplorer] Mapped {/api/v1/course-marketplace/series/:seriesId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.327 [INFO] [RouterExplorer] Mapped {/api/v1/course-marketplace/series/:seriesId/courses/:courseId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.327 [INFO] [RouterExplorer] Mapped {/api/v1/course-marketplace/tags, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.327 [INFO] [RouterExplorer] Mapped {/api/v1/course-marketplace/tags, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.328 [INFO] [RouterExplorer] Mapped {/api/v1/course-marketplace/tags/:id, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.328 [INFO] [RouterExplorer] Mapped {/api/v1/course-marketplace/tags/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.328 [INFO] [RouterExplorer] Mapped {/api/v1/course-marketplace/tags/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.328 [INFO] [RoutesResolver] AiImageGenerateController {/api/ai-image-generate}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.328 [INFO] [RouterExplorer] Mapped {/api/ai-image-generate, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.329 [INFO] [RouterExplorer] Mapped {/api/ai-image-generate/:jobId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.329 [INFO] [RoutesResolver] QueueController {/api/queue}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.329 [INFO] [RouterExplorer] Mapped {/api/queue/addJob, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.330 [INFO] [RouterExplorer] Mapped {/api/queue/clearAllQueues, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.330 [INFO] [RoutesResolver] MinimaxImageController {/api/minimax-image}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.330 [INFO] [RouterExplorer] Mapped {/api/minimax-image/generate, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.331 [INFO] [RoutesResolver] AiTextDialogueController {/api/ai-text-dialogue}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.331 [INFO] [RouterExplorer] Mapped {/api/ai-text-dialogue, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.332 [INFO] [RouterExplorer] Mapped {/api/ai-text-dialogue/:jobId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.332 [INFO] [RoutesResolver] ZhipuLlmController {/zhipu-llm}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.333 [INFO] [RouterExplorer] Mapped {/zhipu-llm/chat, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.333 [INFO] [RoutesResolver] AliQwenTurboController {/ali-qwen-turbo}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.333 [INFO] [RouterExplorer] Mapped {/ali-qwen-turbo/stream, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.334 [INFO] [RouterExplorer] Mapped {/ali-qwen-turbo/sse-callback, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.334 [INFO] [RoutesResolver] AiVisualRecognitionController {/api/ai-visual-recognition}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.334 [INFO] [RouterExplorer] Mapped {/api/ai-visual-recognition, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.334 [INFO] [RouterExplorer] Mapped {/api/ai-visual-recognition/:jobId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.335 [INFO] [RoutesResolver] AliQwenVisionController {/ali-qwen-vision}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.335 [INFO] [RouterExplorer] Mapped {/ali-qwen-vision/analyze, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.335 [INFO] [RoutesResolver] AiExpressionRecognitionController {/api/ai-expression-recognition}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.336 [INFO] [RouterExplorer] Mapped {/api/ai-expression-recognition, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.336 [INFO] [RouterExplorer] Mapped {/api/ai-expression-recognition/:jobId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.336 [INFO] [RoutesResolver] AliyunExpressionController {/api/aliyun-expression}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.336 [INFO] [RouterExplorer] Mapped {/api/aliyun-expression/recognize, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.337 [INFO] [RoutesResolver] AiFaceCompareController {/ai-face-compare}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.337 [INFO] [RouterExplorer] Mapped {/ai-face-compare, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.337 [INFO] [RouterExplorer] Mapped {/ai-face-compare/:jobId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.337 [INFO] [RoutesResolver] AliyunFaceCompareController {/aliyun-face-one-contrast-one}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.338 [INFO] [RouterExplorer] Mapped {/aliyun-face-one-contrast-one/compare, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.338 [INFO] [RoutesResolver] AiFaceRecognitionController {/api/ai-face-recognition}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.338 [INFO] [RouterExplorer] Mapped {/api/ai-face-recognition, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.338 [INFO] [RouterExplorer] Mapped {/api/ai-face-recognition/:jobId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.339 [INFO] [RoutesResolver] AliyunFaceRecognitionController {/api/aliyun-face-recognition}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.339 [INFO] [RouterExplorer] Mapped {/api/aliyun-face-recognition/recognize, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.339 [INFO] [RoutesResolver] AiImageEnhanceController {/api/ai-image-enhance}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.339 [INFO] [RouterExplorer] Mapped {/api/ai-image-enhance, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.339 [INFO] [RouterExplorer] Mapped {/api/ai-image-enhance/:jobId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.340 [INFO] [RoutesResolver] BaiduImageEnhanceController {/api/baidu-image-enhance}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.340 [INFO] [RouterExplorer] Mapped {/api/baidu-image-enhance/enhance, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.340 [INFO] [RoutesResolver] AiImageScoreController {/api/ai-image-score}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.340 [INFO] [RouterExplorer] Mapped {/api/ai-image-score, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.341 [INFO] [RouterExplorer] Mapped {/api/ai-image-score/:jobId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.341 [INFO] [RoutesResolver] AliyunImageScoreController {/api/aliyun-image-score}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.341 [INFO] [RouterExplorer] Mapped {/api/aliyun-image-score/assess, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.341 [INFO] [RoutesResolver] AiImageSegmentController {/api/ai-image-segment}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.341 [INFO] [RouterExplorer] Mapped {/api/ai-image-segment, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.342 [INFO] [RouterExplorer] Mapped {/api/ai-image-segment/:jobId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.342 [INFO] [RoutesResolver] AliyunSegmentImageController {/api/aliyun-segment-image}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.342 [INFO] [RouterExplorer] Mapped {/api/aliyun-segment-image/segment, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.342 [INFO] [RouterExplorer] Mapped {/api/aliyun-segment-image/download, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.343 [INFO] [RoutesResolver] AiSpeechSynthesisController {/api/ai-speech-synthesis}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.343 [INFO] [RouterExplorer] Mapped {/api/ai-speech-synthesis, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.343 [INFO] [RouterExplorer] Mapped {/api/ai-speech-synthesis/:jobId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.344 [INFO] [RoutesResolver] MinimaxTtsController {/api/minimax-tts}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.344 [INFO] [RouterExplorer] Mapped {/api/minimax-tts/config, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.344 [INFO] [RouterExplorer] Mapped {/api/minimax-tts/generate, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.345 [INFO] [RoutesResolver] AiSpeechRecognitionController {/api/ai-speech-recognition}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.345 [INFO] [RouterExplorer] Mapped {/api/ai-speech-recognition, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.345 [INFO] [RouterExplorer] Mapped {/api/ai-speech-recognition/:jobId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.345 [INFO] [RoutesResolver] XunfeiSpeechRecognitionController {/xunfei-speech-recognition}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.346 [INFO] [RouterExplorer] Mapped {/xunfei-speech-recognition/recognize-url, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.346 [INFO] [RouterExplorer] Mapped {/xunfei-speech-recognition/recognize-base64, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.346 [INFO] [RouterExplorer] Mapped {/xunfei-speech-recognition/recognize-file, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.346 [INFO] [RoutesResolver] TrainImageController {/api/scratch/train/image}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.347 [INFO] [RouterExplorer] Mapped {/api/scratch/train/image/stats, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.347 [INFO] [RouterExplorer] Mapped {/api/scratch/train/image/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.347 [INFO] [RouterExplorer] Mapped {/api/scratch/train/image/search, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.347 [INFO] [RouterExplorer] Mapped {/api/scratch/train/image/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.347 [INFO] [RouterExplorer] Mapped {/api/scratch/train/image/status, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.348 [INFO] [RouterExplorer] Mapped {/api/scratch/train/image/:id, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.348 [INFO] [RouterExplorer] Mapped {/api/scratch/train/image/delete, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.348 [INFO] [RouterExplorer] Mapped {/api/scratch/train/image/save, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.348 [INFO] [RoutesResolver] ImageTrainModelController {/image-train-model}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.349 [INFO] [RouterExplorer] Mapped {/image-train-model, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.349 [INFO] [RouterExplorer] Mapped {/image-train-model, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.350 [INFO] [RouterExplorer] Mapped {/image-train-model/public, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.350 [INFO] [RouterExplorer] Mapped {/image-train-model/creator/:creatorId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.350 [INFO] [RouterExplorer] Mapped {/image-train-model/class/:classId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.355 [INFO] [RouterExplorer] Mapped {/image-train-model/school/:schoolId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.356 [INFO] [RouterExplorer] Mapped {/image-train-model/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.357 [INFO] [RouterExplorer] Mapped {/image-train-model/:id/use, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.357 [INFO] [RouterExplorer] Mapped {/image-train-model/:id/train, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.358 [INFO] [RouterExplorer] Mapped {/image-train-model/:id/status/:status, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.358 [INFO] [RouterExplorer] Mapped {/image-train-model/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.359 [INFO] [RouterExplorer] Mapped {/image-train-model/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.363 [INFO] [RoutesResolver] TrainPoseController {/api/scratch/train/pose}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.363 [INFO] [RouterExplorer] Mapped {/api/scratch/train/pose/stats, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.364 [INFO] [RouterExplorer] Mapped {/api/scratch/train/pose/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.364 [INFO] [RouterExplorer] Mapped {/api/scratch/train/pose/search, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.364 [INFO] [RouterExplorer] Mapped {/api/scratch/train/pose/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.364 [INFO] [RouterExplorer] Mapped {/api/scratch/train/pose/status, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.365 [INFO] [RouterExplorer] Mapped {/api/scratch/train/pose/:id, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.365 [INFO] [RouterExplorer] Mapped {/api/scratch/train/pose/delete, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.365 [INFO] [RouterExplorer] Mapped {/api/scratch/train/pose/save, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.366 [INFO] [RoutesResolver] PoseTrainModelController {/pose-train-model}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.366 [INFO] [RouterExplorer] Mapped {/pose-train-model, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.366 [INFO] [RouterExplorer] Mapped {/pose-train-model, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.371 [INFO] [RouterExplorer] Mapped {/pose-train-model/public, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.371 [INFO] [RouterExplorer] Mapped {/pose-train-model/creator/:creatorId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.373 [INFO] [RouterExplorer] Mapped {/pose-train-model/class/:classId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.373 [INFO] [RouterExplorer] Mapped {/pose-train-model/school/:schoolId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.374 [INFO] [RouterExplorer] Mapped {/pose-train-model/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.374 [INFO] [RouterExplorer] Mapped {/pose-train-model/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.375 [INFO] [RouterExplorer] Mapped {/pose-train-model/:id/increment-use, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.375 [INFO] [RouterExplorer] Mapped {/pose-train-model/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.377 [INFO] [RoutesResolver] TrainSoundController {/api/scratch/train/sound}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.381 [INFO] [RouterExplorer] Mapped {/api/scratch/train/sound/stats, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.382 [INFO] [RouterExplorer] Mapped {/api/scratch/train/sound/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.382 [INFO] [RouterExplorer] Mapped {/api/scratch/train/sound/search, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.382 [INFO] [RouterExplorer] Mapped {/api/scratch/train/sound/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.383 [INFO] [RouterExplorer] Mapped {/api/scratch/train/sound/status, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.383 [INFO] [RouterExplorer] Mapped {/api/scratch/train/sound/:id, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.383 [INFO] [RouterExplorer] Mapped {/api/scratch/train/sound/delete, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.384 [INFO] [RouterExplorer] Mapped {/api/scratch/train/sound/save, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.384 [INFO] [RoutesResolver] AudioTrainModelController {/audio-train-model}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.390 [INFO] [RouterExplorer] Mapped {/audio-train-model, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.390 [INFO] [RouterExplorer] Mapped {/audio-train-model, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.391 [INFO] [RouterExplorer] Mapped {/audio-train-model/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.392 [INFO] [RouterExplorer] Mapped {/audio-train-model/creator/:creatorId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.392 [INFO] [RouterExplorer] Mapped {/audio-train-model/type/:modelType, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.393 [INFO] [RouterExplorer] Mapped {/audio-train-model/public/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.394 [INFO] [RouterExplorer] Mapped {/audio-train-model/class/:classId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.399 [INFO] [RouterExplorer] Mapped {/audio-train-model/school/:schoolId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.400 [INFO] [RouterExplorer] Mapped {/audio-train-model/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.400 [INFO] [RouterExplorer] Mapped {/audio-train-model/:id/use, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.400 [INFO] [RouterExplorer] Mapped {/audio-train-model/:id/train, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.401 [INFO] [RouterExplorer] Mapped {/audio-train-model/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.401 [INFO] [RoutesResolver] AiObjectDetectionController {/api/ai-object-detection}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.402 [INFO] [RouterExplorer] Mapped {/api/ai-object-detection, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.402 [INFO] [RouterExplorer] Mapped {/api/ai-object-detection/:jobId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.402 [INFO] [RoutesResolver] AliyunObjectDetectionController {/api/aliyun-object-detection}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.403 [INFO] [RouterExplorer] Mapped {/api/aliyun-object-detection/detect, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.407 [INFO] [RoutesResolver] AiStaticGestureRecognitionController {/api/scratch/ai-static-gesture-recognition}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.408 [INFO] [RouterExplorer] Mapped {/api/scratch/ai-static-gesture-recognition, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.409 [INFO] [RouterExplorer] Mapped {/api/scratch/ai-static-gesture-recognition/:jobId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.409 [INFO] [RoutesResolver] AliyunStaticGestureRecognitionController {/api/aliyun-gesture-recognition}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.409 [INFO] [RouterExplorer] Mapped {/api/aliyun-gesture-recognition/recognize, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.410 [INFO] [RouterExplorer] Mapped {/api/aliyun-gesture-recognition/:taskId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.410 [INFO] [RouterExplorer] Mapped {/api/aliyun-gesture-recognition/status, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.410 [INFO] [RoutesResolver] UserPointsOfflineMessageController {/user-points-offline-message}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.410 [INFO] [RouterExplorer] Mapped {/user-points-offline-message, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.411 [INFO] [RouterExplorer] Mapped {/user-points-offline-message, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.411 [INFO] [RouterExplorer] Mapped {/user-points-offline-message/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.412 [INFO] [RouterExplorer] Mapped {/user-points-offline-message/pending, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.412 [INFO] [RouterExplorer] Mapped {/user-points-offline-message/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.412 [INFO] [RouterExplorer] Mapped {/user-points-offline-message/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.412 [INFO] [RouterExplorer] Mapped {/user-points-offline-message/:id/mark-as-sent, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.413 [INFO] [RouterExplorer] Mapped {/user-points-offline-message/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.413 [INFO] [RoutesResolver] UserRolePermissionController {/user-role-permission}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.415 [INFO] [RouterExplorer] Mapped {/user-role-permission, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.416 [INFO] [RouterExplorer] Mapped {/user-role-permission/batch-assign, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.417 [INFO] [RouterExplorer] Mapped {/user-role-permission, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.418 [INFO] [RouterExplorer] Mapped {/user-role-permission/role/:roleId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.418 [INFO] [RouterExplorer] Mapped {/user-role-permission/permission/:permissionId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.418 [INFO] [RouterExplorer] Mapped {/user-role-permission/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.419 [INFO] [RouterExplorer] Mapped {/user-role-permission/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.419 [INFO] [RouterExplorer] Mapped {/user-role-permission/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.420 [INFO] [RouterExplorer] Mapped {/user-role-permission/role/:roleId/permission/:permissionId, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.420 [INFO] [RouterExplorer] Mapped {/user-role-permission/role/:roleId/all, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.421 [INFO] [RoutesResolver] UserRoleController {/user-role}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.422 [INFO] [RouterExplorer] Mapped {/user-role, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.429 [INFO] [RouterExplorer] Mapped {/user-role, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.431 [INFO] [RouterExplorer] Mapped {/user-role/active, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.432 [INFO] [RouterExplorer] Mapped {/user-role/default, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.435 [INFO] [RouterExplorer] Mapped {/user-role/code/:code, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.436 [INFO] [RouterExplorer] Mapped {/user-role/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.437 [INFO] [RouterExplorer] Mapped {/user-role/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.437 [INFO] [RouterExplorer] Mapped {/user-role/:id/status/:status, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.438 [INFO] [RouterExplorer] Mapped {/user-role/:id/set-default, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.438 [INFO] [RouterExplorer] Mapped {/user-role/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.439 [INFO] [RouterExplorer] Mapped {/user-role/condition, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.439 [INFO] [RoutesResolver] UserSchoolRelationController {/user-school-relation}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.439 [INFO] [RouterExplorer] Mapped {/user-school-relation, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.440 [INFO] [RouterExplorer] Mapped {/user-school-relation, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.440 [INFO] [RouterExplorer] Mapped {/user-school-relation/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.440 [INFO] [RouterExplorer] Mapped {/user-school-relation/school/:schoolId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.440 [INFO] [RouterExplorer] Mapped {/user-school-relation/school/:schoolId/students, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.441 [INFO] [RouterExplorer] Mapped {/user-school-relation/school/:schoolId/teachers, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.441 [INFO] [RouterExplorer] Mapped {/user-school-relation/check, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.441 [INFO] [RouterExplorer] Mapped {/user-school-relation/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.441 [INFO] [RouterExplorer] Mapped {/user-school-relation/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.442 [INFO] [RouterExplorer] Mapped {/user-school-relation/:id/role-type/:roleType, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.442 [INFO] [RouterExplorer] Mapped {/user-school-relation/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.442 [INFO] [RouterExplorer] Mapped {/user-school-relation/user/:userId/school/:schoolId, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.442 [INFO] [RouterExplorer] Mapped {/user-school-relation/user/:userId/all, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.443 [INFO] [RouterExplorer] Mapped {/user-school-relation/school/:schoolId/all, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.443 [INFO] [RoutesResolver] UserWorkLikeController {/user-work-like}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.443 [INFO] [RouterExplorer] Mapped {/user-work-like, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.443 [INFO] [RouterExplorer] Mapped {/user-work-like, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.444 [INFO] [RouterExplorer] Mapped {/user-work-like/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.444 [INFO] [RouterExplorer] Mapped {/user-work-like/target/:targetId/type/:targetType, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.444 [INFO] [RouterExplorer] Mapped {/user-work-like/check, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.444 [INFO] [RouterExplorer] Mapped {/user-work-like/toggle, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.445 [INFO] [RouterExplorer] Mapped {/user-work-like/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.445 [INFO] [RouterExplorer] Mapped {/user-work-like/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.445 [INFO] [RouterExplorer] Mapped {/user-work-like/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.446 [INFO] [RoutesResolver] ActivityAuditController {/activity-audit}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.446 [INFO] [RouterExplorer] Mapped {/activity-audit, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.446 [INFO] [RouterExplorer] Mapped {/activity-audit, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.447 [INFO] [RouterExplorer] Mapped {/activity-audit/activity/:activityId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.447 [INFO] [RouterExplorer] Mapped {/activity-audit/auditor/:auditorId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.447 [INFO] [RouterExplorer] Mapped {/activity-audit/result/:result, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.447 [INFO] [RouterExplorer] Mapped {/activity-audit/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.448 [INFO] [RouterExplorer] Mapped {/activity-audit/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.448 [INFO] [RouterExplorer] Mapped {/activity-audit/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.448 [INFO] [RouterExplorer] Mapped {/activity-audit/:id/hard, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.449 [INFO] [RoutesResolver] ParticipationAuditController {/participation-audit}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.449 [INFO] [RouterExplorer] Mapped {/participation-audit, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.449 [INFO] [RouterExplorer] Mapped {/participation-audit, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.449 [INFO] [RouterExplorer] Mapped {/participation-audit/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.449 [INFO] [RouterExplorer] Mapped {/participation-audit/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.450 [INFO] [RouterExplorer] Mapped {/participation-audit/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.450 [INFO] [RoutesResolver] WorkAuditController {/work-audit}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.450 [INFO] [RouterExplorer] Mapped {/work-audit, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.450 [INFO] [RouterExplorer] Mapped {/work-audit, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.451 [INFO] [RouterExplorer] Mapped {/work-audit/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.451 [INFO] [RouterExplorer] Mapped {/work-audit/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.451 [INFO] [RouterExplorer] Mapped {/work-audit/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.451 [INFO] [RoutesResolver] StudentSelfAssessmentSubmissionController {/student-self-assessment-submission}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.452 [INFO] [RouterExplorer] Mapped {/student-self-assessment-submission/bulk, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.452 [INFO] [RouterExplorer] Mapped {/student-self-assessment-submission/assignment/:assignmentId/student/:studentId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.452 [INFO] [RouterExplorer] Mapped {/student-self-assessment-submission/item/:itemId/submissions, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.453 [INFO] [RouterExplorer] Mapped {/student-self-assessment-submission, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.453 [INFO] [RouterExplorer] Mapped {/student-self-assessment-submission, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.453 [INFO] [RouterExplorer] Mapped {/student-self-assessment-submission/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.454 [INFO] [RouterExplorer] Mapped {/student-self-assessment-submission/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.454 [INFO] [RouterExplorer] Mapped {/student-self-assessment-submission/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.454 [INFO] [RoutesResolver] EncryptionController {/api/encryption}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.455 [INFO] [RouterExplorer] Mapped {/api/encryption/publicKey, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.455 [INFO] [RouterExplorer] Mapped {/api/encryption/keyStatus, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.456 [INFO] [RouterExplorer] Mapped {/api/encryption/session, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.456 [INFO] [RouterExplorer] Mapped {/api/encryption/secureSession, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.456 [INFO] [RouterExplorer] Mapped {/api/encryption/renew-session/:sessionId, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.457 [INFO] [RouterExplorer] Mapped {/api/encryption/session/:sessionId, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.457 [INFO] [RouterExplorer] Mapped {/api/encryption/session-stats, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.458 [INFO] [RouterExplorer] Mapped {/api/encryption/cleanup-sessions, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.458 [INFO] [RouterExplorer] Mapped {/api/encryption/create-session-debug, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.459 [INFO] [RoutesResolver] EncryptExampleController {/encrypt-example}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.459 [INFO] [RouterExplorer] Mapped {/encrypt-example/public-data, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.459 [INFO] [RouterExplorer] Mapped {/encrypt-example/secure-data, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.459 [INFO] [RouterExplorer] Mapped {/encrypt-example/partial-secure/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.460 [INFO] [RouterExplorer] Mapped {/encrypt-example/secure-with-decrypt, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.460 [INFO] [RouterExplorer] Mapped {/encrypt-example/test-request-body-encryption, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.461 [INFO] [RouterExplorer] Mapped {/encrypt-example/simple-partial/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.462 [INFO] [RoutesResolver] SecureExampleController {/api/secure-examples}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.463 [INFO] [RouterExplorer] Mapped {/api/secure-examples/standard, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.463 [INFO] [RouterExplorer] Mapped {/api/secure-examples/secure, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.467 [INFO] [RouterExplorer] Mapped {/api/secure-examples/secure-partial, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.468 [INFO] [RouterExplorer] Mapped {/api/secure-examples/sessions/stats, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.469 [INFO] [RoutesResolver] IpLocationController {/api/v1/ip-location}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.469 [INFO] [RouterExplorer] Mapped {/api/v1/ip-location/query, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.469 [INFO] [RouterExplorer] Mapped {/api/v1/ip-location/check-risk, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.470 [INFO] [RouterExplorer] Mapped {/api/v1/ip-location/user/:userId/stats, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.470 [INFO] [RouterExplorer] Mapped {/api/v1/ip-location/user/:userId/trust, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.470 [INFO] [RouterExplorer] Mapped {/api/v1/ip-location/current, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.470 [INFO] [RouterExplorer] Mapped {/api/v1/ip-location/health, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.471 [INFO] [RoutesResolver] TPSController {/tps}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.471 [INFO] [RouterExplorer] Mapped {/tps/create-test-students, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.471 [INFO] [RouterExplorer] Mapped {/tps/delete-by-prefix, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.471 [INFO] [RouterExplorer] Mapped {/tps/delete-complete-test-data, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.472 [INFO] [RouterExplorer] Mapped {/tps/create-complete-test-data, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.472 [INFO] [RouterExplorer] Mapped {/tps/create-test-teachers, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.472 [INFO] [RouterExplorer] Mapped {/tps/export-test-data-csv, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.473 [INFO] [RouterExplorer] Mapped {/tps/assign-special-package-to-all-students, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.473 [INFO] [RoutesResolver] AiVoiceprintRecognitionController {/api/ai-voiceprint-recognition}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.473 [INFO] [RouterExplorer] Mapped {/api/ai-voiceprint-recognition/create-group, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.473 [INFO] [RouterExplorer] Mapped {/api/ai-voiceprint-recognition/create-feature, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.473 [INFO] [RouterExplorer] Mapped {/api/ai-voiceprint-recognition/compare-feature, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.474 [INFO] [RouterExplorer] Mapped {/api/ai-voiceprint-recognition/search-feature, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.474 [INFO] [RouterExplorer] Mapped {/api/ai-voiceprint-recognition/query-feature-list, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.474 [INFO] [RouterExplorer] Mapped {/api/ai-voiceprint-recognition/voiceprint-libraries, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.474 [INFO] [RouterExplorer] Mapped {/api/ai-voiceprint-recognition/:jobId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.475 [INFO] [RouterExplorer] Mapped {/api/ai-voiceprint-recognition/update-feature, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.475 [INFO] [RouterExplorer] Mapped {/api/ai-voiceprint-recognition/delete-feature, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.475 [INFO] [RouterExplorer] Mapped {/api/ai-voiceprint-recognition/delete-group, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.475 [INFO] [RouterExplorer] Mapped {/api/ai-voiceprint-recognition/voiceprint-features/:groupId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.476 [INFO] [RoutesResolver] XunfeiVoiceprintRecognitionController {/xunfei-voiceprint-recognition}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.476 [INFO] [RouterExplorer] Mapped {/xunfei-voiceprint-recognition/create-group, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.476 [INFO] [RouterExplorer] Mapped {/xunfei-voiceprint-recognition/create-feature, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.476 [INFO] [RouterExplorer] Mapped {/xunfei-voiceprint-recognition/compare-feature, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.477 [INFO] [RouterExplorer] Mapped {/xunfei-voiceprint-recognition/search-feature, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.477 [INFO] [RouterExplorer] Mapped {/xunfei-voiceprint-recognition/query-feature-list, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.477 [INFO] [RouterExplorer] Mapped {/xunfei-voiceprint-recognition/update-feature, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.478 [INFO] [RouterExplorer] Mapped {/xunfei-voiceprint-recognition/delete-feature, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.478 [INFO] [RouterExplorer] Mapped {/xunfei-voiceprint-recognition/delete-group, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.478 [INFO] [RoutesResolver] VoiceprintGroupController {/voiceprint-group}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.478 [INFO] [RouterExplorer] Mapped {/voiceprint-group, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.479 [INFO] [RouterExplorer] Mapped {/voiceprint-group, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.479 [INFO] [RouterExplorer] Mapped {/voiceprint-group/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.479 [INFO] [RouterExplorer] Mapped {/voiceprint-group/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.479 [INFO] [RouterExplorer] Mapped {/voiceprint-group/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.480 [INFO] [RoutesResolver] VoiceprintFeatureController {/voiceprint-feature}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.480 [INFO] [RouterExplorer] Mapped {/voiceprint-feature, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.480 [INFO] [RouterExplorer] Mapped {/voiceprint-feature, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.480 [INFO] [RouterExplorer] Mapped {/voiceprint-feature/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.480 [INFO] [RouterExplorer] Mapped {/voiceprint-feature/group/:groupId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.481 [INFO] [RouterExplorer] Mapped {/voiceprint-feature/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.481 [INFO] [RouterExplorer] Mapped {/voiceprint-feature/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.481 [INFO] [RouterExplorer] Mapped {/voiceprint-feature/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.481 [INFO] [RoutesResolver] UserRoleController {/user-role}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.482 [INFO] [RouterExplorer] Mapped {/user-role, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.482 [INFO] [RouterExplorer] Mapped {/user-role, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.482 [INFO] [RouterExplorer] Mapped {/user-role/active, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.483 [INFO] [RouterExplorer] Mapped {/user-role/default, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.483 [INFO] [RouterExplorer] Mapped {/user-role/code/:code, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.483 [INFO] [RouterExplorer] Mapped {/user-role/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.483 [INFO] [RouterExplorer] Mapped {/user-role/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.484 [INFO] [RouterExplorer] Mapped {/user-role/:id/status/:status, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.484 [INFO] [RouterExplorer] Mapped {/user-role/:id/set-default, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.484 [INFO] [RouterExplorer] Mapped {/user-role/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.484 [INFO] [RouterExplorer] Mapped {/user-role/condition, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.485 [INFO] [PlatformCertificateService] 已加载微信支付平台证书: 604B78292D99D739AA8F4D437065F4D6A87F4057, 过期时间: 2030-06-30T10:22:39+08:00 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.485 [INFO] [PlatformCertificateService] 已加载 1 个微信支付平台证书 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.486 [INFO] [KeyManagementService] 密钥管理选项已加载: algorithm=aes-256-cbc, useRedis=true {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.486 [INFO] [KeyManagementService] 从Redis加载了密钥: b0381a63-e168-4575-97d7-a67b68462d98 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.486 [INFO] [KeyManagementService] 从Redis加载了密钥: 18a27d70-6f69-48f5-977d-a8333c052ab3 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.487 [INFO] [KeyManagementService] 从Redis加载了密钥: f2cd89fd-c713-4da3-81b2-63fbf1d264c0 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.487 [INFO] [KeyManagementService] 从Redis加载了密钥: 401b349c-c3f8-4fee-9ff3-3cfa252b56b7 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.488 [INFO] [KeyManagementService] 从Redis加载了4个密钥 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.488 [INFO] [KeyManagementService] 尝试从Redis加载RSA密钥对... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.488 [INFO] [KeyManagementService] RSA密钥对已保存到Redis，ID: key-80867834, 有效期30天，指纹：80867834 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.489 [INFO] [KeyManagementService] 将密钥 key-80867834 状态从 active 更改为 deprecated {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.489 [INFO] [KeyManagementService] 设置活跃密钥ID: key-4ba5b012 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.489 [INFO] [KeyManagementService] 从Redis加载了445个RSA密钥对 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.489 [INFO] [KeyManagementService] 已从Redis加载445个RSA密钥对 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.490 [INFO] [KeyManagementService] 活跃RSA密钥对验证通过 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.490 [INFO] [KeyManagementService] RSA密钥对已初始化，共445个版本，活跃版本: key-4ba5b012 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.490 [INFO] [KeyManagementService] 密钥管理服务初始化完成 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.490 [INFO] [UserRoleTemplateTaskService] 用户角色模板任务初始化开始... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.491 [INFO] [UserRoleTemplateTaskService] 执行用户角色模板启动任务... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.491 [INFO] [UserRoleTemplateTaskService] Template Repository available: true {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.491 [INFO] [UserRoleTemplateTaskService] Extension Permission Repository available: true {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.492 [INFO] [UserRoleTemplateTaskService] Block Permission Repository available: true {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.492 [INFO] [UserRoleTemplateTaskService] Block Repository available: true {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.492 [INFO] [UserRoleTemplateTaskService] Extension Repository available: true {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.492 [INFO] [UserRoleTemplateTaskService] 开始检查数据库表结构... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.493 [INFO] [UserRoleTemplateTaskService] 数据库表 role_permission_templates 存在 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.493 [INFO] [UserRoleTemplateTaskService] 数据库表 role_template_extension_permission 存在 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.494 [INFO] [UserRoleTemplateTaskService] 数据库表 role_template_block_permission 存在 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.494 [INFO] [UserRoleTemplateTaskService] 数据库表 blocks 存在 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.494 [INFO] [UserRoleTemplateTaskService] 数据库表 extensions 存在 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.494 [INFO] [UserRoleTemplateTaskService] RolePermissionTemplate 表中有 98 条记录 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.495 [INFO] [UserRoleTemplateTaskService] RoleTemplateExtensionPermission 表中有 2023 条记录 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.495 [INFO] [UserRoleTemplateTaskService] RoleTemplateBlockPermission 表中有 17659 条记录 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.495 [INFO] [UserRoleTemplateTaskService] Block 表中有 325 条记录 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.496 [INFO] [UserRoleTemplateTaskService] Extension 表中有 37 条记录 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.496 [INFO] [UserRoleTemplateTaskService] 检查特殊权限模板... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.496 [INFO] [UserRoleTemplateTaskService] 开始获取系统中所有可用的扩展和积木块... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.497 [INFO] [UserRoleTemplateTaskService] 发现系统中有 37 个扩展和 325 个积木块 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.497 [INFO] [UserRoleTemplateTaskService] 特殊权限模板更新完成，确保所有积木和扩展权限都已启用 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.497 [INFO] [UserRoleTemplateTaskService] 用户角色模板任务初始化完成 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.497 [INFO] [UserRoleTemplateTaskService] 用户角色模板任务初始化开始... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.498 [INFO] [UserRoleTemplateTaskService] 执行用户角色模板启动任务... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.498 [INFO] [UserRoleTemplateTaskService] Template Repository available: true {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.498 [INFO] [UserRoleTemplateTaskService] Extension Permission Repository available: true {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.498 [INFO] [UserRoleTemplateTaskService] Block Permission Repository available: true {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.499 [INFO] [UserRoleTemplateTaskService] Block Repository available: true {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.499 [INFO] [UserRoleTemplateTaskService] Extension Repository available: true {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.499 [INFO] [UserRoleTemplateTaskService] 开始检查数据库表结构... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.499 [INFO] [UserRoleTemplateTaskService] 数据库表 role_permission_templates 存在 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.500 [INFO] [UserRoleTemplateTaskService] 数据库表 role_template_extension_permission 存在 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.500 [INFO] [UserRoleTemplateTaskService] 数据库表 role_template_block_permission 存在 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.500 [INFO] [UserRoleTemplateTaskService] 数据库表 blocks 存在 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.500 [INFO] [UserRoleTemplateTaskService] 数据库表 extensions 存在 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.500 [INFO] [UserRoleTemplateTaskService] RolePermissionTemplate 表中有 98 条记录 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.501 [INFO] [UserRoleTemplateTaskService] RoleTemplateExtensionPermission 表中有 2023 条记录 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.501 [INFO] [UserRoleTemplateTaskService] RoleTemplateBlockPermission 表中有 17659 条记录 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.501 [INFO] [UserRoleTemplateTaskService] Block 表中有 325 条记录 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.501 [INFO] [UserRoleTemplateTaskService] Extension 表中有 37 条记录 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.502 [INFO] [UserRoleTemplateTaskService] 检查特殊权限模板... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.502 [INFO] [UserRoleTemplateTaskService] 开始获取系统中所有可用的扩展和积木块... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.502 [INFO] [UserRoleTemplateTaskService] 发现系统中有 37 个扩展和 325 个积木块 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.504 [INFO] [UserRoleTemplateTaskService] 特殊权限模板更新完成，确保所有积木和扩展权限都已启用 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.505 [INFO] [UserRoleTemplateTaskService] 用户角色模板任务初始化完成 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.505 [INFO] [NestApplication] Nest application successfully started {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 11:47:09.512 [INFO] [Startup] Application started successfully {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","url":"http://[::1]:8003","port":8003}
