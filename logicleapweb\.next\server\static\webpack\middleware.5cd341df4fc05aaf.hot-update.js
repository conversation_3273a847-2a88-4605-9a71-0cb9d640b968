"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./middleware.ts":
/*!***********************!*\
  !*** ./middleware.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n\nfunction middleware(request) {\n    // 只处理 API 请求\n    if (request.nextUrl.pathname.startsWith(\"/api/\")) {\n        // 获取客户端真实IP地址\n        const forwarded = request.headers.get(\"x-forwarded-for\");\n        const realIp = request.headers.get(\"x-real-ip\");\n        const cfConnectingIp = request.headers.get(\"cf-connecting-ip\");\n        const remoteAddr = request.headers.get(\"remote-addr\");\n        // 尝试多种方式获取IP\n        let clientIp = request.ip;\n        if (!clientIp && forwarded) {\n            clientIp = forwarded.split(\",\")[0].trim();\n        }\n        if (!clientIp && realIp) {\n            clientIp = realIp;\n        }\n        if (!clientIp && cfConnectingIp) {\n            clientIp = cfConnectingIp;\n        }\n        if (!clientIp && remoteAddr) {\n            clientIp = remoteAddr;\n        }\n        // 如果还是没有获取到，尝试从其他来源\n        if (!clientIp) {\n            // 在开发环境下，我们可以使用一个模拟的公网IP进行测试\n            if (true) {\n                // 使用一个真实的公网IP进行测试（这里使用北京的一个IP）\n                clientIp = \"**************\"; // 模拟的公网IP（百度的IP）\n                console.log(\"\\uD83C\\uDFAD [Middleware] 开发环境使用模拟公网IP:\", clientIp);\n            } else {}\n        }\n        // 添加详细的IP获取日志\n        console.log(\"\\uD83C\\uDF10 [Middleware] IP地址获取详情:\", {\n            url: request.nextUrl.pathname,\n            method: request.method,\n            原始IP来源: {\n                \"request.ip\": request.ip,\n                \"x-forwarded-for\": forwarded,\n                \"x-real-ip\": realIp,\n                \"cf-connecting-ip\": cfConnectingIp,\n                \"remote-addr\": remoteAddr\n            },\n            最终确定IP: clientIp,\n            环境: \"development\",\n            时间戳: new Date().toISOString()\n        });\n        // 创建新的请求头\n        const requestHeaders = new Headers(request.headers);\n        // 设置真实IP相关的头部\n        requestHeaders.set(\"x-forwarded-for\", clientIp);\n        requestHeaders.set(\"x-real-ip\", clientIp);\n        requestHeaders.set(\"x-client-ip\", clientIp);\n        // 创建新的响应，将修改后的头部传递给后端\n        const response = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.rewrite(request.nextUrl, {\n            request: {\n                headers: requestHeaders\n            }\n        });\n        return response;\n    }\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n}\nconst config = {\n    matcher: [\n        /*\r\n     * 匹配所有 API 路由\r\n     */ \"/api/:path*\"\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./middleware.ts\n");

/***/ })

});