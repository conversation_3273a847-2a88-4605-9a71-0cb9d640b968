"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IpLocationFacadeService = void 0;
const common_1 = require("@nestjs/common");
const logger_service_1 = require("../../../../common/logger/logger.service");
const ip_location_application_service_1 = require("./ip-location-application.service");
const ip_location_domain_service_1 = require("../../domain/services/ip-location-domain.service");
let IpLocationFacadeService = class IpLocationFacadeService {
    applicationService;
    domainService;
    logger;
    constructor(applicationService, domainService, logger) {
        this.applicationService = applicationService;
        this.domainService = domainService;
        this.logger = logger;
    }
    async getLocationByIP(ip, includeRisk = false) {
        const startTime = Date.now();
        try {
            const request = { ip, includeRisk };
            const result = await this.applicationService.queryIpLocation(request);
            return this.createSuccessResponse(result, '查询成功', Date.now() - startTime);
        }
        catch (error) {
            this.logger.error(`IP位置查询失败: ${ip}`, error, 'IpLocationFacadeService');
            return this.createErrorResponse(error.message || '查询失败', Date.now() - startTime);
        }
    }
    async assessLoginRisk(userId, ip, userAgent, sessionId) {
        const startTime = Date.now();
        try {
            const request = {
                userId,
                ipAddress: ip,
                userAgent,
                sessionId
            };
            const result = await this.applicationService.checkLoginRisk(request);
            return this.createSuccessResponse(result, '风险评估完成', Date.now() - startTime);
        }
        catch (error) {
            this.logger.error(`登录风险评估失败: ${userId}-${ip}`, error, 'IpLocationFacadeService');
            return this.createErrorResponse(error.message || '风险评估失败', Date.now() - startTime);
        }
    }
    async getUserLocationStats(userId, days = 30) {
        const startTime = Date.now();
        try {
            const result = await this.applicationService.getUserLocationStatistics(userId, days);
            return this.createSuccessResponse(result, '统计查询成功', Date.now() - startTime);
        }
        catch (error) {
            this.logger.error(`用户位置统计失败: ${userId}`, error, 'IpLocationFacadeService');
            return this.createErrorResponse(error.message || '统计查询失败', Date.now() - startTime);
        }
    }
    async setTrustedLocation(userId, province, city, reason = '用户主动设置') {
        const startTime = Date.now();
        try {
            const request = {
                province,
                city,
                reason
            };
            await this.applicationService.setTrustedLocation(userId, request);
            return this.createSuccessResponse({ userId, province, city }, '可信位置设置成功', Date.now() - startTime);
        }
        catch (error) {
            this.logger.error(`设置可信位置失败: ${userId}-${province}-${city}`, error, 'IpLocationFacadeService');
            return this.createErrorResponse(error.message || '设置失败', Date.now() - startTime);
        }
    }
    async updateUserCommonLocation(userId, ip) {
        const startTime = Date.now();
        try {
            const request = { ip, includeRisk: false };
            const locationInfo = await this.applicationService.queryIpLocation(request);
            return this.createSuccessResponse({ userId, ip, location: locationInfo }, '常用位置更新成功', Date.now() - startTime);
        }
        catch (error) {
            this.logger.error(`更新常用位置失败: ${userId}-${ip}`, error, 'IpLocationFacadeService');
            return this.createErrorResponse(error.message || '更新失败', Date.now() - startTime);
        }
    }
    async testIpResolution(testIp = '*******') {
        const startTime = Date.now();
        try {
            const result = await this.domainService.testResolution(testIp);
            return this.createSuccessResponse(result, result.success ? '测试成功' : '测试失败', Date.now() - startTime);
        }
        catch (error) {
            this.logger.error(`IP解析测试失败: ${testIp}`, error, 'IpLocationFacadeService');
            return this.createErrorResponse(error.message || '测试失败', Date.now() - startTime);
        }
    }
    createSuccessResponse(data, message = '操作成功', executionTime, fromCache) {
        return {
            success: true,
            data,
            message,
            timestamp: new Date().toISOString(),
            executionTime: executionTime ? `${executionTime}ms` : undefined,
            fromCache
        };
    }
    createErrorResponse(error, executionTime) {
        return {
            success: false,
            error,
            data: null,
            timestamp: new Date().toISOString(),
            executionTime: executionTime ? `${executionTime}ms` : undefined
        };
    }
};
exports.IpLocationFacadeService = IpLocationFacadeService;
exports.IpLocationFacadeService = IpLocationFacadeService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [ip_location_application_service_1.IpLocationApplicationService,
        ip_location_domain_service_1.IpLocationDomainService,
        logger_service_1.LoggerService])
], IpLocationFacadeService);
//# sourceMappingURL=ip-location-facade.service.js.map