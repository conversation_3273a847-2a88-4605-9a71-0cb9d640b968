import { SetMetadata, createParamDecorator, ExecutionContext } from '@nestjs/common';
import { LoginLoggerUtil } from './login-logger.util';
import { LoginType } from '../../util/database/mysql/user_login_log/entities/user_login_log.entity';

// 登录日志元数据键
export const LOGIN_LOG_KEY = 'login_log';

// 使用数据库实体中的登录类型枚举
export { LoginType as LoginLogType };

// 登录日志配置接口
export interface LoginLogConfig {
  loginType: LoginType;
  successMessage?: string;
  failureMessage?: string;
  extractUserId?: (result: any, body: any) => number;
  extractSessionId?: (result: any, body: any) => string;
  extractFailReason?: (error: any) => string;
}

/**
 * 登录日志装饰器
 * 自动记录登录成功和失败的日志
 */
export const LoginLog = (config: LoginLogConfig) => SetMetadata(LOGIN_LOG_KEY, config);

/**
 * 获取客户端信息的参数装饰器
 */
export const ClientInfo = createParamDecorator(
  (data: unknown, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();
    
    // 获取客户端真实IP地址
    const getClientIp = (req: any): string => {
      const xForwardedFor = req.headers['x-forwarded-for'];
      const xRealIp = req.headers['x-real-ip'];
      const xClientIp = req.headers['x-client-ip'];
      const connectionIp = req.connection?.remoteAddress;
      const socketIp = req.socket?.remoteAddress;
      const connectionSocketIp = req.connection?.socket?.remoteAddress;

      // 添加详细的IP获取日志
      console.log('🔐 [LoginLog] IP地址获取详情:', {
        请求路径: req.url,
        请求方法: req.method,
        请求头IP信息: {
          'x-forwarded-for': xForwardedFor,
          'x-real-ip': xRealIp,
          'x-client-ip': xClientIp,
        },
        连接IP信息: {
          'connection.remoteAddress': connectionIp,
          'socket.remoteAddress': socketIp,
          'connection.socket.remoteAddress': connectionSocketIp,
        },
        时间戳: new Date().toISOString()
      });

      const headerIp = xForwardedFor || xRealIp || xClientIp || connectionIp || socketIp || connectionSocketIp;

      if (headerIp && headerIp.includes(',')) {
        const finalIp = headerIp.split(',')[0].trim();
        console.log('✅ [LoginLog] IP地址提取成功:', {
          原始IP: headerIp,
          最终IP: finalIp,
          来源: xForwardedFor ? 'x-forwarded-for' : xRealIp ? 'x-real-ip' : xClientIp ? 'x-client-ip' : '连接IP',
          时间戳: new Date().toISOString()
        });
        return finalIp;
      }

      const finalIp = headerIp || '127.0.0.1';
      console.log('✅ [LoginLog] IP地址确定:', {
        最终IP: finalIp,
        是否为默认: finalIp === '127.0.0.1',
        时间戳: new Date().toISOString()
      });
      return finalIp;
    };

    const clientIp = getClientIp(request);
    const userAgent = request.headers['user-agent'] || '未知设备';
    
    return {
      clientIp,
      userAgent,
      deviceInfo: LoginLoggerUtil.getDeviceInfo(userAgent),
      request
    };
  },
);

/**
 * 登录日志拦截器
 * 自动处理登录日志记录的逻辑
 */
import { Injectable, NestInterceptor, CallHandler } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Observable, throwError } from 'rxjs';
import { tap, catchError } from 'rxjs/operators';

@Injectable()
export class LoginLogInterceptor implements NestInterceptor {
  constructor(private reflector: Reflector) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const loginLogConfig = this.reflector.get<LoginLogConfig>(
      LOGIN_LOG_KEY,
      context.getHandler(),
    );

    if (!loginLogConfig) {
      return next.handle();
    }

    const request = context.switchToHttp().getRequest();
    const clientIp = this.getClientIp(request);
    const userAgent = request.headers['user-agent'] || '未知设备';
    const deviceInfo = LoginLoggerUtil.getDeviceInfo(userAgent);

    return next.handle().pipe(
      tap(async (result) => {
        // 登录成功，记录成功日志
        try {
          const userId = loginLogConfig.extractUserId 
            ? loginLogConfig.extractUserId(result, request.body)
            : result?.data?.userInfo?.id || result?.userInfo?.id;

          const sessionId = loginLogConfig.extractSessionId
            ? loginLogConfig.extractSessionId(result, request.body)
            : `${loginLogConfig.loginType}-${userId}-${Date.now()}`;

          if (userId) {
            await LoginLoggerUtil.logSuccessLogin({
              userId,
              loginType: loginLogConfig.loginType,
              clientIp,
              userAgent,
              deviceInfo,
              sessionId
            });
          }
        } catch (error) {
          console.error('记录登录成功日志失败:', error);
        }
      }),
      catchError(async (error) => {
        // 登录失败，记录失败日志
        try {
          const failReason = loginLogConfig.extractFailReason
            ? loginLogConfig.extractFailReason(error)
            : error.message || '登录失败';

          const userId = loginLogConfig.extractUserId
            ? loginLogConfig.extractUserId(null, request.body)
            : undefined;

          await LoginLoggerUtil.logFailedLogin({
            userId,
            loginType: loginLogConfig.loginType,
            clientIp,
            userAgent,
            failReason
          });
        } catch (logError) {
          console.error('记录登录失败日志失败:', logError);
        }

        return throwError(error);
      }),
    );
  }

  private getClientIp(request: any): string {
    const headerIp =
      request.headers['x-forwarded-for'] ||
      request.headers['x-real-ip'] ||
      request.headers['x-client-ip'] ||
      request.connection?.remoteAddress ||
      request.socket?.remoteAddress ||
      request.connection?.socket?.remoteAddress;

    if (headerIp && headerIp.includes(',')) {
      return headerIp.split(',')[0].trim();
    }

    return headerIp || '127.0.0.1';
  }
}

/**
 * 简化的登录日志记录函数
 * 用于手动记录登录日志
 */
export class LoginLogHelper {
  /**
   * 记录登录成功
   */
  static async logSuccess(
    userId: number,
    loginType: LoginType,
    clientInfo: any,
    sessionId?: string
  ) {
    await LoginLoggerUtil.logSuccessLogin({
      userId,
      loginType,
      clientIp: clientInfo.clientIp,
      userAgent: clientInfo.userAgent,
      deviceInfo: clientInfo.deviceInfo,
      sessionId: sessionId || `${loginType}-${userId}-${Date.now()}`
    });
  }

  /**
   * 记录登录失败
   */
  static async logFailure(
    loginType: LoginType,
    clientInfo: any,
    failReason: string,
    userId?: number
  ) {
    await LoginLoggerUtil.logFailedLogin({
      userId,
      loginType,
      clientIp: clientInfo.clientIp,
      userAgent: clientInfo.userAgent,
      failReason
    });
  }

  /**
   * 记录角色切换（已废弃，请使用 logLogout + logSuccess 组合）
   * @deprecated 使用 logLogout 和 logSuccess 的组合来记录角色切换
   */
  static async logRoleSwitch(
    userId: number,
    clientInfo: any,
    fromRole: string,
    toRole: string
  ) {
    console.warn('⚠️ logRoleSwitch 方法已废弃，请使用 logLogout + logSuccess 组合');
    console.log('🔄 角色切换日志（废弃方法）:', {
      userId,
      fromRole,
      toRole,
      clientIp: clientInfo.clientIp,
      userAgent: clientInfo.userAgent?.substring(0, 50) + '...'
    });

    // 使用新的方法组合来实现角色切换日志
    try {
      await LoginLogHelper.logSuccess(
        userId,
        LoginType.REFRESH, // 角色切换视为refresh类型
        clientInfo,
        `role-switch-${userId}-${Date.now()}`
      );
      console.log('✅ 角色切换日志记录成功（废弃方法）');
    } catch (error) {
      console.error('❌ 角色切换日志记录失败:', error);
      console.error('❌ 错误详情:', error.stack);
    }
  }

  /**
   * 记录登出
   */
  static async logLogout(
    userId: number,
    clientInfo: any,
    sessionId?: string,
    reason?: string,
    logoutType?: 'active' | 'passive' | 'switch'
  ) {
    await LoginLoggerUtil.logLogout({
      userId,
      clientIp: clientInfo.clientIp,
      sessionId: sessionId, // 不自动生成，保持原值（可能是undefined）
      reason: reason || '用户主动登出',
      logoutType: logoutType || 'active'
    });
  }
}
