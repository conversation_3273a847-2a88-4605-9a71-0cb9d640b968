{"version": 3, "file": "unified-exception.filter.js", "sourceRoot": "", "sources": ["../../../src/common/response/unified-exception.filter.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAMwB;AAGxB,+BAAoC;AAO7B,IAAM,sBAAsB,GAA5B,MAAM,sBAAsB;IACJ;IAA7B,YAA6B,eAAuC;QAAvC,oBAAe,GAAf,eAAe,CAAwB;IAAG,CAAC;IAExE,KAAK,CAAC,SAAkB,EAAE,IAAmB;QAC3C,MAAM,GAAG,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QAChC,MAAM,QAAQ,GAAG,GAAG,CAAC,WAAW,EAAY,CAAC;QAC7C,MAAM,OAAO,GAAG,GAAG,CAAC,UAAU,EAAW,CAAC;QAG1C,MAAM,SAAS,GAAG,OAAO,CAAC,OAAO,CAAC,cAAc,CAAW,IAAI,IAAA,SAAM,GAAE,CAAC;QAGxE,MAAM,SAAS,GAAG,OAAO,CAAC,WAAW,CAAW,CAAC;QACjD,MAAM,aAAa,GAAG,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC;QAErE,IAAI,UAAU,GAAG,mBAAU,CAAC,qBAAqB,CAAC;QAClD,IAAI,YAAY,GAAG,cAAc,CAAC,cAAc,CAAC;QACjD,IAAI,OAAO,GAAG,SAAS,CAAC;QACxB,IAAI,SAAS,GAAQ,IAAI,CAAC;QAG1B,IAAI,SAAS,YAAY,sBAAa,EAAE,CAAC;YACvC,UAAU,GAAG,SAAS,CAAC,SAAS,EAAE,CAAC;YACnC,MAAM,iBAAiB,GAAG,SAAS,CAAC,WAAW,EAAE,CAAC;YAGlD,IAAI,OAAO,iBAAiB,KAAK,QAAQ,EAAE,CAAC;gBAC1C,OAAO,GAAG,iBAAiB,CAAC;YAC9B,CAAC;iBAAM,IAAI,OAAO,iBAAiB,KAAK,QAAQ,IAAI,iBAAiB,KAAK,IAAI,EAAE,CAAC;gBAC/E,MAAM,YAAY,GAAG,iBAAwB,CAAC;gBAG9C,IAAI,YAAY,CAAC,GAAG,EAAE,CAAC;oBACrB,OAAO,GAAG,YAAY,CAAC,GAAG,CAAC;gBAC7B,CAAC;qBAAM,IAAI,YAAY,CAAC,OAAO,EAAE,CAAC;oBAChC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,OAAO,CAAC;wBAC3C,CAAC,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;wBACjC,CAAC,CAAC,YAAY,CAAC,OAAO,CAAC;gBAC3B,CAAC;gBAGD,IAAI,YAAY,CAAC,IAAI,EAAE,CAAC;oBACtB,YAAY,GAAG,YAAY,CAAC,IAAI,CAAC;gBACnC,CAAC;gBAGD,IAAI,YAAY,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;oBACpC,SAAS,GAAG,YAAY,CAAC,IAAI,CAAC;gBAChC,CAAC;YACH,CAAC;YAGD,IAAI,YAAY,KAAK,cAAc,CAAC,cAAc,EAAE,CAAC;gBACnD,YAAY,GAAG,IAAI,CAAC,2BAA2B,CAAC,UAAU,CAAC,CAAC;YAC9D,CAAC;YAGD,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;gBAC1B,OAAO,GAAG,IAAI,CAAC,6BAA6B,CAAC,UAAU,CAAC,CAAC;YAC3D,CAAC;QACH,CAAC;aAAM,IAAI,SAAS,YAAY,KAAK,EAAE,CAAC;YAEtC,OAAO,GAAG,SAAS,CAAC,OAAO,IAAI,MAAM,CAAC;YACtC,SAAS,GAAG;gBACV,IAAI,EAAE,SAAS,CAAC,IAAI;gBAEpB,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,IAAI,EAAE,KAAK,EAAE,SAAS,CAAC,KAAK,EAAE,CAAC;aACzE,CAAC;QACJ,CAAC;QAGD,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAC7C,OAAO,EACP,SAAS,EACT,YAAY,EACZ;YACE,IAAI,EAAE,OAAO,CAAC,GAAG;YACjB,aAAa;YACb,SAAS;YACT,UAAU,EAAE,wBAAwB;YACpC,SAAS,EAAE,SAAS,YAAY,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAChF,UAAU;SACX,CACF,CAAC;QAGF,QAAQ,CAAC,SAAS,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;QAG9C,QAAQ,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IACjD,CAAC;IAMO,2BAA2B,CAAC,UAAkB;QACpD,QAAQ,UAAU,EAAE,CAAC;YACnB,KAAK,mBAAU,CAAC,WAAW;gBACzB,OAAO,cAAc,CAAC,WAAW,CAAC;YACpC,KAAK,mBAAU,CAAC,YAAY;gBAC1B,OAAO,cAAc,CAAC,YAAY,CAAC;YACrC,KAAK,mBAAU,CAAC,SAAS;gBACvB,OAAO,cAAc,CAAC,SAAS,CAAC;YAClC,KAAK,mBAAU,CAAC,SAAS;gBACvB,OAAO,cAAc,CAAC,SAAS,CAAC;YAClC,KAAK,mBAAU,CAAC,qBAAqB;gBACnC,OAAO,cAAc,CAAC,cAAc,CAAC;YACvC;gBACE,OAAO,cAAc,CAAC,cAAc,CAAC;QACzC,CAAC;IACH,CAAC;IAMO,6BAA6B,CAAC,UAAkB;QACtD,QAAQ,UAAU,EAAE,CAAC;YACnB,KAAK,mBAAU,CAAC,WAAW;gBACzB,OAAO,QAAQ,CAAC;YAClB,KAAK,mBAAU,CAAC,YAAY;gBAC1B,OAAO,UAAU,CAAC;YACpB,KAAK,mBAAU,CAAC,SAAS;gBACvB,OAAO,YAAY,CAAC;YACtB,KAAK,mBAAU,CAAC,SAAS;gBACvB,OAAO,UAAU,CAAC;YACpB,KAAK,mBAAU,CAAC,kBAAkB;gBAChC,OAAO,UAAU,CAAC;YACpB,KAAK,mBAAU,CAAC,QAAQ;gBACtB,OAAO,MAAM,CAAC;YAChB,KAAK,mBAAU,CAAC,oBAAoB;gBAClC,OAAO,UAAU,CAAC;YACpB,KAAK,mBAAU,CAAC,iBAAiB;gBAC/B,OAAO,cAAc,CAAC;YACxB,KAAK,mBAAU,CAAC,qBAAqB;gBACnC,OAAO,SAAS,CAAC;YACnB,KAAK,mBAAU,CAAC,WAAW;gBACzB,OAAO,MAAM,CAAC;YAChB,KAAK,mBAAU,CAAC,mBAAmB;gBACjC,OAAO,SAAS,CAAC;YACnB,KAAK,mBAAU,CAAC,eAAe;gBAC7B,OAAO,MAAM,CAAC;YAChB;gBACE,OAAO,SAAS,UAAU,GAAG,CAAC;QAClC,CAAC;IACH,CAAC;CACF,CAAA;AAnJY,wDAAsB;iCAAtB,sBAAsB;IADlC,IAAA,cAAK,GAAE;yDAEwC,sBAAsB,oBAAtB,sBAAsB;GADzD,sBAAsB,CAmJlC;AAMD,MAAa,iBAAkB,SAAQ,sBAAa;IAClD,YACE,OAAe,EACf,eAAuB,cAAc,CAAC,cAAc,EACpD,aAAyB,mBAAU,CAAC,WAAW,EAC/C,IAAU;QAEV,KAAK,CACH;YACE,GAAG,EAAE,OAAO;YACZ,IAAI,EAAE,YAAY;YAClB,IAAI;SACL,EACD,UAAU,CACX,CAAC;IACJ,CAAC;CACF;AAhBD,8CAgBC;AAKD,MAAa,mBAAoB,SAAQ,iBAAiB;IACxD,YAAY,OAAe,EAAE,IAAU;QACrC,KAAK,CAAC,OAAO,EAAE,cAAc,CAAC,WAAW,EAAE,mBAAU,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;IAC3E,CAAC;CACF;AAJD,kDAIC;AAKD,MAAa,qBAAsB,SAAQ,iBAAiB;IAC1D,YAAY,UAAkB,UAAU,EAAE,IAAU;QAClD,KAAK,CAAC,OAAO,EAAE,cAAc,CAAC,YAAY,EAAE,mBAAU,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;IAC7E,CAAC;CACF;AAJD,sDAIC;AAKD,MAAa,kBAAmB,SAAQ,iBAAiB;IACvD,YAAY,UAAkB,YAAY,EAAE,IAAU;QACpD,KAAK,CAAC,OAAO,EAAE,cAAc,CAAC,SAAS,EAAE,mBAAU,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;IACvE,CAAC;CACF;AAJD,gDAIC;AAKD,MAAa,iBAAkB,SAAQ,iBAAiB;IACtD,YAAY,UAAkB,UAAU,EAAE,IAAU;QAClD,KAAK,CAAC,OAAO,EAAE,cAAc,CAAC,SAAS,EAAE,mBAAU,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;IACvE,CAAC;CACF;AAJD,8CAIC"}