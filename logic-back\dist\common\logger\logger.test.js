"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LoggerTest = void 0;
class LoggerTest {
    loggerService;
    constructor(loggerService) {
        this.loggerService = loggerService;
    }
    testAllLogTypes() {
        console.log('=== 开始测试日志系统 ===');
        console.log('这是一个console.log测试');
        console.log('测试对象输出:', { name: '张三', age: 25, hobbies: ['读书', '游戏'] });
        console.log('测试数组输出:', [1, 2, 3, 'test', { key: 'value' }]);
        console.error('这是一个console.error测试');
        console.error('测试错误对象:', new Error('测试错误'));
        console.warn('这是一个console.warn测试');
        console.info('这是一个console.info测试');
        console.debug('这是一个console.debug测试');
        console.trace('这是一个console.trace测试');
        console.assert(true, '这个断言应该不会输出');
        console.assert(false, '这个断言应该会输出');
        console.dir({
            user: {
                name: '李四',
                profile: {
                    age: 30,
                    city: '北京'
                }
            }
        });
        console.table([
            { name: '张三', age: 25, city: '北京' },
            { name: '李四', age: 30, city: '上海' },
            { name: '王五', age: 28, city: '广州' }
        ]);
        console.group('用户信息组');
        console.log('用户名: 张三');
        console.log('年龄: 25');
        console.group('地址信息');
        console.log('城市: 北京');
        console.log('区域: 朝阳区');
        console.groupEnd();
        console.groupEnd();
        console.time('性能测试');
        for (let i = 0; i < 1000000; i++) {
        }
        console.timeEnd('性能测试');
        for (let i = 0; i < 5; i++) {
            console.count('循环计数');
        }
        this.loggerService.log('使用LoggerService的log方法', 'LoggerTest');
        this.loggerService.error('使用LoggerService的error方法', 'Error stack trace', 'LoggerTest');
        this.loggerService.warn('使用LoggerService的warn方法', 'LoggerTest');
        this.loggerService.debug('使用LoggerService的debug方法', 'LoggerTest');
        const mockReq = {
            method: 'GET',
            url: '/api/test',
            get: (header) => header === 'User-Agent' ? 'Test-Agent' : undefined,
            ip: '127.0.0.1',
            connection: { remoteAddress: '127.0.0.1' }
        };
        const mockRes = {
            statusCode: 200
        };
        this.loggerService.logHttpRequest(mockReq, mockRes, 150);
        this.loggerService.logDatabase('SELECT', 'users', { id: 1 });
        this.loggerService.logDatabase('INSERT', 'users', { name: '新用户', email: '<EMAIL>' });
        this.loggerService.logDatabase('UPDATE', 'users', { id: 1, name: '更新用户' }, new Error('更新失败'));
        this.loggerService.logPayment('create_order', { orderId: 'P123456', amount: 100 });
        this.loggerService.logPayment('payment_failed', { orderId: 'P123456' }, new Error('支付失败'));
        this.loggerService.logAuth('login', 'user123', { method: 'password' });
        this.loggerService.logAuth('login_failed', 'user123', { method: 'password' }, new Error('密码错误'));
        this.loggerService.logBusiness('user', 'register', { username: 'newuser', email: '<EMAIL>' });
        this.loggerService.logBusiness('order', 'create', { orderId: 'O123456' }, new Error('创建订单失败'));
        this.loggerService.logPerformance('database_query', 250, { query: 'SELECT * FROM users' });
        this.loggerService.logSecurity('suspicious_login', { ip: '*************', attempts: 5 }, 'high');
        this.loggerService.logSecurity('password_change', { userId: 'user123' }, 'low');
        console.log('=== 日志系统测试完成 ===');
        console.log('请检查以下日志文件:');
        console.log('- logs/application-YYYY-MM-DD.log (所有日志)');
        console.log('- logs/error-YYYY-MM-DD.log (错误日志)');
        console.log('- logs/http-YYYY-MM-DD.log (HTTP请求日志)');
        console.log('- logs/database-YYYY-MM-DD.log (数据库日志)');
        console.log('- logs/console-YYYY-MM-DD.log (Console输出日志)');
    }
    async testAsyncLogs() {
        console.log('=== 开始测试异步日志 ===');
        try {
            await new Promise(resolve => setTimeout(resolve, 100));
            console.log('异步操作完成');
            await new Promise((resolve, reject) => {
                setTimeout(() => reject(new Error('异步操作失败')), 50);
            });
        }
        catch (error) {
            console.error('捕获到异步错误:', error);
            this.loggerService.error('异步操作失败', error.stack, 'AsyncTest');
        }
        console.log('=== 异步日志测试完成 ===');
    }
    testLogPerformance() {
        console.log('=== 开始测试日志性能 ===');
        const startTime = Date.now();
        const logCount = 1000;
        for (let i = 0; i < logCount; i++) {
            console.log(`性能测试日志 ${i + 1}/${logCount}`, {
                index: i,
                timestamp: new Date().toISOString(),
                data: { test: 'performance', value: Math.random() }
            });
        }
        const endTime = Date.now();
        const duration = endTime - startTime;
        console.log(`=== 日志性能测试完成 ===`);
        console.log(`输出 ${logCount} 条日志耗时: ${duration}ms`);
        console.log(`平均每条日志耗时: ${(duration / logCount).toFixed(2)}ms`);
        this.loggerService.logPerformance('log_output_test', duration, {
            logCount,
            averageTime: duration / logCount
        });
    }
}
exports.LoggerTest = LoggerTest;
//# sourceMappingURL=logger.test.js.map