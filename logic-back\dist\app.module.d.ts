import { OnModuleInit, MiddlewareConsumer, NestModule } from '@nestjs/common';
import { UserRoleTemplateTaskService } from './web/user_role/user_role_template_task.service';
import { LoggerService } from './common/logger/logger.service';
export declare class AppModule implements OnModuleInit, NestModule {
    private readonly userRoleTemplateTaskService?;
    private readonly loggerService?;
    constructor(userRoleTemplateTaskService?: UserRoleTemplateTaskService | undefined, loggerService?: LoggerService | undefined);
    configure(consumer: MiddlewareConsumer): void;
    onModuleInit(): Promise<void>;
}
