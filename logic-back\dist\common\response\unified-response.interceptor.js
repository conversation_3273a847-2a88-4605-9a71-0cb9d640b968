"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RequestTimeMiddleware = exports.UnifiedResponseInterceptor = void 0;
exports.ApiResponse = ApiResponse;
exports.ApiErrorResponse = ApiErrorResponse;
const common_1 = require("@nestjs/common");
const operators_1 = require("rxjs/operators");
const unified_response_service_1 = require("./unified-response.service");
const uuid_1 = require("uuid");
let UnifiedResponseInterceptor = class UnifiedResponseInterceptor {
    responseService;
    constructor(responseService) {
        this.responseService = responseService;
    }
    intercept(context, next) {
        const request = context.switchToHttp().getRequest();
        const path = request.path || request.url || '';
        const requestId = request.headers['x-request-id'] || (0, uuid_1.v4)();
        const startTime = request['startTime'];
        const executionTime = startTime ? Date.now() - startTime : undefined;
        const excludePaths = [
            '/weixin/message',
            '/health',
            '/metrics',
            '/swagger',
            '/api-docs'
        ];
        if (excludePaths.some(excludePath => path.includes(excludePath))) {
            return next.handle();
        }
        return next.handle().pipe((0, operators_1.map)(data => {
            if (this.isUnifiedResponse(data)) {
                return data;
            }
            if (this.isLegacyResponse(data)) {
                return this.responseService.custom(data.code, data.msg, data.data, {
                    path,
                    executionTime,
                    requestId,
                    logContext: 'UnifiedResponseInterceptor'
                });
            }
            return this.responseService.success(data, '操作成功', {
                path,
                executionTime,
                requestId,
                logContext: 'UnifiedResponseInterceptor'
            });
        }));
    }
    isUnifiedResponse(data) {
        return (data &&
            typeof data === 'object' &&
            'code' in data &&
            'msg' in data &&
            'data' in data &&
            'timestamp' in data);
    }
    isLegacyResponse(data) {
        return (data &&
            typeof data === 'object' &&
            'code' in data &&
            'msg' in data &&
            'data' in data &&
            !('timestamp' in data));
    }
};
exports.UnifiedResponseInterceptor = UnifiedResponseInterceptor;
exports.UnifiedResponseInterceptor = UnifiedResponseInterceptor = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [unified_response_service_1.UnifiedResponseService])
], UnifiedResponseInterceptor);
let RequestTimeMiddleware = class RequestTimeMiddleware {
    use(req, res, next) {
        req['startTime'] = Date.now();
        if (!req.headers['x-request-id']) {
            req.headers['x-request-id'] = (0, uuid_1.v4)();
        }
        next();
    }
};
exports.RequestTimeMiddleware = RequestTimeMiddleware;
exports.RequestTimeMiddleware = RequestTimeMiddleware = __decorate([
    (0, common_1.Injectable)()
], RequestTimeMiddleware);
function ApiResponse(message) {
    return function (target, propertyKey, descriptor) {
        const originalMethod = descriptor.value;
        descriptor.value = async function (...args) {
            const result = await originalMethod.apply(this, args);
            if (result && typeof result === 'object' && 'code' in result) {
                return result;
            }
            if (this.responseService && typeof this.responseService.success === 'function') {
                return this.responseService.success(result, message);
            }
            return result;
        };
        return descriptor;
    };
}
function ApiErrorResponse(message, code) {
    return function (target, propertyKey, descriptor) {
        const originalMethod = descriptor.value;
        descriptor.value = async function (...args) {
            try {
                return await originalMethod.apply(this, args);
            }
            catch (error) {
                if (this.responseService && typeof this.responseService.error === 'function') {
                    return this.responseService.error(message || error.message, null, code, {
                        logContext: target.constructor.name,
                        exception: error instanceof Error ? error : new Error(String(error))
                    });
                }
                throw error;
            }
        };
        return descriptor;
    };
}
//# sourceMappingURL=unified-response.interceptor.js.map