import { LoggerService } from '../logger/logger.service';
export interface UnifiedResponse<T = any> {
    code: number;
    msg: string;
    data: T;
    timestamp: string;
    path?: string;
    executionTime?: string;
    requestId?: string;
}
export declare const RESPONSE_CODES: {
    readonly SUCCESS: 200;
    readonly BAD_REQUEST: 400;
    readonly UNAUTHORIZED: 401;
    readonly FORBIDDEN: 403;
    readonly NOT_FOUND: 404;
    readonly INTERNAL_ERROR: 500;
    readonly BUSINESS_ERROR: 1000;
};
export declare class UnifiedResponseService {
    private readonly loggerService;
    constructor(loggerService: LoggerService);
    success<T = any>(data?: T, msg?: string, options?: {
        path?: string;
        executionTime?: number;
        requestId?: string;
        logContext?: string;
    }): UnifiedResponse<T>;
    error<T = any>(msg?: string, data?: T, code?: 500, options?: {
        path?: string;
        executionTime?: number;
        requestId?: string;
        logContext?: string;
        exception?: Error;
        httpStatus?: number;
    }): UnifiedResponse<T>;
    businessError<T = any>(msg: string, data?: T, options?: {
        path?: string;
        executionTime?: number;
        requestId?: string;
        logContext?: string;
    }): UnifiedResponse<T>;
    badRequest<T = any>(msg?: string, data?: T, options?: {
        path?: string;
        executionTime?: number;
        requestId?: string;
        logContext?: string;
    }): UnifiedResponse<T>;
    unauthorized<T = any>(msg?: string, data?: T, options?: {
        path?: string;
        executionTime?: number;
        requestId?: string;
        logContext?: string;
    }): UnifiedResponse<T>;
    forbidden<T = any>(msg?: string, data?: T, options?: {
        path?: string;
        executionTime?: number;
        requestId?: string;
        logContext?: string;
    }): UnifiedResponse<T>;
    notFound<T = any>(msg?: string, data?: T, options?: {
        path?: string;
        executionTime?: number;
        requestId?: string;
        logContext?: string;
    }): UnifiedResponse<T>;
    custom<T = any>(code: number, msg: string, data?: T, options?: {
        path?: string;
        executionTime?: number;
        requestId?: string;
        logContext?: string;
    }): UnifiedResponse<T>;
    isSuccess(response: UnifiedResponse): boolean;
    isError(response: UnifiedResponse): boolean;
}
