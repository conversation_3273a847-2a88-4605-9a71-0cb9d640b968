"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/test-ip/page",{

/***/ "(app-pages-browser)/./app/test-ip/page.tsx":
/*!******************************!*\
  !*** ./app/test-ip/page.tsx ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ TestIPPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Divider,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/typography/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Divider,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/space/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Divider,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/card/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Divider,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Divider,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/divider/index.js\");\n/* harmony import */ var _lib_request__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/request */ \"(app-pages-browser)/./lib/request.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst { Title, Text, Paragraph } = _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_3__[\"default\"];\nfunction TestIPPage() {\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [result, setResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [realPublicIP, setRealPublicIP] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 页面加载时输出环境信息\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        var _navigator_connection, _navigator_connection1;\n        console.log(\"\\uD83C\\uDF10 [Frontend Init] 测试页面初始化...\");\n        console.log(\"\\uD83D\\uDDA5️ [Frontend Init] 浏览器环境信息:\", {\n            页面信息: {\n                URL: window.location.href,\n                域名: window.location.hostname,\n                端口: window.location.port,\n                协议: window.location.protocol,\n                路径: window.location.pathname\n            },\n            访问方式: {\n                是否本地访问: window.location.hostname === \"localhost\",\n                是否内网穿透: window.location.hostname.includes(\"ngrok\") || window.location.hostname.includes(\"tunnel\"),\n                访问类型: window.location.hostname === \"localhost\" ? \"本地开发\" : window.location.hostname.includes(\"ngrok\") ? \"ngrok穿透\" : window.location.hostname.includes(\"tunnel\") ? \"其他穿透\" : \"未知\"\n            },\n            浏览器信息: {\n                用户代理: navigator.userAgent,\n                语言: navigator.language,\n                平台: navigator.platform,\n                在线状态: navigator.onLine\n            },\n            网络信息: {\n                连接类型: ((_navigator_connection = navigator.connection) === null || _navigator_connection === void 0 ? void 0 : _navigator_connection.effectiveType) || \"未知\",\n                网络状态: ((_navigator_connection1 = navigator.connection) === null || _navigator_connection1 === void 0 ? void 0 : _navigator_connection1.downlink) ? \"\".concat(navigator.connection.downlink, \"Mbps\") : \"未知\"\n            },\n            时间信息: {\n                本地时间: new Date().toISOString(),\n                时区: Intl.DateTimeFormat().resolvedOptions().timeZone,\n                时区偏移: new Date().getTimezoneOffset()\n            }\n        });\n        // 输出预期的测试流程\n        console.log(\"\\uD83D\\uDCCB [Frontend Init] 测试流程说明:\", {\n            测试目标: \"IP地址获取和传递功能验证\",\n            测试步骤: [\n                \"1. 获取真实公网IP (通过第三方API)\",\n                \"2. 测试当前IP位置获取 (后端API)\",\n                \"3. 测试指定IP查询 (*******)\",\n                \"4. 测试登录IP记录 (模拟登录失败)\"\n            ],\n            观察要点: [\n                \"前端请求日志 (\\uD83D\\uDCE4 [Frontend Request])\",\n                \"前端响应日志 (\\uD83D\\uDCE5 [Frontend Response])\",\n                \"中间件IP处理 (\\uD83C\\uDF10 [Middleware])\",\n                \"后端IP提取 (\\uD83D\\uDDA5️ [Backend])\",\n                \"登录IP记录 (\\uD83D\\uDD10 [LoginLog])\"\n            ],\n            预期结果: {\n                本地访问: \"IP为模拟值 (**************)\",\n                穿透访问: \"IP为真实公网IP\",\n                地理位置: \"根据IP解析出对应位置\"\n            }\n        });\n    }, []);\n    // 获取真实的公网IP地址\n    const getRealPublicIP = async ()=>{\n        setLoading(true);\n        try {\n            console.log(\"\\uD83C\\uDF0D [Frontend] 开始获取真实公网IP...\");\n            // 使用多个IP查询服务，提高成功率\n            const ipServices = [\n                \"https://api.ipify.org?format=json\",\n                \"https://ipapi.co/json/\",\n                \"https://httpbin.org/ip\",\n                \"https://api.ip.sb/ip\",\n                \"https://ifconfig.me/ip\",\n                \"https://icanhazip.com\"\n            ];\n            for (const service of ipServices){\n                try {\n                    console.log(\"\\uD83D\\uDD0D 尝试获取公网IP: \".concat(service));\n                    const controller = new AbortController();\n                    const timeoutId = setTimeout(()=>controller.abort(), 5000);\n                    const response = await fetch(service, {\n                        method: \"GET\",\n                        signal: controller.signal\n                    });\n                    clearTimeout(timeoutId);\n                    if (!response.ok) continue;\n                    let data;\n                    const contentType = response.headers.get(\"content-type\");\n                    if (contentType && contentType.includes(\"application/json\")) {\n                        data = await response.json();\n                        const ip = data.ip || data.origin || data.query;\n                        if (ip && isValidIPAddress(ip)) {\n                            console.log(\"✅ 成功获取公网IP: \".concat(ip, \" (来源: \").concat(service, \")\"));\n                            setRealPublicIP(ip);\n                            setResult({\n                                type: \"real-public-ip\",\n                                ip: ip,\n                                source: service,\n                                timestamp: new Date().toISOString()\n                            });\n                            return;\n                        }\n                    } else {\n                        const text = await response.text();\n                        const ip = text.trim();\n                        if (isValidIPAddress(ip)) {\n                            console.log(\"✅ 成功获取公网IP: \".concat(ip, \" (来源: \").concat(service, \")\"));\n                            setRealPublicIP(ip);\n                            setResult({\n                                type: \"real-public-ip\",\n                                ip: ip,\n                                source: service,\n                                timestamp: new Date().toISOString()\n                            });\n                            return;\n                        }\n                    }\n                } catch (error) {\n                    console.log(\"❌ 获取公网IP失败: \".concat(service, \" - \").concat(error));\n                    continue;\n                }\n            }\n            throw new Error(\"所有公网IP服务都无法访问\");\n        } catch (error) {\n            console.error(\"❌ [Frontend] 获取真实公网IP失败:\", error);\n            setResult({\n                type: \"error\",\n                error: error.message,\n                timestamp: new Date().toISOString()\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 验证IP地址格式\n    const isValidIPAddress = (ip)=>{\n        const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;\n        return ipv4Regex.test(ip);\n    };\n    // 测试获取当前IP位置\n    const testCurrentIP = async ()=>{\n        setLoading(true);\n        try {\n            console.log(\"\\uD83E\\uDDEA [Frontend Test] 开始测试当前IP获取...\");\n            console.log(\"\\uD83C\\uDF10 [Frontend Test] 当前环境信息:\", {\n                页面URL: window.location.href,\n                域名: window.location.hostname,\n                是否内网穿透: window.location.hostname.includes(\"ngrok\") || window.location.hostname.includes(\"tunnel\"),\n                用户代理: navigator.userAgent.substring(0, 100) + \"...\",\n                时间戳: new Date().toISOString()\n            });\n            console.log(\"\\uD83D\\uDCE4 [Frontend Test] 准备发送请求到: /api/v1/ip-location/current\");\n            const startTime = Date.now();\n            const response = await _lib_request__WEBPACK_IMPORTED_MODULE_2__[\"default\"].get(\"/api/v1/ip-location/current\");\n            const endTime = Date.now();\n            console.log(\"\\uD83D\\uDCE5 [Frontend Test] 请求完成:\", {\n                耗时: \"\".concat(endTime - startTime, \"ms\"),\n                响应状态: response.status,\n                响应数据: response.data,\n                时间戳: new Date().toISOString()\n            });\n            setResult({\n                type: \"current-ip\",\n                data: response.data,\n                requestTime: endTime - startTime,\n                timestamp: new Date().toISOString()\n            });\n        } catch (error) {\n            var _error_response, _error_response1, _error_response2;\n            console.error(\"❌ [Frontend Test] 当前IP测试失败:\", {\n                错误类型: error.name,\n                错误消息: error.message,\n                响应状态: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status,\n                响应数据: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.data,\n                完整错误: error,\n                时间戳: new Date().toISOString()\n            });\n            setResult({\n                type: \"error\",\n                error: error.message,\n                response: (_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.data,\n                timestamp: new Date().toISOString()\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 测试查询指定IP\n    const testQueryIP = async ()=>{\n        setLoading(true);\n        try {\n            const testIP = \"*******\"; // Google DNS\n            console.log(\"\\uD83E\\uDDEA [Frontend Test] 开始测试IP查询...\");\n            console.log(\"\\uD83C\\uDFAF [Frontend Test] 查询参数:\", {\n                目标IP: testIP,\n                IP类型: \"Google DNS服务器\",\n                预期位置: \"美国\",\n                包含风险评估: false,\n                时间戳: new Date().toISOString()\n            });\n            const queryUrl = \"/api/v1/ip-location/query?ip=\".concat(testIP, \"&includeRisk=false\");\n            console.log(\"\\uD83D\\uDCE4 [Frontend Test] 准备发送查询请求:\", queryUrl);\n            const startTime = Date.now();\n            const response = await _lib_request__WEBPACK_IMPORTED_MODULE_2__[\"default\"].get(queryUrl);\n            const endTime = Date.now();\n            console.log(\"\\uD83D\\uDCE5 [Frontend Test] IP查询完成:\", {\n                查询IP: testIP,\n                耗时: \"\".concat(endTime - startTime, \"ms\"),\n                响应状态: response.status,\n                地理位置: response.data ? {\n                    国家: response.data.country,\n                    省份: response.data.province,\n                    城市: response.data.city,\n                    运营商: response.data.isp,\n                    置信度: response.data.confidence\n                } : \"无数据\",\n                完整响应: response.data,\n                时间戳: new Date().toISOString()\n            });\n            setResult({\n                type: \"query-ip\",\n                testIP,\n                data: response.data,\n                requestTime: endTime - startTime,\n                timestamp: new Date().toISOString()\n            });\n        } catch (error) {\n            var _error_response, _error_response1, _error_response2;\n            console.error(\"❌ [Frontend Test] IP查询失败:\", {\n                查询IP: \"*******\",\n                错误类型: error.name,\n                错误消息: error.message,\n                响应状态: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status,\n                响应数据: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.data,\n                可能原因: [\n                    \"后端服务未启动\",\n                    \"IP解析服务异常\",\n                    \"网络连接问题\"\n                ],\n                时间戳: new Date().toISOString()\n            });\n            setResult({\n                type: \"error\",\n                error: error.message,\n                response: (_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.data,\n                timestamp: new Date().toISOString()\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 测试登录接口（观察IP日志）\n    const testLoginIP = async ()=>{\n        setLoading(true);\n        try {\n            console.log(\"\\uD83E\\uDDEA [Frontend Test] 开始测试登录IP获取...\");\n            console.log(\"\\uD83D\\uDD10 [Frontend Test] 登录测试说明:\", {\n                目的: \"观察登录时的IP获取和记录过程\",\n                预期结果: \"登录失败（使用错误凭据）\",\n                观察重点: [\n                    \"IP地址获取\",\n                    \"登录日志记录\",\n                    \"错误处理\"\n                ],\n                测试凭据: {\n                    手机号: \"12345678910\",\n                    密码: \"123456 (错误密码)\"\n                },\n                时间戳: new Date().toISOString()\n            });\n            console.log(\"\\uD83D\\uDCE4 [Frontend Test] 发送登录请求...\");\n            const startTime = Date.now();\n            // 这里故意使用错误的登录信息，只是为了触发IP获取逻辑\n            const response = await _lib_request__WEBPACK_IMPORTED_MODULE_2__[\"default\"].post(\"/api/user-auth/password\", {\n                phone: \"12345678910\",\n                password: \"123456\"\n            });\n            const endTime = Date.now();\n            console.log(\"\\uD83D\\uDCE5 [Frontend Test] 登录响应 (意外成功):\", {\n                耗时: \"\".concat(endTime - startTime, \"ms\"),\n                响应状态: response.status,\n                响应数据: response.data,\n                注意: \"这不应该成功，请检查后端验证逻辑\",\n                时间戳: new Date().toISOString()\n            });\n            setResult({\n                type: \"login-test\",\n                data: response.data,\n                requestTime: endTime - startTime,\n                timestamp: new Date().toISOString()\n            });\n        } catch (error) {\n            var _error_response, _error_response1, _error_response2;\n            console.log(\"\\uD83D\\uDCDD [Frontend Test] 登录测试完成 (预期失败):\", {\n                错误类型: error.name,\n                错误消息: error.message,\n                响应状态: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status,\n                响应数据: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.data,\n                分析: {\n                    是否预期失败: true,\n                    失败原因: \"使用了错误的登录凭据\",\n                    IP获取状态: \"应该已触发IP获取和日志记录\",\n                    后续检查: \"查看后端控制台的 [LoginLog] 日志\"\n                },\n                时间戳: new Date().toISOString()\n            });\n            setResult({\n                type: \"login-test\",\n                error: error.message,\n                response: (_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.data,\n                note: \"这是预期的失败，主要用于观察IP获取日志\",\n                timestamp: new Date().toISOString()\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"24px\",\n            maxWidth: \"1200px\",\n            margin: \"0 auto\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                level: 2,\n                children: \"\\uD83E\\uDDEA IP地址获取测试页面\"\n            }, void 0, false, {\n                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                lineNumber: 346,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                children: \"这个页面用于测试前端到后端的IP地址传递和获取功能。 请打开浏览器开发者工具的控制台，以及后端服务器的日志，观察IP获取过程。\"\n            }, void 0, false, {\n                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                lineNumber: 348,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                direction: \"vertical\",\n                size: \"large\",\n                style: {\n                    width: \"100%\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83C\\uDFAF 测试功能\",\n                        size: \"small\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                wrap: true,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        type: \"primary\",\n                                        loading: loading,\n                                        onClick: getRealPublicIP,\n                                        style: {\n                                            background: \"#52c41a\",\n                                            borderColor: \"#52c41a\"\n                                        },\n                                        children: \"\\uD83C\\uDF0D 获取真实公网IP\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                        lineNumber: 358,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        loading: loading,\n                                        onClick: testCurrentIP,\n                                        children: \"测试获取当前IP位置\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                        lineNumber: 367,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        loading: loading,\n                                        onClick: testQueryIP,\n                                        children: \"测试查询指定IP (*******)\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        loading: loading,\n                                        onClick: testLoginIP,\n                                        danger: true,\n                                        children: \"测试登录IP获取 (会失败)\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                lineNumber: 357,\n                                columnNumber: 11\n                            }, this),\n                            realPublicIP && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginTop: \"12px\",\n                                    padding: \"8px\",\n                                    background: \"#f6ffed\",\n                                    border: \"1px solid #b7eb8f\",\n                                    borderRadius: \"6px\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    strong: true,\n                                    style: {\n                                        color: \"#52c41a\"\n                                    },\n                                    children: [\n                                        \"\\uD83C\\uDF0D 你的真实公网IP: \",\n                                        realPublicIP\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 392,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                lineNumber: 391,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 356,\n                        columnNumber: 9\n                    }, this),\n                    result && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83D\\uDCCA 测试结果\",\n                        size: \"small\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                background: \"#f5f5f5\",\n                                padding: \"16px\",\n                                borderRadius: \"6px\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                style: {\n                                    margin: 0,\n                                    fontSize: \"12px\",\n                                    lineHeight: \"1.4\"\n                                },\n                                children: JSON.stringify(result, null, 2)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                lineNumber: 401,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                            lineNumber: 400,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 399,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 408,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83D\\uDCCB 观察要点\",\n                        size: \"small\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            direction: \"vertical\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            children: \"\\uD83C\\uDF10 前端中间件日志：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 414,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"查看浏览器控制台，观察 [Middleware] 标记的日志\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 413,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            children: \"\\uD83D\\uDDA5️ 后端IP提取日志：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 419,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"查看后端控制台，观察 [Backend] 标记的日志\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 420,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 418,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            children: \"\\uD83D\\uDD10 登录日志：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 424,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"查看后端控制台，观察 [LoginLog] 标记的日志\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 425,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 423,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            children: \"\\uD83D\\uDD0D 重点观察：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 429,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"IP地址是否从前端正确传递到后端，以及各个环节的IP获取情况\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 430,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 428,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                            lineNumber: 412,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 411,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83E\\uDD14 为什么本地开发获取到127.0.0.1？\",\n                        size: \"small\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            direction: \"vertical\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            style: {\n                                                color: \"#fa8c16\"\n                                            },\n                                            children: \"\\uD83C\\uDFE0 本地开发环境：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 439,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"浏览器 → localhost:3000 → 后端API，所有请求都来自本机，所以IP是127.0.0.1\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 440,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 438,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            style: {\n                                                color: \"#52c41a\"\n                                            },\n                                            children: \"\\uD83C\\uDF0D 生产环境：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 444,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"用户浏览器 → CDN/负载均衡 → Web服务器 → 后端API，能获取到真实公网IP\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 445,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 443,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            style: {\n                                                color: \"#1890ff\"\n                                            },\n                                            children: \"\\uD83C\\uDFAD 模拟解决方案：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 449,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"中间件已配置在开发环境使用模拟公网IP (**************) 进行测试\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 450,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 448,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            style: {\n                                                color: \"#722ed1\"\n                                            },\n                                            children: \"\\uD83E\\uDDEA 真实IP对比：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 454,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: '点击\"获取真实公网IP\"按钮，对比你的真实公网IP和后端获取的IP'\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 455,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 453,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                            lineNumber: 437,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 436,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83C\\uDF0D 当前环境信息\",\n                        size: \"small\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            direction: \"vertical\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    children: [\n                                        \"浏览器 User-Agent: \",\n                                         true ? navigator.userAgent : 0\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 463,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    children: [\n                                        \"当前时间: \",\n                                        new Date().toISOString()\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 464,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    children: [\n                                        \"页面URL: \",\n                                         true ? window.location.href : 0\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 465,\n                                    columnNumber: 13\n                                }, this),\n                                 true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    style: {\n                                        color: window.location.hostname.includes(\"ngrok\") || window.location.hostname.includes(\"tunnel\") ? \"#52c41a\" : \"#fa8c16\",\n                                        fontWeight: \"bold\"\n                                    },\n                                    children: [\n                                        \"访问方式: \",\n                                        window.location.hostname.includes(\"ngrok\") || window.location.hostname.includes(\"tunnel\") ? \"\\uD83C\\uDF10 内网穿透访问 (可获取真实IP)\" : \"\\uD83C\\uDFE0 本地访问 (模拟IP)\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 467,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                            lineNumber: 462,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 461,\n                        columnNumber: 9\n                    }, this),\n                     true && !window.location.hostname.includes(\"ngrok\") && !window.location.hostname.includes(\"tunnel\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83D\\uDE80 想要测试真实IP获取？\",\n                        size: \"small\",\n                        style: {\n                            borderColor: \"#52c41a\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            direction: \"vertical\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    strong: true,\n                                    style: {\n                                        color: \"#52c41a\"\n                                    },\n                                    children: \"使用内网穿透获取真实IP：\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 485,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        background: \"#f6ffed\",\n                                        padding: \"12px\",\n                                        borderRadius: \"6px\",\n                                        border: \"1px solid #b7eb8f\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            code: true,\n                                            children: \"1. 安装ngrok: npm install -g ngrok\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 487,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 487,\n                                            columnNumber: 67\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            code: true,\n                                            children: \"2. 穿透前端: ngrok http 3000\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 488,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 488,\n                                            columnNumber: 59\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            code: true,\n                                            children: \"3. 访问ngrok提供的公网地址\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 489,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 489,\n                                            columnNumber: 52\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            code: true,\n                                            children: \"4. 重新测试IP获取功能\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 490,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 486,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    type: \"secondary\",\n                                    children: \"通过内网穿透，你可以模拟真实的生产环境，获取到真实的公网IP地址！\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 492,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                            lineNumber: 484,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 483,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                lineNumber: 353,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n        lineNumber: 345,\n        columnNumber: 5\n    }, this);\n}\n_s(TestIPPage, \"nEdSa3/Nhnh++ZCrXx2cdHtF+aE=\");\n_c = TestIPPage;\nvar _c;\n$RefreshReg$(_c, \"TestIPPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/test-ip/page.tsx\n"));

/***/ })

});