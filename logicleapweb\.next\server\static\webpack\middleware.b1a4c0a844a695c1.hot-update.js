"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./middleware.ts":
/*!***********************!*\
  !*** ./middleware.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n\nfunction middleware(request) {\n    // 只处理 API 请求\n    if (request.nextUrl.pathname.startsWith(\"/api/\")) {\n        // 获取客户端真实IP地址\n        const forwarded = request.headers.get(\"x-forwarded-for\");\n        const realIp = request.headers.get(\"x-real-ip\");\n        const cfConnectingIp = request.headers.get(\"cf-connecting-ip\");\n        const remoteAddr = request.headers.get(\"remote-addr\");\n        // 尝试多种方式获取IP\n        let clientIp = request.ip;\n        if (!clientIp && forwarded) {\n            clientIp = forwarded.split(\",\")[0].trim();\n        }\n        if (!clientIp && realIp) {\n            clientIp = realIp;\n        }\n        if (!clientIp && cfConnectingIp) {\n            clientIp = cfConnectingIp;\n        }\n        if (!clientIp && remoteAddr) {\n            clientIp = remoteAddr;\n        }\n        // 如果还是没有获取到，尝试从其他来源\n        if (!clientIp) {\n            // 检查是否通过内网穿透访问\n            const host = request.headers.get(\"host\");\n            const isNgrokTunnel = host?.includes(\"ngrok\") || host?.includes(\"tunnel\") || host?.includes(\"vicp.fun\");\n            if (true) {\n                if (isNgrokTunnel) {\n                    // 通过内网穿透访问，尝试从其他头部获取真实IP\n                    // 有些内网穿透服务会使用不同的头部名称\n                    const possibleIpHeaders = [\n                        \"x-original-forwarded-for\",\n                        \"x-forwarded-for-original\",\n                        \"x-client-ip-original\",\n                        \"x-tunnel-client-ip\",\n                        \"cf-connecting-ip\",\n                        \"true-client-ip\"\n                    ];\n                    for (const header of possibleIpHeaders){\n                        const headerValue = request.headers.get(header);\n                        if (headerValue && headerValue !== \"127.0.0.1\") {\n                            clientIp = headerValue.split(\",\")[0].trim();\n                            console.log(`🌐 [Middleware] 从 ${header} 获取到IP:`, clientIp);\n                            break;\n                        }\n                    }\n                    // 如果还是没有获取到，使用一个测试IP\n                    if (!clientIp || clientIp === \"127.0.0.1\") {\n                        clientIp = \"**************\"; // 使用测试IP\n                        console.log(\"\\uD83C\\uDFAD [Middleware] 内网穿透环境未获取到真实IP，使用测试IP:\", clientIp);\n                    }\n                } else {\n                    // 本地开发环境，使用模拟公网IP进行测试\n                    clientIp = \"**************\"; // 模拟的公网IP（百度的IP）\n                    console.log(\"\\uD83C\\uDFAD [Middleware] 本地开发环境使用模拟公网IP:\", clientIp);\n                }\n            } else {}\n        }\n        // 添加详细的IP获取日志\n        const host = request.headers.get(\"host\");\n        const isNgrokTunnel = host?.includes(\"ngrok\") || host?.includes(\"tunnel\") || host?.includes(\"vicp.fun\");\n        // 获取所有可能的IP相关头部\n        const allHeaders = {};\n        const ipRelatedHeaders = [\n            \"x-forwarded-for\",\n            \"x-real-ip\",\n            \"cf-connecting-ip\",\n            \"remote-addr\",\n            \"x-original-forwarded-for\",\n            \"x-forwarded-for-original\",\n            \"x-client-ip-original\",\n            \"x-tunnel-client-ip\",\n            \"true-client-ip\",\n            \"x-client-ip\",\n            \"x-cluster-client-ip\"\n        ];\n        ipRelatedHeaders.forEach((header)=>{\n            allHeaders[header] = request.headers.get(header);\n        });\n        console.log(\"\\uD83C\\uDF10 [Middleware] IP地址获取详情:\", {\n            请求信息: {\n                url: request.nextUrl.pathname,\n                method: request.method,\n                host: host,\n                是否内网穿透: isNgrokTunnel,\n                穿透类型: host?.includes(\"ngrok\") ? \"ngrok\" : host?.includes(\"vicp.fun\") ? \"vicp.fun\" : host?.includes(\"tunnel\") ? \"tunnel\" : \"未知\"\n            },\n            所有IP相关头部: allHeaders,\n            原始IP来源: {\n                \"request.ip\": request.ip,\n                \"x-forwarded-for\": forwarded,\n                \"x-real-ip\": realIp,\n                \"cf-connecting-ip\": cfConnectingIp,\n                \"remote-addr\": remoteAddr\n            },\n            最终确定IP: clientIp,\n            环境: \"development\",\n            时间戳: new Date().toISOString()\n        });\n        // 创建新的请求头\n        const requestHeaders = new Headers(request.headers);\n        // 设置真实IP相关的头部\n        requestHeaders.set(\"x-forwarded-for\", clientIp);\n        requestHeaders.set(\"x-real-ip\", clientIp);\n        requestHeaders.set(\"x-client-ip\", clientIp);\n        // 创建新的响应，将修改后的头部传递给后端\n        const response = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.rewrite(request.nextUrl, {\n            request: {\n                headers: requestHeaders\n            }\n        });\n        return response;\n    }\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n}\nconst config = {\n    matcher: [\n        /*\r\n     * 匹配所有 API 路由\r\n     */ \"/api/:path*\"\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./middleware.ts\n");

/***/ })

});