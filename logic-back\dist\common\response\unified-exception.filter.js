"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotFoundException = exports.ForbiddenException = exports.UnauthorizedException = exports.ValidationException = exports.BusinessException = exports.UnifiedExceptionFilter = void 0;
const common_1 = require("@nestjs/common");
const uuid_1 = require("uuid");
let UnifiedExceptionFilter = class UnifiedExceptionFilter {
    responseService;
    constructor(responseService) {
        this.responseService = responseService;
    }
    catch(exception, host) {
        const ctx = host.switchToHttp();
        const response = ctx.getResponse();
        const request = ctx.getRequest();
        const requestId = request.headers['x-request-id'] || (0, uuid_1.v4)();
        const startTime = request['startTime'];
        const executionTime = startTime ? Date.now() - startTime : undefined;
        let httpStatus = common_1.HttpStatus.INTERNAL_SERVER_ERROR;
        let businessCode = RESPONSE_CODES.INTERNAL_ERROR;
        let message = '服务器内部错误';
        let errorData = null;
        if (exception instanceof common_1.HttpException) {
            httpStatus = exception.getStatus();
            const exceptionResponse = exception.getResponse();
            if (typeof exceptionResponse === 'string') {
                message = exceptionResponse;
            }
            else if (typeof exceptionResponse === 'object' && exceptionResponse !== null) {
                const exceptionObj = exceptionResponse;
                if (exceptionObj.msg) {
                    message = exceptionObj.msg;
                }
                else if (exceptionObj.message) {
                    message = Array.isArray(exceptionObj.message)
                        ? exceptionObj.message.join(', ')
                        : exceptionObj.message;
                }
                if (exceptionObj.code) {
                    businessCode = exceptionObj.code;
                }
                if (exceptionObj.data !== undefined) {
                    errorData = exceptionObj.data;
                }
            }
            if (businessCode === RESPONSE_CODES.INTERNAL_ERROR) {
                businessCode = this.mapHttpStatusToBusinessCode(httpStatus);
            }
            if (message === '服务器内部错误') {
                message = this.getDefaultMessageByHttpStatus(httpStatus);
            }
        }
        else if (exception instanceof Error) {
            message = exception.message || '未知错误';
            errorData = {
                name: exception.name,
                ...(process.env.NODE_ENV !== 'production' && { stack: exception.stack })
            };
        }
        const responseBody = this.responseService.error(message, errorData, businessCode, {
            path: request.url,
            executionTime,
            requestId,
            logContext: 'UnifiedExceptionFilter',
            exception: exception instanceof Error ? exception : new Error(String(exception)),
            httpStatus
        });
        response.setHeader('X-Request-ID', requestId);
        response.status(httpStatus).json(responseBody);
    }
    mapHttpStatusToBusinessCode(httpStatus) {
        switch (httpStatus) {
            case common_1.HttpStatus.BAD_REQUEST:
                return RESPONSE_CODES.BAD_REQUEST;
            case common_1.HttpStatus.UNAUTHORIZED:
                return RESPONSE_CODES.UNAUTHORIZED;
            case common_1.HttpStatus.FORBIDDEN:
                return RESPONSE_CODES.FORBIDDEN;
            case common_1.HttpStatus.NOT_FOUND:
                return RESPONSE_CODES.NOT_FOUND;
            case common_1.HttpStatus.INTERNAL_SERVER_ERROR:
                return RESPONSE_CODES.INTERNAL_ERROR;
            default:
                return RESPONSE_CODES.INTERNAL_ERROR;
        }
    }
    getDefaultMessageByHttpStatus(httpStatus) {
        switch (httpStatus) {
            case common_1.HttpStatus.BAD_REQUEST:
                return '请求参数错误';
            case common_1.HttpStatus.UNAUTHORIZED:
                return '请先登录后再访问';
            case common_1.HttpStatus.FORBIDDEN:
                return '您没有权限执行此操作';
            case common_1.HttpStatus.NOT_FOUND:
                return '请求的资源不存在';
            case common_1.HttpStatus.METHOD_NOT_ALLOWED:
                return '请求方法不被允许';
            case common_1.HttpStatus.CONFLICT:
                return '请求冲突';
            case common_1.HttpStatus.UNPROCESSABLE_ENTITY:
                return '请求数据格式错误';
            case common_1.HttpStatus.TOO_MANY_REQUESTS:
                return '请求过于频繁，请稍后再试';
            case common_1.HttpStatus.INTERNAL_SERVER_ERROR:
                return '服务器内部错误';
            case common_1.HttpStatus.BAD_GATEWAY:
                return '网关错误';
            case common_1.HttpStatus.SERVICE_UNAVAILABLE:
                return '服务暂时不可用';
            case common_1.HttpStatus.GATEWAY_TIMEOUT:
                return '网关超时';
            default:
                return `请求错误 (${httpStatus})`;
        }
    }
};
exports.UnifiedExceptionFilter = UnifiedExceptionFilter;
exports.UnifiedExceptionFilter = UnifiedExceptionFilter = __decorate([
    (0, common_1.Catch)(),
    __metadata("design:paramtypes", [typeof (_a = typeof UnifiedResponseService !== "undefined" && UnifiedResponseService) === "function" ? _a : Object])
], UnifiedExceptionFilter);
class BusinessException extends common_1.HttpException {
    constructor(message, businessCode = RESPONSE_CODES.BUSINESS_ERROR, httpStatus = common_1.HttpStatus.BAD_REQUEST, data) {
        super({
            msg: message,
            code: businessCode,
            data
        }, httpStatus);
    }
}
exports.BusinessException = BusinessException;
class ValidationException extends BusinessException {
    constructor(message, data) {
        super(message, RESPONSE_CODES.BAD_REQUEST, common_1.HttpStatus.BAD_REQUEST, data);
    }
}
exports.ValidationException = ValidationException;
class UnauthorizedException extends BusinessException {
    constructor(message = '请先登录后再访问', data) {
        super(message, RESPONSE_CODES.UNAUTHORIZED, common_1.HttpStatus.UNAUTHORIZED, data);
    }
}
exports.UnauthorizedException = UnauthorizedException;
class ForbiddenException extends BusinessException {
    constructor(message = '您没有权限执行此操作', data) {
        super(message, RESPONSE_CODES.FORBIDDEN, common_1.HttpStatus.FORBIDDEN, data);
    }
}
exports.ForbiddenException = ForbiddenException;
class NotFoundException extends BusinessException {
    constructor(message = '请求的资源不存在', data) {
        super(message, RESPONSE_CODES.NOT_FOUND, common_1.HttpStatus.NOT_FOUND, data);
    }
}
exports.NotFoundException = NotFoundException;
//# sourceMappingURL=unified-exception.filter.js.map