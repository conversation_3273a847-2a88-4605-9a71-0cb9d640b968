"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IpLocationController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const ip_location_application_service_1 = require("../application/services/ip-location-application.service");
const ip_location_facade_service_1 = require("../application/services/ip-location-facade.service");
const ip_query_request_dto_1 = require("../application/dto/requests/ip-query.request.dto");
const risk_check_request_dto_1 = require("../application/dto/requests/risk-check.request.dto");
const trust_location_request_dto_1 = require("../application/dto/requests/trust-location.request.dto");
const location_info_response_dto_1 = require("../application/dto/responses/location-info.response.dto");
const risk_assessment_response_dto_1 = require("../application/dto/responses/risk-assessment.response.dto");
const location_stats_response_dto_1 = require("../application/dto/responses/location-stats.response.dto");
let IpLocationController = class IpLocationController {
    ipLocationApplicationService;
    ipLocationFacadeService;
    constructor(ipLocationApplicationService, ipLocationFacadeService) {
        this.ipLocationApplicationService = ipLocationApplicationService;
        this.ipLocationFacadeService = ipLocationFacadeService;
    }
    async queryIpLocationV2(query) {
        const result = await this.ipLocationFacadeService.getLocationByIP(query.ip, query.includeRisk);
        if (!result.success) {
            throw new Error(result.error || '查询失败');
        }
        return {
            success: true,
            data: result.data,
            message: result.message,
            meta: {
                executionTime: result.executionTime,
                fromCache: result.fromCache
            }
        };
    }
    async checkLoginRisk(request) {
        return await this.ipLocationApplicationService.checkLoginRisk(request);
    }
    async getUserLocationStats(userId, days = 30, includeTrusted = true) {
        return await this.ipLocationApplicationService.getUserLocationStatistics(userId, days);
    }
    async setTrustedLocation(userId, request) {
        await this.ipLocationApplicationService.setTrustedLocation(userId, request);
        return {
            message: '可信登录地设置成功',
            data: {
                userId,
                location: {
                    province: request.province,
                    city: request.city
                },
                setAt: new Date().toISOString()
            }
        };
    }
    async getCurrentIpLocation(request) {
        const clientIp = this.extractClientIP(request);
        const queryRequest = {
            ip: clientIp,
            includeRisk: false
        };
        return await this.ipLocationApplicationService.queryIpLocation(queryRequest);
    }
    async healthCheck() {
        return {
            status: 'UP',
            timestamp: new Date().toISOString(),
            service: 'ip-location'
        };
    }
    extractClientIP(request) {
        const forwarded = request.headers['x-forwarded-for'];
        const realIp = request.headers['x-real-ip'];
        const cfConnectingIp = request.headers['cf-connecting-ip'];
        const xClientIp = request.headers['x-client-ip'];
        const xClusterClientIp = request.headers['x-cluster-client-ip'];
        const clientIp = request.connection?.remoteAddress ||
            request.socket?.remoteAddress ||
            request?.ip;
        console.log('🖥️ [Backend] IP地址提取详情:', {
            请求路径: request.url,
            请求方法: request.method,
            请求头IP信息: {
                'x-forwarded-for': forwarded,
                'x-real-ip': realIp,
                'cf-connecting-ip': cfConnectingIp,
                'x-client-ip': xClientIp,
                'x-cluster-client-ip': xClusterClientIp,
            },
            连接IP信息: {
                'connection.remoteAddress': request.connection?.remoteAddress,
                'socket.remoteAddress': request.socket?.remoteAddress,
                'request.ip': request?.ip,
                '最终连接IP': clientIp
            },
            时间戳: new Date().toISOString()
        });
        const ipSources = [
            forwarded?.split(',')[0]?.trim(),
            cfConnectingIp,
            realIp,
            xClientIp,
            xClusterClientIp,
            clientIp
        ];
        for (const ip of ipSources) {
            if (ip) {
                const cleanIp = this.cleanAndValidateIP(ip);
                if (cleanIp) {
                    console.log('✅ [Backend] IP地址提取成功:', {
                        原始IP: ip,
                        清理后IP: cleanIp,
                        来源: this.getIpSourceName(ip, {
                            forwarded: forwarded?.split(',')[0]?.trim(),
                            cfConnectingIp,
                            realIp,
                            xClientIp,
                            xClusterClientIp,
                            clientIp
                        }),
                        时间戳: new Date().toISOString()
                    });
                    return cleanIp;
                }
            }
        }
        console.log('⚠️ [Backend] 使用默认IP地址:', {
            原因: '所有IP源都无效',
            默认IP: '127.0.0.1',
            时间戳: new Date().toISOString()
        });
        return '127.0.0.1';
    }
    cleanAndValidateIP(ip) {
        if (!ip)
            return null;
        let cleanIp = ip.trim();
        cleanIp = cleanIp.replace('::ffff:', '');
        if (cleanIp === '::1') {
            return '127.0.0.1';
        }
        if (this.isValidIP(cleanIp)) {
            return cleanIp;
        }
        return null;
    }
    isValidIP(ip) {
        const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
        const ipv6Regex = /^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^::1$|^::$/;
        return ipv4Regex.test(ip) || ipv6Regex.test(ip);
    }
    getIpSourceName(ip, sources) {
        if (ip === sources.forwarded)
            return 'x-forwarded-for';
        if (ip === sources.cfConnectingIp)
            return 'cf-connecting-ip (Cloudflare)';
        if (ip === sources.realIp)
            return 'x-real-ip';
        if (ip === sources.xClientIp)
            return 'x-client-ip';
        if (ip === sources.xClusterClientIp)
            return 'x-cluster-client-ip';
        if (ip === sources.clientIp)
            return 'connection.remoteAddress';
        return '未知来源';
    }
};
exports.IpLocationController = IpLocationController;
__decorate([
    (0, common_1.Get)('query'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: '查询IP地理位置 (DDD架构)',
        description: '基于DDD架构的IP地理位置查询，性能更优，功能更强'
    }),
    (0, swagger_1.ApiQuery)({ name: 'ip', description: 'IP地址', example: '**************' }),
    (0, swagger_1.ApiQuery)({
        name: 'includeRisk',
        description: '是否包含风险评估',
        required: false,
        type: Boolean,
        example: false
    }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '请求参数错误' }),
    (0, swagger_1.ApiResponse)({ status: 500, description: '服务器内部错误' }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [ip_query_request_dto_1.IpQueryRequestDto]),
    __metadata("design:returntype", Promise)
], IpLocationController.prototype, "queryIpLocationV2", null);
__decorate([
    (0, common_1.Post)('check-risk'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: '登录风险检查',
        description: '基于IP地址和用户历史进行登录风险评估'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '风险评估成功',
        type: risk_assessment_response_dto_1.RiskAssessmentResponseDto
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '请求参数错误' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '用户不存在' }),
    (0, swagger_1.ApiResponse)({ status: 500, description: '服务器内部错误' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [risk_check_request_dto_1.RiskCheckRequestDto]),
    __metadata("design:returntype", Promise)
], IpLocationController.prototype, "checkLoginRisk", null);
__decorate([
    (0, common_1.Get)('user/:userId/stats'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: '获取用户位置统计',
        description: '获取用户的常用登录地统计信息'
    }),
    (0, swagger_1.ApiParam)({ name: 'userId', description: '用户ID', type: Number }),
    (0, swagger_1.ApiQuery)({
        name: 'days',
        description: '统计天数',
        required: false,
        type: Number,
        example: 30
    }),
    (0, swagger_1.ApiQuery)({
        name: 'includeTrusted',
        description: '是否包含可信位置',
        required: false,
        type: Boolean,
        example: true
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '查询成功',
        type: location_stats_response_dto_1.LocationStatsResponseDto
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '请求参数错误' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '用户不存在' }),
    (0, swagger_1.ApiResponse)({ status: 500, description: '服务器内部错误' }),
    __param(0, (0, common_1.Param)('userId')),
    __param(1, (0, common_1.Query)('days')),
    __param(2, (0, common_1.Query)('includeTrusted')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number, Boolean]),
    __metadata("design:returntype", Promise)
], IpLocationController.prototype, "getUserLocationStats", null);
__decorate([
    (0, common_1.Post)('user/:userId/trust'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: '设置可信登录地',
        description: '将指定位置设置为用户的可信登录地'
    }),
    (0, swagger_1.ApiParam)({ name: 'userId', description: '用户ID', type: Number }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '设置成功' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '请求参数错误' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '用户不存在' }),
    (0, swagger_1.ApiResponse)({ status: 500, description: '服务器内部错误' }),
    __param(0, (0, common_1.Param)('userId')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, trust_location_request_dto_1.TrustLocationRequestDto]),
    __metadata("design:returntype", Promise)
], IpLocationController.prototype, "setTrustedLocation", null);
__decorate([
    (0, common_1.Get)('current'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: '获取当前IP位置',
        description: '获取当前请求IP的地理位置信息'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '查询成功',
        type: location_info_response_dto_1.LocationInfoResponseDto
    }),
    (0, swagger_1.ApiResponse)({ status: 500, description: '服务器内部错误' }),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], IpLocationController.prototype, "getCurrentIpLocation", null);
__decorate([
    (0, common_1.Get)('health'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: '健康检查',
        description: 'IP地理位置服务健康检查'
    }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '服务正常' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], IpLocationController.prototype, "healthCheck", null);
exports.IpLocationController = IpLocationController = __decorate([
    (0, swagger_1.ApiTags)('IP地理位置'),
    (0, swagger_1.ApiBearerAuth)('access-token'),
    (0, common_1.Controller)('api/v1/ip-location'),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({
        transform: true,
        transformOptions: { enableImplicitConversion: true },
        whitelist: true,
        forbidNonWhitelisted: true
    })),
    __metadata("design:paramtypes", [ip_location_application_service_1.IpLocationApplicationService,
        ip_location_facade_service_1.IpLocationFacadeService])
], IpLocationController);
//# sourceMappingURL=ip-location.controller.js.map