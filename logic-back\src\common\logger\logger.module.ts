import { Modu<PERSON> } from '@nestjs/common';
import { WinstonModule } from 'nest-winston';
import { LoggerService } from './logger.service';
import { ConsoleOverrideService } from './console-override.service';
import { createLoggerConfig } from './logger.config';

@Module({
  imports: [
    WinstonModule.forRoot(createLoggerConfig())
  ],
  controllers: [],
  providers: [LoggerService, ConsoleOverrideService],
  exports: [LoggerService, ConsoleOverrideService, WinstonModule],
})
export class LoggerModule {}
