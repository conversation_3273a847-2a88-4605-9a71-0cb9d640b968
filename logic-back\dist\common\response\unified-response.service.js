"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UnifiedResponseService = exports.RESPONSE_CODES = void 0;
const common_1 = require("@nestjs/common");
const logger_service_1 = require("../logger/logger.service");
exports.RESPONSE_CODES = {
    SUCCESS: 200,
    BAD_REQUEST: 400,
    UNAUTHORIZED: 401,
    FORBIDDEN: 403,
    NOT_FOUND: 404,
    INTERNAL_ERROR: 500,
    BUSINESS_ERROR: 1000,
};
let UnifiedResponseService = class UnifiedResponseService {
    loggerService;
    constructor(loggerService) {
        this.loggerService = loggerService;
    }
    success(data, msg = '操作成功', options) {
        const response = {
            code: exports.RESPONSE_CODES.SUCCESS,
            msg,
            data: data,
            timestamp: new Date().toISOString(),
            path: options?.path,
            executionTime: options?.executionTime ? `${options.executionTime}ms` : undefined,
            requestId: options?.requestId,
        };
        if (options?.logContext) {
            this.loggerService.log(`Success: ${msg} - ${options.path || 'Unknown'}`, options.logContext);
        }
        return response;
    }
    error(msg = '系统错误', data, code = exports.RESPONSE_CODES.INTERNAL_ERROR, options) {
        const response = {
            code,
            msg,
            data: data,
            timestamp: new Date().toISOString(),
            path: options?.path,
            executionTime: options?.executionTime ? `${options.executionTime}ms` : undefined,
            requestId: options?.requestId,
        };
        if (options?.logContext) {
            this.loggerService.error(`Error: ${msg} - ${options.path || 'Unknown'} - Code: ${code}`, options?.exception?.stack, options.logContext);
            this.loggerService.error('Error Details', JSON.stringify({
                path: options.path,
                code,
                message: msg,
                data,
                httpStatus: options.httpStatus,
                requestId: options.requestId,
                timestamp: response.timestamp,
                exception: options.exception ? {
                    name: options.exception.name,
                    message: options.exception.message,
                    stack: options.exception.stack
                } : undefined
            }), options.logContext);
        }
        return response;
    }
    businessError(msg, data, options) {
        return this.error(msg, data, exports.RESPONSE_CODES.BUSINESS_ERROR, options);
    }
    badRequest(msg = '请求参数错误', data, options) {
        return this.error(msg, data, exports.RESPONSE_CODES.BAD_REQUEST, options);
    }
    unauthorized(msg = '请先登录后再访问', data, options) {
        return this.error(msg, data, exports.RESPONSE_CODES.UNAUTHORIZED, options);
    }
    forbidden(msg = '您没有权限执行此操作', data, options) {
        return this.error(msg, data, exports.RESPONSE_CODES.FORBIDDEN, options);
    }
    notFound(msg = '请求的资源不存在', data, options) {
        return this.error(msg, data, exports.RESPONSE_CODES.NOT_FOUND, options);
    }
    custom(code, msg, data, options) {
        const response = {
            code,
            msg,
            data: data,
            timestamp: new Date().toISOString(),
            path: options?.path,
            executionTime: options?.executionTime ? `${options.executionTime}ms` : undefined,
            requestId: options?.requestId,
        };
        if (options?.logContext) {
            this.loggerService.log(`Custom Response: ${msg} - Code: ${code} - ${options.path || 'Unknown'}`, options.logContext);
        }
        return response;
    }
    isSuccess(response) {
        return response.code === exports.RESPONSE_CODES.SUCCESS;
    }
    isError(response) {
        return response.code !== exports.RESPONSE_CODES.SUCCESS;
    }
};
exports.UnifiedResponseService = UnifiedResponseService;
exports.UnifiedResponseService = UnifiedResponseService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [logger_service_1.LoggerService])
], UnifiedResponseService);
//# sourceMappingURL=unified-response.service.js.map