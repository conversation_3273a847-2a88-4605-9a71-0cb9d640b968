import { Injectable, OnModuleInit } from '@nestjs/common';
import { Logger } from 'winston';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Inject } from '@nestjs/common';

/**
 * Console重写服务
 * 用于在应用启动时自动重写console方法，将所有console输出重定向到Winston日志系统
 */
@Injectable()
export class ConsoleOverrideService implements OnModuleInit {
  private originalConsole: any;

  constructor(
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger
  ) {}

  onModuleInit() {
    this.overrideConsole();
  }

  /**
   * 重写console方法，将所有console输出重定向到Winston日志系统
   */
  private overrideConsole() {
    // 保存原始的console方法
    this.originalConsole = {
      log: console.log,
      error: console.error,
      warn: console.warn,
      info: console.info,
      debug: console.debug,
      trace: console.trace,
      assert: console.assert,
      dir: console.dir,
      table: console.table,
      group: console.group,
      groupCollapsed: console.groupCollapsed,
      groupEnd: console.groupEnd,
      time: console.time,
      timeEnd: console.timeEnd,
      count: console.count,
      clear: console.clear
    };

    // 重写console.log
    console.log = (...args: any[]) => {
      const message = this.formatMessage(args);
      this.logger.info(message, { context: 'Console' });
      this.callOriginalInDev('log', args);
    };

    // 重写console.error
    console.error = (...args: any[]) => {
      const message = this.formatMessage(args);
      this.logger.error(message, { context: 'Console' });
      this.callOriginalInDev('error', args);
    };

    // 重写console.warn
    console.warn = (...args: any[]) => {
      const message = this.formatMessage(args);
      this.logger.warn(message, { context: 'Console' });
      this.callOriginalInDev('warn', args);
    };

    // 重写console.info
    console.info = (...args: any[]) => {
      const message = this.formatMessage(args);
      this.logger.info(message, { context: 'Console' });
      this.callOriginalInDev('info', args);
    };

    // 重写console.debug
    console.debug = (...args: any[]) => {
      const message = this.formatMessage(args);
      this.logger.debug(message, { context: 'Console' });
      this.callOriginalInDev('debug', args);
    };

    // 重写console.trace
    console.trace = (...args: any[]) => {
      const message = this.formatMessage(args);
      const stack = new Error().stack;
      this.logger.debug(message, { context: 'Console', trace: stack });
      this.callOriginalInDev('trace', args);
    };

    // 重写console.assert
    console.assert = (condition: boolean, ...args: any[]) => {
      if (!condition) {
        const message = this.formatMessage(args);
        this.logger.error(`Assertion failed: ${message}`, { context: 'Console' });
      }
      this.callOriginalInDev('assert', [condition, ...args]);
    };

    // 重写console.dir
    console.dir = (obj: any, options?: any) => {
      const message = JSON.stringify(obj, null, 2);
      this.logger.info(`Dir: ${message}`, { context: 'Console' });
      this.callOriginalInDev('dir', [obj, options]);
    };

    // 重写console.table
    console.table = (data: any) => {
      const message = JSON.stringify(data, null, 2);
      this.logger.info(`Table: ${message}`, { context: 'Console' });
      this.callOriginalInDev('table', [data]);
    };

    // 重写console.group
    console.group = (...args: any[]) => {
      const message = this.formatMessage(args);
      this.logger.info(`Group: ${message}`, { context: 'Console' });
      this.callOriginalInDev('group', args);
    };

    // 重写console.groupCollapsed
    console.groupCollapsed = (...args: any[]) => {
      const message = this.formatMessage(args);
      this.logger.info(`GroupCollapsed: ${message}`, { context: 'Console' });
      this.callOriginalInDev('groupCollapsed', args);
    };

    // 重写console.groupEnd
    console.groupEnd = () => {
      this.logger.info('GroupEnd', { context: 'Console' });
      this.callOriginalInDev('groupEnd', []);
    };

    // 时间相关的方法需要特殊处理
    const timers = new Map<string, number>();
    
    console.time = (label: string = 'default') => {
      timers.set(label, Date.now());
      this.logger.info(`Timer started: ${label}`, { context: 'Console' });
      this.callOriginalInDev('time', [label]);
    };

    console.timeEnd = (label: string = 'default') => {
      const startTime = timers.get(label);
      if (startTime) {
        const duration = Date.now() - startTime;
        timers.delete(label);
        this.logger.info(`Timer ended: ${label} - ${duration}ms`, { context: 'Console' });
      }
      this.callOriginalInDev('timeEnd', [label]);
    };

    const counters = new Map<string, number>();
    
    console.count = (label: string = 'default') => {
      const count = (counters.get(label) || 0) + 1;
      counters.set(label, count);
      this.logger.info(`Count: ${label} - ${count}`, { context: 'Console' });
      this.callOriginalInDev('count', [label]);
    };

    console.clear = () => {
      this.logger.info('Console cleared', { context: 'Console' });
      this.callOriginalInDev('clear', []);
    };

    this.logger.info('Console methods have been overridden to use Winston logger', { context: 'ConsoleOverride' });
  }

  /**
   * 格式化消息
   */
  private formatMessage(args: any[]): string {
    return args.map(arg => {
      if (typeof arg === 'object' && arg !== null) {
        try {
          return JSON.stringify(arg, null, 2);
        } catch (error) {
          return String(arg);
        }
      }
      return String(arg);
    }).join(' ');
  }

  /**
   * 在开发环境下调用原始console方法
   */
  private callOriginalInDev(method: string, args: any[]) {
    if (process.env.NODE_ENV === 'dev' || process.env.NODE_ENV === 'development') {
      if (this.originalConsole[method]) {
        this.originalConsole[method](...args);
      }
    }
  }

  /**
   * 恢复原始console方法
   */
  restoreConsole() {
    if (this.originalConsole) {
      Object.keys(this.originalConsole).forEach(method => {
        console[method] = this.originalConsole[method];
      });
      this.logger.info('Console methods have been restored to original', { context: 'ConsoleOverride' });
    }
  }

  /**
   * 获取原始console方法
   */
  getOriginalConsole() {
    return this.originalConsole;
  }
}
