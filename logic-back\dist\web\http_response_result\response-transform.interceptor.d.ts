import { NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Observable } from 'rxjs';
import { HttpResponse } from './http-response.interface';
import { HttpResponseResultService } from './http_response_result.service';
export declare class ResponseTransformInterceptor<T> implements NestInterceptor<T, HttpResponse<T>> {
    private readonly responseService;
    constructor(responseService: HttpResponseResultService);
    intercept(context: ExecutionContext, next: CallHandler): Observable<HttpResponse<T>>;
}
