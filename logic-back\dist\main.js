"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const app_module_1 = require("./app.module");
const common_1 = require("@nestjs/common");
const swagger_service_1 = require("./util/swagger/swagger.service");
const utilFunction_1 = require("./util/utilFunction/utilFunction");
const response_transform_interceptor_1 = require("./web/http_response_result/response-transform.interceptor");
const logger_service_1 = require("./common/logger/logger.service");
const global_exception_filter_1 = require("./common/logger/global-exception.filter");
const nest_winston_1 = require("nest-winston");
const express = require("express");
async function bootstrap() {
    const app = await core_1.NestFactory.create(app_module_1.AppModule, {
        abortOnError: false,
        bufferLogs: true
    });
    const winstonLogger = app.get(WINSTON_MODULE_PROVIDER);
    overrideConsole(winstonLogger);
    const loggerService = app.get(logger_service_1.LoggerService);
    app.useLogger(app.get(nest_winston_1.WINSTON_MODULE_NEST_PROVIDER));
    app.useGlobalFilters(new global_exception_filter_1.GlobalExceptionFilter(loggerService));
    loggerService.logStartup('Application is starting...', {
        nodeVersion: process.version,
        platform: process.platform,
        environment: process.env.NODE_ENV || 'development',
        port: process.env.PORT || 8003
    });
    app.use(express.json({ limit: '50mb' }));
    app.use(express.urlencoded({ limit: '50mb', extended: true }));
    app.use((req, res, next) => {
        if (req.url.includes('/weixin/message') && req.method === 'POST') {
            let data = '';
            req.setEncoding('utf8');
            req.on('data', (chunk) => {
                data += chunk;
            });
            req.on('end', () => {
                req.body = data;
                loggerService.log(`[微信消息原始数据]: ${data}`, 'WeChat');
                next();
            });
        }
        else {
            next();
        }
    });
    app.use((req, res, next) => {
        if (req.url.includes('/weixin/message') && req.method === 'GET') {
            loggerService.log(`[微信URL验证]: ${req.url}`, 'WeChat');
        }
        next();
    });
    app.useGlobalPipes(new common_1.ValidationPipe({
        transform: true,
        whitelist: true,
        forbidNonWhitelisted: true,
    }));
    app.useGlobalInterceptors(new response_transform_interceptor_1.ResponseTransformInterceptor());
    if ((0, utilFunction_1.isDev)()) {
        const swaggerService = new swagger_service_1.SwaggerService();
        swaggerService.setup(app);
    }
    app.enableCors();
    const port = process.env.PORT ?? 8003;
    await app.listen(port);
    const url = await app.getUrl();
    loggerService.logStartup('Application started successfully', {
        url,
        port,
        timestamp: new Date().toISOString()
    });
}
bootstrap().catch((error) => {
    console.error('应用启动失败:', error);
    process.exit(1);
});
//# sourceMappingURL=main.js.map