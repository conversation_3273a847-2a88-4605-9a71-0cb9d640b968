"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IpLocationQueryService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const query_handler_interface_1 = require("../interfaces/query-handler.interface");
const ip_location_domain_service_1 = require("../../domain/services/ip-location-domain.service");
const risk_assessment_domain_service_1 = require("../../domain/services/risk-assessment-domain.service");
const location_comparison_service_1 = require("../../domain/services/location-comparison.service");
const geographic_location_vo_1 = require("../../domain/value-objects/geographic-location.vo");
const user_common_location_entity_1 = require("../../domain/entities/user-common-location.entity");
const ip_location_domain_exception_1 = require("../../domain/exceptions/ip-location-domain.exception");
let IpLocationQueryService = class IpLocationQueryService {
    userLocationRepository;
    ipLocationDomainService;
    riskAssessmentService;
    locationComparisonService;
    constructor(userLocationRepository, ipLocationDomainService, riskAssessmentService, locationComparisonService) {
        this.userLocationRepository = userLocationRepository;
        this.ipLocationDomainService = ipLocationDomainService;
        this.riskAssessmentService = riskAssessmentService;
        this.locationComparisonService = locationComparisonService;
    }
    async handleGetLocationByIp(query) {
        const startTime = Date.now();
        try {
            const validation = query.validate();
            if (!validation.isValid) {
                return (0, query_handler_interface_1.createFailureQueryResult)(validation.errors, '查询验证失败', Date.now() - startTime);
            }
            let fromCache = false;
            let location;
            try {
                location = await this.ipLocationDomainService.resolveLocation(query.ipAddress);
            }
            catch (error) {
                if (query.ipAddress.isLoopback && this.isDevelopmentEnvironment()) {
                    location = this.createDefaultLocationForDevelopment(query.ipAddress.value);
                }
                else {
                    throw error;
                }
            }
            const result = {
                ip: query.ipAddress.value,
                country: location.country,
                province: location.province,
                city: location.city,
                isp: location.isp,
                dataSource: location.dataSource,
                confidence: location.confidence,
                isHighQuality: location.isHighQuality,
                displayName: location.displayName
            };
            if (query.needsRiskAssessment) {
                const basicUserHistory = {
                    commonLocations: [],
                    totalLoginCount: 0,
                    riskLoginCount: 0,
                    accountAge: 0
                };
                const riskScore = this.riskAssessmentService.assessLoginRisk(location, basicUserHistory, query.ipAddress);
                result.riskAssessment = {
                    level: riskScore.level,
                    score: riskScore.score,
                    reason: riskScore.reason,
                    factors: riskScore.factors,
                    needVerification: riskScore.needsVerification,
                    recommendedActions: riskScore.recommendedVerificationMethods
                };
            }
            return (0, query_handler_interface_1.createSuccessQueryResult)(result, 'IP位置查询成功', Date.now() - startTime, fromCache, query.getCacheKey());
        }
        catch (error) {
            if (error instanceof ip_location_domain_exception_1.IpLocationDomainException) {
                return (0, query_handler_interface_1.createFailureQueryResult)([error.message], '领域业务错误', Date.now() - startTime);
            }
            return (0, query_handler_interface_1.createFailureQueryResult)(['IP位置查询时发生未知错误'], error.message, Date.now() - startTime);
        }
    }
    async handleGetUserLocationStats(query) {
        const startTime = Date.now();
        try {
            const validation = query.validate();
            if (!validation.isValid) {
                return (0, query_handler_interface_1.createFailureQueryResult)(validation.errors, '查询验证失败', Date.now() - startTime);
            }
            const queryBuilder = this.userLocationRepository.createQueryBuilder('location')
                .where('location.userId = :userId', { userId: query.userId })
                .andWhere('location.lastLoginAt >= :startDate', { startDate: query.getStartDate() })
                .andWhere('location.lastLoginAt <= :endDate', { endDate: query.getEndDate() });
            if (query.minLoginCount > 1) {
                queryBuilder.andWhere('location.loginCount >= :minLoginCount', {
                    minLoginCount: query.minLoginCount
                });
            }
            const locations = await queryBuilder.getMany();
            const result = {
                userId: query.userId,
                queryPeriod: {
                    days: query.days,
                    startDate: query.getStartDate().toISOString(),
                    endDate: query.getEndDate().toISOString()
                },
                summary: {
                    totalLocations: locations.length,
                    trustedLocations: locations.filter(l => l.isTrusted).length,
                    totalLogins: locations.reduce((sum, l) => sum + l.loginCount, 0),
                    riskLogins: locations.filter(l => l.trustScore < 50).reduce((sum, l) => sum + l.loginCount, 0),
                    uniqueProvinces: new Set(locations.map(l => l.province)).size,
                    uniqueCities: new Set(locations.map(l => l.city)).size
                },
                locations: locations.map(location => ({
                    country: location.country,
                    province: location.province,
                    city: location.city,
                    isp: location.isp || '未知',
                    loginCount: location.loginCount,
                    isTrusted: location.isTrusted,
                    trustScore: location.trustScore,
                    lastLoginAt: location.lastLoginAt?.toISOString() || '',
                    firstLoginAt: location.firstLoginAt?.toISOString() || ''
                }))
            };
            if (query.includeRiskAnalysis) {
                const userHistory = {
                    commonLocations: locations.map(l => ({
                        location: geographic_location_vo_1.GeographicLocation.create(l.country, l.province, l.city, l.isp || '未知'),
                        loginCount: l.loginCount,
                        isTrusted: l.isTrusted,
                        lastLoginAt: l.lastLoginAt || new Date(),
                        firstLoginAt: l.firstLoginAt || new Date()
                    })),
                    totalLoginCount: result.summary.totalLogins,
                    riskLoginCount: result.summary.riskLogins,
                    accountAge: 365
                };
                const userProfile = this.riskAssessmentService.calculateUserRiskProfile(userHistory);
                result.riskAnalysis = {
                    overallRiskLevel: userProfile.riskLevel,
                    riskFactors: userProfile.characteristics,
                    recommendations: userProfile.recommendations
                };
            }
            return (0, query_handler_interface_1.createSuccessQueryResult)(result, '用户位置统计查询成功', Date.now() - startTime, false, query.getCacheKey());
        }
        catch (error) {
            if (error instanceof ip_location_domain_exception_1.IpLocationDomainException) {
                return (0, query_handler_interface_1.createFailureQueryResult)([error.message], '领域业务错误', Date.now() - startTime);
            }
            return (0, query_handler_interface_1.createFailureQueryResult)(['用户位置统计查询时发生未知错误'], error.message, Date.now() - startTime);
        }
    }
    async handleAssessLoginRisk(query) {
        const startTime = Date.now();
        try {
            const validation = query.validate();
            if (!validation.isValid) {
                return (0, query_handler_interface_1.createFailureQueryResult)(validation.errors, '查询验证失败', Date.now() - startTime);
            }
            let fromCache = false;
            const currentLocation = await this.ipLocationDomainService.resolveLocation(query.ipAddress);
            const userLocations = await this.userLocationRepository.find({
                where: { userId: query.userId },
                order: { lastLoginAt: 'DESC' },
                take: 50
            });
            const userHistory = {
                commonLocations: userLocations.map(l => ({
                    location: geographic_location_vo_1.GeographicLocation.create(l.country, l.province, l.city, l.isp || '未知'),
                    loginCount: l.loginCount,
                    isTrusted: l.isTrusted,
                    lastLoginAt: l.lastLoginAt || new Date(),
                    firstLoginAt: l.firstLoginAt || new Date()
                })),
                totalLoginCount: userLocations.reduce((sum, l) => sum + l.loginCount, 0),
                riskLoginCount: userLocations.filter(l => l.trustScore < 50).reduce((sum, l) => sum + l.loginCount, 0),
                accountAge: 365
            };
            const riskScore = this.riskAssessmentService.assessLoginRisk(currentLocation, userHistory, query.ipAddress);
            const result = {
                riskAssessment: {
                    level: riskScore.level,
                    score: riskScore.score,
                    reason: riskScore.reason,
                    factors: riskScore.factors,
                    needVerification: riskScore.needsVerification,
                    recommendedActions: riskScore.recommendedVerificationMethods
                },
                location: {
                    country: currentLocation.country,
                    province: currentLocation.province,
                    city: currentLocation.city,
                    isp: currentLocation.isp,
                    displayName: currentLocation.displayName
                },
                userHistory: {
                    lastLoginLocation: userLocations.length > 0 ? userLocations[0].getDisplayName() : undefined,
                    commonLocationCount: userLocations.length,
                    isNewLocation: !userLocations.some(l => l.province === currentLocation.province && l.city === currentLocation.city)
                }
            };
            if (query.includeRecommendations) {
                const verificationNeeds = this.riskAssessmentService.analyzeVerificationNeeds(riskScore, userHistory);
                result.recommendations = {
                    verificationMethods: verificationNeeds.verificationMethods,
                    urgency: verificationNeeds.urgency,
                    reason: verificationNeeds.reason
                };
            }
            if (query.includeUserProfile) {
                const userProfile = this.riskAssessmentService.calculateUserRiskProfile(userHistory);
                result.userProfile = userProfile;
            }
            return (0, query_handler_interface_1.createSuccessQueryResult)(result, '登录风险评估完成', Date.now() - startTime, fromCache, query.getCacheKey());
        }
        catch (error) {
            if (error instanceof ip_location_domain_exception_1.IpLocationDomainException) {
                return (0, query_handler_interface_1.createFailureQueryResult)([error.message], '领域业务错误', Date.now() - startTime);
            }
            return (0, query_handler_interface_1.createFailureQueryResult)(['登录风险评估时发生未知错误'], error.message, Date.now() - startTime);
        }
    }
    isDevelopmentEnvironment() {
        return process.env.NODE_ENV === 'development' ||
            process.env.NODE_ENV === 'dev' ||
            !process.env.NODE_ENV;
    }
    createDefaultLocationForDevelopment(_ip) {
        return geographic_location_vo_1.GeographicLocation.create('中国', '广东省', '广州市', '本地开发环境');
    }
};
exports.IpLocationQueryService = IpLocationQueryService;
exports.IpLocationQueryService = IpLocationQueryService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(user_common_location_entity_1.UserCommonLocation)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        ip_location_domain_service_1.IpLocationDomainService,
        risk_assessment_domain_service_1.RiskAssessmentDomainService,
        location_comparison_service_1.LocationComparisonService])
], IpLocationQueryService);
//# sourceMappingURL=ip-location-query.service.js.map