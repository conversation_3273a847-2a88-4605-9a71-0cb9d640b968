@echo off
echo 🚀 启动内网穿透IP测试环境
echo.

echo 📋 启动步骤：
echo 1. 启动后端服务 (端口8601)
echo 2. 启动前端服务 (端口3000) 
echo 3. 启动ngrok穿透 (前端)
echo 4. 通过ngrok地址访问测试页面
echo.

echo ⚠️  请确保已安装ngrok: npm install -g ngrok
echo.

pause

echo 🖥️  启动后端服务...
start "后端服务" cmd /k "cd logic-back && npm run start:dev"

timeout /t 3

echo 🌐 启动前端服务...
start "前端服务" cmd /k "cd logicleapweb && npm run dev"

timeout /t 5

echo 🚇 启动ngrok穿透...
start "ngrok穿透" cmd /k "ngrok http 3000"

echo.
echo ✅ 所有服务已启动！
echo.
echo 📋 接下来的步骤：
echo 1. 等待所有服务启动完成
echo 2. 在ngrok窗口中复制公网地址 (如: https://abc123.ngrok.io)
echo 3. 访问: https://abc123.ngrok.io/test-ip
echo 4. 点击测试按钮观察IP获取情况
echo.
echo 🔍 观察要点：
echo - 真实公网IP vs 后端获取IP是否一致
echo - 地理位置是否正确解析
echo - 日志中的IP传递链路
echo.

pause
