"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HttpResponseResultService = void 0;
const common_1 = require("@nestjs/common");
const http_response_interface_1 = require("./http-response.interface");
const logger_service_1 = require("../../common/logger/logger.service");
let HttpResponseResultService = class HttpResponseResultService {
    loggerService;
    constructor(loggerService) {
        this.loggerService = loggerService;
    }
    success(data, msg = '操作成功', code = http_response_interface_1.SUCCESS_CODE, logOptions) {
        const response = {
            code,
            msg,
            data: data,
        };
        this.logSuccess(msg, data, code, logOptions);
        return response;
    }
    error(msg = '系统错误', data, code = http_response_interface_1.ERROR_CODE, logOptions) {
        const response = {
            code,
            msg,
            data: data,
        };
        this.logError(msg, data, code, logOptions);
        return response;
    }
    custom(code, msg, data, logOptions) {
        const response = {
            code,
            msg,
            data: data,
        };
        if (code >= 200 && code < 300) {
            this.logSuccess(msg, data, code, logOptions);
        }
        else {
            this.logError(msg, data, code, logOptions);
        }
        return response;
    }
    logSuccess(msg, data, code, logOptions) {
        if (!this.shouldLog(logOptions))
            return;
        const context = logOptions?.context || 'HttpResponseResultService';
        const logMessage = this.buildLogMessage('SUCCESS', msg, code, logOptions);
        this.loggerService?.log(logMessage, context);
        if (logOptions?.enableLog) {
            this.loggerService?.debug(JSON.stringify({
                type: 'Success Details',
                message: msg,
                code,
                data,
                path: logOptions?.path,
                userId: logOptions?.userId,
                requestId: logOptions?.requestId,
                executionTime: logOptions?.executionTime,
                timestamp: new Date().toISOString()
            }), context);
        }
    }
    logError(msg, data, code, logOptions) {
        if (!this.shouldLog(logOptions))
            return;
        const context = logOptions?.context || 'HttpResponseResultService';
        const logMessage = this.buildLogMessage('ERROR', msg, code, logOptions);
        this.loggerService?.error(logMessage, undefined, context);
        this.loggerService?.error(JSON.stringify({
            type: 'Error Details',
            message: msg,
            code,
            data,
            path: logOptions?.path,
            userId: logOptions?.userId,
            requestId: logOptions?.requestId,
            executionTime: logOptions?.executionTime,
            timestamp: new Date().toISOString()
        }), undefined, context);
    }
    buildLogMessage(level, msg, code, logOptions) {
        const parts = [
            `[${level}]`,
            msg,
            `Code: ${code}`,
            logOptions?.path ? `Path: ${logOptions.path}` : '',
            logOptions?.userId ? `User: ${logOptions.userId}` : '',
            logOptions?.executionTime ? `Time: ${logOptions.executionTime}ms` : '',
            logOptions?.requestId ? `RequestId: ${logOptions.requestId}` : ''
        ].filter(Boolean);
        return parts.join(' - ');
    }
    shouldLog(logOptions) {
        if (!this.loggerService)
            return false;
        if (logOptions?.enableLog === false)
            return false;
        return true;
    }
};
exports.HttpResponseResultService = HttpResponseResultService;
exports.HttpResponseResultService = HttpResponseResultService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Optional)()),
    __metadata("design:paramtypes", [logger_service_1.LoggerService])
], HttpResponseResultService);
//# sourceMappingURL=http_response_result.service.js.map