import { Module } from '@nestjs/common';
import { HttpExceptionFilter } from './http-exception.filter';
import { HttpResponseResultModule } from '../http_response_result/http_response_result.module';
import { LoggerModule } from '../../common/logger/logger.module';
import { APP_FILTER } from '@nestjs/core';

@Module({
  imports: [
    HttpResponseResultModule, // 导入响应结果模块
    LoggerModule, // 导入日志模块
  ],
  providers: [
    {
      provide: APP_FILTER,
      useClass: HttpExceptionFilter,
    }
  ],
  exports: [],
})
export class HttpExceptionFilterModule {}