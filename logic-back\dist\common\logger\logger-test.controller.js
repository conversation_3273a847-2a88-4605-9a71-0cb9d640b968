"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LoggerTestController = void 0;
const common_1 = require("@nestjs/common");
const logger_service_1 = require("./logger.service");
const logger_test_1 = require("./logger.test");
const swagger_1 = require("@nestjs/swagger");
let LoggerTestController = class LoggerTestController {
    loggerService;
    loggerTest;
    constructor(loggerService) {
        this.loggerService = loggerService;
        this.loggerTest = new logger_test_1.LoggerTest(this.loggerService);
    }
    testAllLogs() {
        try {
            this.loggerTest.testAllLogTypes();
            return {
                success: true,
                message: '所有日志类型测试完成，请检查日志文件',
                logFiles: [
                    'logs/application-YYYY-MM-DD.log',
                    'logs/error-YYYY-MM-DD.log',
                    'logs/http-YYYY-MM-DD.log',
                    'logs/database-YYYY-MM-DD.log',
                    'logs/console-YYYY-MM-DD.log'
                ]
            };
        }
        catch (error) {
            this.loggerService.error('日志测试失败', error.stack, 'LoggerTestController');
            return {
                success: false,
                message: '日志测试失败',
                error: error.message
            };
        }
    }
    async testAsyncLogs() {
        try {
            await this.loggerTest.testAsyncLogs();
            return {
                success: true,
                message: '异步日志测试完成'
            };
        }
        catch (error) {
            this.loggerService.error('异步日志测试失败', error.stack, 'LoggerTestController');
            return {
                success: false,
                message: '异步日志测试失败',
                error: error.message
            };
        }
    }
    testLogPerformance() {
        try {
            this.loggerTest.testLogPerformance();
            return {
                success: true,
                message: '日志性能测试完成，请查看日志文件中的性能数据'
            };
        }
        catch (error) {
            this.loggerService.error('日志性能测试失败', error.stack, 'LoggerTestController');
            return {
                success: false,
                message: '日志性能测试失败',
                error: error.message
            };
        }
    }
    testCustomLogs(body) {
        try {
            const { message, level, context = 'CustomTest' } = body;
            switch (level.toLowerCase()) {
                case 'log':
                case 'info':
                    console.log(`[自定义测试] ${message}`);
                    this.loggerService.log(message, context);
                    break;
                case 'error':
                    console.error(`[自定义测试] ${message}`);
                    this.loggerService.error(message, undefined, context);
                    break;
                case 'warn':
                    console.warn(`[自定义测试] ${message}`);
                    this.loggerService.warn(message, context);
                    break;
                case 'debug':
                    console.debug(`[自定义测试] ${message}`);
                    this.loggerService.debug(message, context);
                    break;
                default:
                    console.log(`[自定义测试] ${message}`);
                    this.loggerService.log(message, context);
            }
            return {
                success: true,
                message: `自定义${level}日志已记录`,
                data: { message, level, context }
            };
        }
        catch (error) {
            this.loggerService.error('自定义日志测试失败', error.stack, 'LoggerTestController');
            return {
                success: false,
                message: '自定义日志测试失败',
                error: error.message
            };
        }
    }
    testConsoleMethods() {
        try {
            console.log('=== Console方法测试开始 ===');
            console.log('console.log 测试');
            console.info('console.info 测试');
            console.warn('console.warn 测试');
            console.error('console.error 测试');
            console.debug('console.debug 测试');
            console.log('对象输出:', { name: '测试', value: 123 });
            console.log('数组输出:', [1, 2, 3, '测试']);
            console.dir({ nested: { object: { test: true } } });
            console.table([
                { id: 1, name: '张三', age: 25 },
                { id: 2, name: '李四', age: 30 }
            ]);
            console.group('测试分组');
            console.log('分组内容1');
            console.log('分组内容2');
            console.groupEnd();
            console.time('测试计时');
            for (let i = 0; i < 100000; i++) {
            }
            console.timeEnd('测试计时');
            for (let i = 0; i < 3; i++) {
                console.count('测试计数');
            }
            console.assert(true, '这个断言不应该显示');
            console.assert(false, '这个断言应该显示');
            console.trace('堆栈跟踪测试');
            console.log('=== Console方法测试结束 ===');
            return {
                success: true,
                message: 'Console方法测试完成，所有输出都应该被保存到日志文件中'
            };
        }
        catch (error) {
            this.loggerService.error('Console方法测试失败', error.stack, 'LoggerTestController');
            return {
                success: false,
                message: 'Console方法测试失败',
                error: error.message
            };
        }
    }
    simulateErrors() {
        try {
            console.log('=== 错误模拟测试开始 ===');
            try {
                throw new Error('这是一个模拟的JavaScript错误');
            }
            catch (error) {
                console.error('捕获到JavaScript错误:', error);
                this.loggerService.error('JavaScript错误', error.stack, 'ErrorSimulation');
            }
            try {
                const obj = null;
                obj.someProperty;
            }
            catch (error) {
                console.error('捕获到类型错误:', error);
                this.loggerService.error('类型错误', error.stack, 'ErrorSimulation');
            }
            try {
                undefinedVariable;
            }
            catch (error) {
                console.error('捕获到引用错误:', error);
                this.loggerService.error('引用错误', error.stack, 'ErrorSimulation');
            }
            Promise.reject(new Error('这是一个Promise拒绝错误')).catch(error => {
                console.error('捕获到Promise拒绝:', error);
                this.loggerService.error('Promise拒绝', error.stack, 'ErrorSimulation');
            });
            console.log('=== 错误模拟测试结束 ===');
            return {
                success: true,
                message: '错误模拟测试完成，所有错误都应该被记录到错误日志文件中'
            };
        }
        catch (error) {
            this.loggerService.error('错误模拟测试失败', error.stack, 'LoggerTestController');
            return {
                success: false,
                message: '错误模拟测试失败',
                error: error.message
            };
        }
    }
};
exports.LoggerTestController = LoggerTestController;
__decorate([
    (0, common_1.Get)('all'),
    (0, swagger_1.ApiOperation)({ summary: '测试所有类型的日志输出' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '测试完成' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], LoggerTestController.prototype, "testAllLogs", null);
__decorate([
    (0, common_1.Get)('async'),
    (0, swagger_1.ApiOperation)({ summary: '测试异步日志输出' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '异步测试完成' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], LoggerTestController.prototype, "testAsyncLogs", null);
__decorate([
    (0, common_1.Get)('performance'),
    (0, swagger_1.ApiOperation)({ summary: '测试日志输出性能' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '性能测试完成' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], LoggerTestController.prototype, "testLogPerformance", null);
__decorate([
    (0, common_1.Post)('custom'),
    (0, swagger_1.ApiOperation)({ summary: '自定义日志测试' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '自定义测试完成' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], LoggerTestController.prototype, "testCustomLogs", null);
__decorate([
    (0, common_1.Get)('console-methods'),
    (0, swagger_1.ApiOperation)({ summary: '测试所有console方法' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Console方法测试完成' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], LoggerTestController.prototype, "testConsoleMethods", null);
__decorate([
    (0, common_1.Get)('error-simulation'),
    (0, swagger_1.ApiOperation)({ summary: '模拟各种错误情况' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '错误模拟完成' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], LoggerTestController.prototype, "simulateErrors", null);
exports.LoggerTestController = LoggerTestController = __decorate([
    (0, swagger_1.ApiTags)('日志测试'),
    (0, common_1.Controller)('api/v1/logger-test'),
    __metadata("design:paramtypes", [logger_service_1.LoggerService])
], LoggerTestController);
//# sourceMappingURL=logger-test.controller.js.map