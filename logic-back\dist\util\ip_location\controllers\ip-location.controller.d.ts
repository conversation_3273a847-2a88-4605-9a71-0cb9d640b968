import { Request } from 'express';
import { IpLocationFacadeService } from '../application/services/ip-location-facade.service';
import { IpQueryRequestDto } from '../application/dto/requests/ip-query.request.dto';
import { RiskCheckRequestDto } from '../application/dto/requests/risk-check.request.dto';
import { TrustLocationRequestDto } from '../application/dto/requests/trust-location.request.dto';
import { LocationInfoResponseDto } from '../application/dto/responses/location-info.response.dto';
import { RiskAssessmentResponseDto } from '../application/dto/responses/risk-assessment.response.dto';
import { LocationStatsResponseDto } from '../application/dto/responses/location-stats.response.dto';
export declare class IpLocationController {
    private readonly ipLocationFacadeService;
    constructor(ipLocationFacadeService: IpLocationFacadeService);
    queryIpLocationV2(query: IpQueryRequestDto): Promise<{
        success: boolean;
        data: LocationInfoResponseDto | null;
        message: any;
        meta: {
            executionTime: any;
            fromCache: any;
        };
    }>;
    checkLoginRisk(request: RiskCheckRequestDto): Promise<RiskAssessmentResponseDto | null>;
    getUserLocationStats(userId: number, days?: number): Promise<LocationStatsResponseDto | null>;
    setTrustedLocation(userId: number, request: TrustLocationRequestDto): Promise<{
        message: string;
        data: {
            userId: number;
            province: string;
            city: string;
        } | null;
    }>;
    getCurrentIpLocation(request: Request): Promise<LocationInfoResponseDto | null>;
    healthCheck(): Promise<{
        status: string;
        timestamp: string;
        service: string;
    }>;
    private extractClientIP;
    private cleanAndValidateIP;
    private isValidIP;
    private getIpSourceName;
}
