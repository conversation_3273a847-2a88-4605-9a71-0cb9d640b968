import { Request } from 'express';
import { IpLocationFacadeService } from '../application/services/ip-location-facade.service';
import { IpQueryRequestDto } from '../application/dto/requests/ip-query.request.dto';
import { RiskCheckRequestDto } from '../application/dto/requests/risk-check.request.dto';
import { TrustLocationRequestDto } from '../application/dto/requests/trust-location.request.dto';
import { LocationInfoResponseDto } from '../application/dto/responses/location-info.response.dto';
import { RiskAssessmentResponseDto } from '../application/dto/responses/risk-assessment.response.dto';
import { LocationStatsResponseDto } from '../application/dto/responses/location-stats.response.dto';
import { HttpResponseResultService } from '../../../web/http_response_result/http_response_result.service';
export declare class IpLocationController {
    private readonly ipLocationFacadeService;
    private readonly responseService;
    constructor(ipLocationFacadeService: IpLocationFacadeService, responseService: HttpResponseResultService);
    queryIpLocationV2(query: IpQueryRequestDto, request: Request): Promise<import("../../../web/http_response_result/http-response.interface").HttpResponse<{
        ip: string;
        includeRisk: boolean | undefined;
    }> | import("../../../web/http_response_result/http-response.interface").HttpResponse<{
        meta: {
            executionTime: any;
            fromCache: any;
        };
        ip?: string | undefined;
        country?: string | undefined;
        province?: string | undefined;
        city?: string | undefined;
        isp?: string | undefined;
        dataSource?: string | undefined;
        confidence?: number | undefined;
        isHighQuality?: boolean | undefined;
        displayName?: string | undefined;
        risk?: {
            level: string;
            score: number;
            reason: string;
            needVerification: boolean;
        };
    }>>;
    checkLoginRisk(request: RiskCheckRequestDto): Promise<RiskAssessmentResponseDto | null>;
    getUserLocationStats(userId: number, days?: number): Promise<LocationStatsResponseDto | null>;
    setTrustedLocation(userId: number, request: TrustLocationRequestDto): Promise<{
        message: string;
        data: {
            userId: number;
            province: string;
            city: string;
        } | null;
    }>;
    getCurrentIpLocation(request: Request): Promise<LocationInfoResponseDto | null>;
    healthCheck(): Promise<{
        status: string;
        timestamp: string;
        service: string;
    }>;
    private extractClientIP;
    private cleanAndValidateIP;
    private isValidIP;
    private getIpSourceName;
    private buildLogOptions;
}
