import { ExceptionFilter, Catch, ArgumentsHost, HttpException, HttpStatus } from '@nestjs/common';
import { Request, Response } from 'express';
import { HttpResponseResultService, LogOptions } from '../http_response_result/http_response_result.service';
import { LoggerService } from '../../common/logger/logger.service';

/**
 * 全局HTTP异常过滤器
 * 用于统一处理异常响应格式，并自动记录异常日志
 */
@Catch(HttpException)
export class HttpExceptionFilter implements ExceptionFilter {
  constructor(
    private readonly httpResponseResultService: HttpResponseResultService,
    private readonly loggerService: LoggerService
  ) {}
  
  catch(exception: HttpException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();
    const status = exception.getStatus();

    // 获取异常响应
    const exceptionResponse = exception.getResponse();
    
    // 默认错误消息
    let errorMessage = '服务器错误';
    let errorCode = status;
    let errorData = null;

    // 解析异常响应
    if (typeof exceptionResponse === 'object' && exceptionResponse !== null) {
      const exceptionObj = exceptionResponse as any;
      
      // 如果存在自定义消息、代码或数据，则使用它们
      if (exceptionObj.msg) {
        errorMessage = exceptionObj.msg;
      } else if (exceptionObj.message) {
        errorMessage = exceptionObj.message;
      }
      
      if (exceptionObj.code) {
        errorCode = exceptionObj.code;
      }
      
      if (exceptionObj.data !== undefined) {
        errorData = exceptionObj.data;
      }
    }

    // 根据不同状态码设置不同的默认消息
    if (errorMessage === '服务器错误') {
      switch (status) {
        case HttpStatus.UNAUTHORIZED:
          errorMessage = '请先登录后再访问';
          break;
        case HttpStatus.FORBIDDEN:
          errorMessage = '您没有权限执行此操作';
          break;
        case HttpStatus.NOT_FOUND:
          errorMessage = '请求的资源不存在';
          break;
        case HttpStatus.BAD_REQUEST:
          errorMessage = '请求参数错误';
          break;
        case HttpStatus.INTERNAL_SERVER_ERROR:
          errorMessage = '服务器内部错误';
          break;
        default:
          errorMessage = `请求错误 (${status})`;
      }
    }

    // 构建日志选项
    const logOptions: LogOptions = {
      path: request.url,
      requestId: request.headers['x-request-id'] as string,
      context: 'HttpExceptionFilter',
      enableLog: true
    };

    // 使用HttpResponseResultService构建统一响应格式（自动记录日志）
    const responseBody = this.httpResponseResultService.error(errorMessage, errorData, errorCode, logOptions);

    // 额外记录异常详情
    this.logExceptionDetails(exception, request, status, errorMessage);

    // 返回响应
    response
      .status(status)
      .json(responseBody);
  }

  /**
   * 记录异常详情
   * @param exception 异常对象
   * @param request 请求对象
   * @param status HTTP状态码
   * @param errorMessage 错误消息
   */
  private logExceptionDetails(
    exception: HttpException,
    request: Request,
    status: number,
    errorMessage: string
  ): void {
    // 记录异常基本信息
    this.loggerService.error(
      `HTTP Exception: ${request.method} ${request.url} - ${errorMessage} (${status})`,
      exception.stack,
      'HttpExceptionFilter'
    );

    // 记录详细的异常信息
    this.loggerService.error(JSON.stringify({
      type: 'Exception Details',
      url: request.url,
      method: request.method,
      statusCode: status,
      message: errorMessage,
      exceptionName: exception.name,
      userAgent: request.get('User-Agent'),
      ip: this.extractClientIP(request),
      requestId: request.headers['x-request-id'],
      timestamp: new Date().toISOString(),
      headers: {
        'content-type': request.get('Content-Type'),
        'authorization': request.get('Authorization') ? '[REDACTED]' : undefined,
        'x-forwarded-for': request.get('X-Forwarded-For'),
        'x-real-ip': request.get('X-Real-IP')
      },
      body: request.body ? this.sanitizeRequestBody(request.body) : undefined,
      query: request.query,
      params: request.params
    }), undefined, 'HttpExceptionFilter');
  }

  /**
   * 提取客户端IP地址
   * @param request 请求对象
   */
  private extractClientIP(request: Request): string {
    const forwarded = request.headers['x-forwarded-for'] as string;
    const realIp = request.headers['x-real-ip'] as string;
    const clientIp = (request.socket as any)?.remoteAddress;

    if (forwarded) return forwarded.split(',')[0].trim();
    if (realIp) return realIp;
    return clientIp?.replace('::ffff:', '') || '127.0.0.1';
  }

  /**
   * 清理请求体中的敏感信息
   * @param body 请求体
   */
  private sanitizeRequestBody(body: any): any {
    if (!body || typeof body !== 'object') return body;

    const sensitiveFields = ['password', 'token', 'secret', 'key', 'authorization'];
    const sanitized = { ...body };

    for (const field of sensitiveFields) {
      if (sanitized[field]) {
        sanitized[field] = '[REDACTED]';
      }
    }

    return sanitized;
  }
}