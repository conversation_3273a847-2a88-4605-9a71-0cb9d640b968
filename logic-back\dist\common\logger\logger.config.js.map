{"version": 3, "file": "logger.config.js", "sourceRoot": "", "sources": ["../../../src/common/logger/logger.config.ts"], "names": [], "mappings": ";;AA6DA,gDAkHC;AAKD,0CA4LC;AAhXD,mCAAmC;AACnC,6DAA6D;AAE7D,6BAA6B;AAC7B,yBAAyB;AAGzB,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,MAAM,CAAC,CAAC;AACjD,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC;IAC5B,EAAE,CAAC,SAAS,CAAC,OAAO,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;AAC7C,CAAC;AAGD,MAAM,SAAS,GAAG,OAAO,CAAC,MAAM,CAAC,OAAO,CACtC,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC;IACvB,MAAM,EAAE,yBAAyB;CAClC,CAAC,EACF,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,EACtC,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE,EACrB,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,IAAI,EAAE,EAAE,EAAE;IAC/E,IAAI,UAAU,GAAG,GAAG,SAAS,KAAK,KAAK,CAAC,WAAW,EAAE,GAAG,CAAC;IAEzD,IAAI,OAAO,EAAE,CAAC;QACZ,UAAU,IAAI,KAAK,OAAO,GAAG,CAAC;IAChC,CAAC;IAED,UAAU,IAAI,IAAI,OAAO,EAAE,CAAC;IAG5B,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACjC,UAAU,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC;IAC3C,CAAC;IAGD,IAAI,KAAK,EAAE,CAAC;QACV,UAAU,IAAI,KAAK,KAAK,EAAE,CAAC;IAC7B,CAAC;IAED,OAAO,UAAU,CAAC;AACpB,CAAC,CAAC,CACH,CAAC;AAGF,MAAM,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,OAAO,CAC1C,OAAO,CAAC,MAAM,CAAC,QAAQ,EAAE,EACzB,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC;IACvB,MAAM,EAAE,cAAc;CACvB,CAAC,EACF,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE;IAC/D,IAAI,UAAU,GAAG,GAAG,SAAS,IAAI,KAAK,EAAE,CAAC;IAEzC,IAAI,OAAO,EAAE,CAAC;QACZ,UAAU,IAAI,KAAK,OAAO,GAAG,CAAC;IAChC,CAAC;IAED,UAAU,IAAI,IAAI,OAAO,EAAE,CAAC;IAE5B,OAAO,UAAU,CAAC;AACpB,CAAC,CAAC,CACH,CAAC;AAEF,SAAgB,kBAAkB;IAChC,MAAM,aAAa,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,KAAK,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,CAAC;IAE/F,MAAM,UAAU,GAAwB,EAAE,CAAC;IAG3C,IAAI,aAAa,EAAE,CAAC;QAClB,UAAU,CAAC,IAAI,CACb,IAAI,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC;YAC7B,MAAM,EAAE,aAAa;YACrB,KAAK,EAAE,OAAO;SACf,CAAC,CACH,CAAC;IACJ,CAAC;IAGD,UAAU,CAAC,IAAI,CACb,IAAI,eAAe,CAAC;QAClB,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,wBAAwB,CAAC;QACtD,WAAW,EAAE,YAAY;QACzB,aAAa,EAAE,IAAI;QACnB,OAAO,EAAE,KAAK;QACd,QAAQ,EAAE,KAAK;QACf,MAAM,EAAE,SAAS;QACjB,KAAK,EAAE,OAAO;KACf,CAAC,CACH,CAAC;IAGF,UAAU,CAAC,IAAI,CACb,IAAI,eAAe,CAAC;QAClB,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,kBAAkB,CAAC;QAChD,WAAW,EAAE,YAAY;QACzB,aAAa,EAAE,IAAI;QACnB,OAAO,EAAE,KAAK;QACd,QAAQ,EAAE,KAAK;QACf,MAAM,EAAE,SAAS;QACjB,KAAK,EAAE,OAAO;KACf,CAAC,CACH,CAAC;IAGF,UAAU,CAAC,IAAI,CACb,IAAI,eAAe,CAAC;QAClB,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,iBAAiB,CAAC;QAC/C,WAAW,EAAE,YAAY;QACzB,aAAa,EAAE,IAAI;QACnB,OAAO,EAAE,KAAK;QACd,QAAQ,EAAE,KAAK;QACf,MAAM,EAAE,SAAS;QACjB,KAAK,EAAE,MAAM;KACd,CAAC,CACH,CAAC;IAGF,UAAU,CAAC,IAAI,CACb,IAAI,eAAe,CAAC;QAClB,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,qBAAqB,CAAC;QACnD,WAAW,EAAE,YAAY;QACzB,aAAa,EAAE,IAAI;QACnB,OAAO,EAAE,KAAK;QACd,QAAQ,EAAE,KAAK;QACf,MAAM,EAAE,SAAS;QACjB,KAAK,EAAE,OAAO;KACf,CAAC,CACH,CAAC;IAGF,UAAU,CAAC,IAAI,CACb,IAAI,eAAe,CAAC;QAClB,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,oBAAoB,CAAC;QAClD,WAAW,EAAE,YAAY;QACzB,aAAa,EAAE,IAAI;QACnB,OAAO,EAAE,KAAK;QACd,QAAQ,EAAE,KAAK;QACf,MAAM,EAAE,SAAS;QACjB,KAAK,EAAE,OAAO;KACf,CAAC,CACH,CAAC;IAEF,OAAO;QACL,UAAU;QACV,MAAM,EAAE,SAAS;QACjB,WAAW,EAAE;YACX,OAAO,EAAE,YAAY;YACrB,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI,OAAO;YACnD,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAI,aAAa;YAClD,GAAG,EAAE,OAAO,CAAC,GAAG;YAChB,QAAQ,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE;SACnC;QACD,WAAW,EAAE,KAAK;QAElB,iBAAiB,EAAE;YACjB,IAAI,eAAe,CAAC;gBAClB,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,uBAAuB,CAAC;gBACrD,WAAW,EAAE,YAAY;gBACzB,aAAa,EAAE,IAAI;gBACnB,OAAO,EAAE,KAAK;gBACd,QAAQ,EAAE,KAAK;gBACf,MAAM,EAAE,SAAS;aAClB,CAAC;SACH;QAED,iBAAiB,EAAE;YACjB,IAAI,eAAe,CAAC;gBAClB,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,uBAAuB,CAAC;gBACrD,WAAW,EAAE,YAAY;gBACzB,aAAa,EAAE,IAAI;gBACnB,OAAO,EAAE,KAAK;gBACd,QAAQ,EAAE,KAAK;gBACf,MAAM,EAAE,SAAS;aAClB,CAAC;SACH;KACF,CAAC;AACJ,CAAC;AAKD,SAAgB,eAAe,CAAC,MAAsB;IAEpD,MAAM,eAAe,GAAG;QACtB,GAAG,EAAE,OAAO,CAAC,GAAG;QAChB,KAAK,EAAE,OAAO,CAAC,KAAK;QACpB,IAAI,EAAE,OAAO,CAAC,IAAI;QAClB,IAAI,EAAE,OAAO,CAAC,IAAI;QAClB,KAAK,EAAE,OAAO,CAAC,KAAK;QACpB,KAAK,EAAE,OAAO,CAAC,KAAK;QACpB,MAAM,EAAE,OAAO,CAAC,MAAM;QACtB,GAAG,EAAE,OAAO,CAAC,GAAG;QAChB,MAAM,EAAE,OAAO,CAAC,MAAM;QACtB,KAAK,EAAE,OAAO,CAAC,KAAK;QACpB,cAAc,EAAE,OAAO,CAAC,cAAc;QACtC,QAAQ,EAAE,OAAO,CAAC,QAAQ;QAC1B,KAAK,EAAE,OAAO,CAAC,KAAK;QACpB,IAAI,EAAE,OAAO,CAAC,IAAI;QAClB,OAAO,EAAE,OAAO,CAAC,OAAO;QACxB,KAAK,EAAE,OAAO,CAAC,KAAK;QACpB,KAAK,EAAE,OAAO,CAAC,KAAK;KACrB,CAAC;IAGF,OAAO,CAAC,GAAG,GAAG,CAAC,GAAG,IAAW,EAAE,EAAE;QAC/B,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAC7B,OAAO,GAAG,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CACrE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACZ,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC,CAAC;QAE7C,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,KAAK,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,EAAE,CAAC;YAC7E,eAAe,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC;QAC/B,CAAC;IACH,CAAC,CAAC;IAGF,OAAO,CAAC,KAAK,GAAG,CAAC,GAAG,IAAW,EAAE,EAAE;QACjC,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAC7B,OAAO,GAAG,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CACrE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACZ,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC,CAAC;QAC9C,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,KAAK,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,EAAE,CAAC;YAC7E,eAAe,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,CAAC;QACjC,CAAC;IACH,CAAC,CAAC;IAGF,OAAO,CAAC,IAAI,GAAG,CAAC,GAAG,IAAW,EAAE,EAAE;QAChC,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAC7B,OAAO,GAAG,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CACrE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACZ,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC,CAAC;QAC7C,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,KAAK,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,EAAE,CAAC;YAC7E,eAAe,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;QAChC,CAAC;IACH,CAAC,CAAC;IAGF,OAAO,CAAC,IAAI,GAAG,CAAC,GAAG,IAAW,EAAE,EAAE;QAChC,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAC7B,OAAO,GAAG,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CACrE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACZ,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC,CAAC;QAC7C,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,KAAK,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,EAAE,CAAC;YAC7E,eAAe,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;QAChC,CAAC;IACH,CAAC,CAAC;IAGF,OAAO,CAAC,KAAK,GAAG,CAAC,GAAG,IAAW,EAAE,EAAE;QACjC,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAC7B,OAAO,GAAG,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CACrE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACZ,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC,CAAC;QAC9C,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,KAAK,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,EAAE,CAAC;YAC7E,eAAe,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,CAAC;QACjC,CAAC;IACH,CAAC,CAAC;IAGF,OAAO,CAAC,KAAK,GAAG,CAAC,GAAG,IAAW,EAAE,EAAE;QACjC,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAC7B,OAAO,GAAG,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CACrE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACZ,MAAM,KAAK,GAAG,IAAI,KAAK,EAAE,CAAC,KAAK,CAAC;QAChC,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;QAC5D,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,KAAK,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,EAAE,CAAC;YAC7E,eAAe,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,CAAC;QACjC,CAAC;IACH,CAAC,CAAC;IAGF,OAAO,CAAC,MAAM,GAAG,CAAC,SAAkB,EAAE,GAAG,IAAW,EAAE,EAAE;QACtD,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAC7B,OAAO,GAAG,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CACrE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACZ,MAAM,CAAC,KAAK,CAAC,qBAAqB,OAAO,EAAE,EAAE,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC,CAAC;QACvE,CAAC;QACD,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,KAAK,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,EAAE,CAAC;YAC7E,eAAe,CAAC,MAAM,CAAC,SAAS,EAAE,GAAG,IAAI,CAAC,CAAC;QAC7C,CAAC;IACH,CAAC,CAAC;IAGF,OAAO,CAAC,GAAG,GAAG,CAAC,GAAQ,EAAE,OAAa,EAAE,EAAE;QACxC,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;QAC7C,MAAM,CAAC,IAAI,CAAC,QAAQ,OAAO,EAAE,EAAE,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC,CAAC;QACvD,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,KAAK,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,EAAE,CAAC;YAC7E,eAAe,CAAC,GAAG,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;QACpC,CAAC;IACH,CAAC,CAAC;IAGF,OAAO,CAAC,KAAK,GAAG,CAAC,IAAS,EAAE,EAAE;QAC5B,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;QAC9C,MAAM,CAAC,IAAI,CAAC,UAAU,OAAO,EAAE,EAAE,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC,CAAC;QACzD,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,KAAK,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,EAAE,CAAC;YAC7E,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC9B,CAAC;IACH,CAAC,CAAC;IAGF,OAAO,CAAC,KAAK,GAAG,CAAC,GAAG,IAAW,EAAE,EAAE;QACjC,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC/B,MAAM,CAAC,IAAI,CAAC,UAAU,OAAO,EAAE,EAAE,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC,CAAC;QACzD,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,KAAK,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,EAAE,CAAC;YAC7E,eAAe,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,CAAC;QACjC,CAAC;IACH,CAAC,CAAC;IAEF,OAAO,CAAC,cAAc,GAAG,CAAC,GAAG,IAAW,EAAE,EAAE;QAC1C,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC/B,MAAM,CAAC,IAAI,CAAC,mBAAmB,OAAO,EAAE,EAAE,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC,CAAC;QAClE,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,KAAK,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,EAAE,CAAC;YAC7E,eAAe,CAAC,cAAc,CAAC,GAAG,IAAI,CAAC,CAAC;QAC1C,CAAC;IACH,CAAC,CAAC;IAEF,OAAO,CAAC,QAAQ,GAAG,GAAG,EAAE;QACtB,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC,CAAC;QAChD,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,KAAK,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,EAAE,CAAC;YAC7E,eAAe,CAAC,QAAQ,EAAE,CAAC;QAC7B,CAAC;IACH,CAAC,CAAC;IAGF,MAAM,MAAM,GAAG,IAAI,GAAG,EAAkB,CAAC;IAEzC,OAAO,CAAC,IAAI,GAAG,CAAC,QAAgB,SAAS,EAAE,EAAE;QAC3C,MAAM,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;QAC9B,MAAM,CAAC,IAAI,CAAC,kBAAkB,KAAK,EAAE,EAAE,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC,CAAC;QAC/D,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,KAAK,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,EAAE,CAAC;YAC7E,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9B,CAAC;IACH,CAAC,CAAC;IAEF,OAAO,CAAC,OAAO,GAAG,CAAC,QAAgB,SAAS,EAAE,EAAE;QAC9C,MAAM,SAAS,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QACpC,IAAI,SAAS,EAAE,CAAC;YACd,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACrB,MAAM,CAAC,IAAI,CAAC,gBAAgB,KAAK,MAAM,QAAQ,IAAI,EAAE,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC,CAAC;QAC/E,CAAC;QACD,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,KAAK,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,EAAE,CAAC;YAC7E,eAAe,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QACjC,CAAC;IACH,CAAC,CAAC;IAEF,MAAM,QAAQ,GAAG,IAAI,GAAG,EAAkB,CAAC;IAE3C,OAAO,CAAC,KAAK,GAAG,CAAC,QAAgB,SAAS,EAAE,EAAE;QAC5C,MAAM,KAAK,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;QAC7C,QAAQ,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QAC3B,MAAM,CAAC,IAAI,CAAC,UAAU,KAAK,MAAM,KAAK,EAAE,EAAE,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC,CAAC;QAClE,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,KAAK,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,EAAE,CAAC;YAC7E,eAAe,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAC/B,CAAC;IACH,CAAC,CAAC;IAEF,OAAO,CAAC,KAAK,GAAG,GAAG,EAAE;QACnB,MAAM,CAAC,IAAI,CAAC,iBAAiB,EAAE,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC,CAAC;QACvD,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,KAAK,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,EAAE,CAAC;YAC7E,eAAe,CAAC,KAAK,EAAE,CAAC;QAC1B,CAAC;IACH,CAAC,CAAC;IAGF,OAAO,eAAe,CAAC;AACzB,CAAC"}