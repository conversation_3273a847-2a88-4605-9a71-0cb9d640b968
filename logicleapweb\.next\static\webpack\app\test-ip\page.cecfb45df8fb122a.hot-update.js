"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/test-ip/page",{

/***/ "(app-pages-browser)/./app/test-ip/page.tsx":
/*!******************************!*\
  !*** ./app/test-ip/page.tsx ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ TestIPPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Divider,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/typography/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Divider,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/space/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Divider,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/card/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Divider,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Divider,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/divider/index.js\");\n/* harmony import */ var _lib_request__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/request */ \"(app-pages-browser)/./lib/request.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst { Title, Text, Paragraph } = _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_3__[\"default\"];\nfunction TestIPPage() {\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [result, setResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [realPublicIP, setRealPublicIP] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [logs, setLogs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [debugInfo, setDebugInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 添加日志的辅助函数\n    const addLog = (message, data)=>{\n        const timestamp = new Date().toLocaleTimeString();\n        const logEntry = \"[\".concat(timestamp, \"] \").concat(message);\n        // 输出到控制台\n        if (data) {\n            console.log(message, data);\n        } else {\n            console.log(message);\n        }\n        // 添加到页面日志\n        setLogs((prev)=>[\n                ...prev.slice(-19),\n                logEntry\n            ]); // 保留最近20条日志\n    };\n    // 清空日志\n    const clearLogs = ()=>{\n        setLogs([]);\n        console.clear();\n        addLog(\"\\uD83E\\uDDF9 [Frontend] 日志已清空\");\n    };\n    // 页面加载时输出环境信息\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        var _navigator_connection, _navigator_connection1;\n        addLog(\"\\uD83C\\uDF10 [Frontend Init] 测试页面初始化...\");\n        console.log(\"\\uD83D\\uDDA5️ [Frontend Init] 浏览器环境信息:\", {\n            页面信息: {\n                URL: window.location.href,\n                域名: window.location.hostname,\n                端口: window.location.port,\n                协议: window.location.protocol,\n                路径: window.location.pathname\n            },\n            访问方式: {\n                是否本地访问: window.location.hostname === \"localhost\",\n                是否内网穿透: window.location.hostname.includes(\"ngrok\") || window.location.hostname.includes(\"tunnel\"),\n                访问类型: window.location.hostname === \"localhost\" ? \"本地开发\" : window.location.hostname.includes(\"ngrok\") ? \"ngrok穿透\" : window.location.hostname.includes(\"tunnel\") ? \"其他穿透\" : \"未知\"\n            },\n            浏览器信息: {\n                用户代理: navigator.userAgent,\n                语言: navigator.language,\n                平台: navigator.platform,\n                在线状态: navigator.onLine\n            },\n            网络信息: {\n                连接类型: ((_navigator_connection = navigator.connection) === null || _navigator_connection === void 0 ? void 0 : _navigator_connection.effectiveType) || \"未知\",\n                网络状态: ((_navigator_connection1 = navigator.connection) === null || _navigator_connection1 === void 0 ? void 0 : _navigator_connection1.downlink) ? \"\".concat(navigator.connection.downlink, \"Mbps\") : \"未知\"\n            },\n            时间信息: {\n                本地时间: new Date().toISOString(),\n                时区: Intl.DateTimeFormat().resolvedOptions().timeZone,\n                时区偏移: new Date().getTimezoneOffset()\n            }\n        });\n        // 输出预期的测试流程\n        console.log(\"\\uD83D\\uDCCB [Frontend Init] 测试流程说明:\", {\n            测试目标: \"IP地址获取和传递功能验证\",\n            测试步骤: [\n                \"1. 获取真实公网IP (通过第三方API)\",\n                \"2. 测试当前IP位置获取 (后端API)\",\n                \"3. 测试指定IP查询 (*******)\",\n                \"4. 测试登录IP记录 (模拟登录失败)\"\n            ],\n            观察要点: [\n                \"前端请求日志 (\\uD83D\\uDCE4 [Frontend Request])\",\n                \"前端响应日志 (\\uD83D\\uDCE5 [Frontend Response])\",\n                \"中间件IP处理 (\\uD83C\\uDF10 [Middleware])\",\n                \"后端IP提取 (\\uD83D\\uDDA5️ [Backend])\",\n                \"登录IP记录 (\\uD83D\\uDD10 [LoginLog])\"\n            ],\n            预期结果: {\n                本地访问: \"IP为模拟值 (**************)\",\n                穿透访问: \"IP为真实公网IP\",\n                地理位置: \"根据IP解析出对应位置\"\n            }\n        });\n    }, []);\n    // 获取真实的公网IP地址\n    const getRealPublicIP = async ()=>{\n        setLoading(true);\n        try {\n            console.log(\"\\uD83C\\uDF0D [Frontend] 开始获取真实公网IP...\");\n            // 使用多个IP查询服务，提高成功率\n            const ipServices = [\n                \"https://api.ipify.org?format=json\",\n                \"https://ipapi.co/json/\",\n                \"https://httpbin.org/ip\",\n                \"https://api.ip.sb/ip\",\n                \"https://ifconfig.me/ip\",\n                \"https://icanhazip.com\"\n            ];\n            for (const service of ipServices){\n                try {\n                    console.log(\"\\uD83D\\uDD0D 尝试获取公网IP: \".concat(service));\n                    const controller = new AbortController();\n                    const timeoutId = setTimeout(()=>controller.abort(), 5000);\n                    const response = await fetch(service, {\n                        method: \"GET\",\n                        signal: controller.signal\n                    });\n                    clearTimeout(timeoutId);\n                    if (!response.ok) continue;\n                    let data;\n                    const contentType = response.headers.get(\"content-type\");\n                    if (contentType && contentType.includes(\"application/json\")) {\n                        data = await response.json();\n                        const ip = data.ip || data.origin || data.query;\n                        if (ip && isValidIPAddress(ip)) {\n                            console.log(\"✅ 成功获取公网IP: \".concat(ip, \" (来源: \").concat(service, \")\"));\n                            setRealPublicIP(ip);\n                            setResult({\n                                type: \"real-public-ip\",\n                                ip: ip,\n                                source: service,\n                                timestamp: new Date().toISOString()\n                            });\n                            return;\n                        }\n                    } else {\n                        const text = await response.text();\n                        const ip = text.trim();\n                        if (isValidIPAddress(ip)) {\n                            console.log(\"✅ 成功获取公网IP: \".concat(ip, \" (来源: \").concat(service, \")\"));\n                            setRealPublicIP(ip);\n                            setResult({\n                                type: \"real-public-ip\",\n                                ip: ip,\n                                source: service,\n                                timestamp: new Date().toISOString()\n                            });\n                            return;\n                        }\n                    }\n                } catch (error) {\n                    console.log(\"❌ 获取公网IP失败: \".concat(service, \" - \").concat(error));\n                    continue;\n                }\n            }\n            throw new Error(\"所有公网IP服务都无法访问\");\n        } catch (error) {\n            console.error(\"❌ [Frontend] 获取真实公网IP失败:\", error);\n            setResult({\n                type: \"error\",\n                error: error.message,\n                timestamp: new Date().toISOString()\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 验证IP地址格式\n    const isValidIPAddress = (ip)=>{\n        const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;\n        return ipv4Regex.test(ip);\n    };\n    // 测试获取当前IP位置\n    const testCurrentIP = async ()=>{\n        setLoading(true);\n        try {\n            addLog(\"\\uD83E\\uDDEA [Frontend Test] 开始测试当前IP获取...\");\n            addLog(\"\\uD83C\\uDF10 [Frontend Test] 当前环境信息:\", {\n                页面URL: window.location.href,\n                域名: window.location.hostname,\n                是否内网穿透: window.location.hostname.includes(\"ngrok\") || window.location.hostname.includes(\"tunnel\"),\n                用户代理: navigator.userAgent.substring(0, 100) + \"...\",\n                时间戳: new Date().toISOString()\n            });\n            console.log(\"\\uD83D\\uDCE4 [Frontend Test] 准备发送请求到: /api/v1/ip-location/current\");\n            const startTime = Date.now();\n            const response = await _lib_request__WEBPACK_IMPORTED_MODULE_2__[\"default\"].get(\"/api/v1/ip-location/current\");\n            const endTime = Date.now();\n            console.log(\"\\uD83D\\uDCE5 [Frontend Test] 请求完成:\", {\n                耗时: \"\".concat(endTime - startTime, \"ms\"),\n                响应状态: response.status,\n                响应数据: response.data,\n                时间戳: new Date().toISOString()\n            });\n            setResult({\n                type: \"current-ip\",\n                data: response.data,\n                requestTime: endTime - startTime,\n                timestamp: new Date().toISOString()\n            });\n        } catch (error) {\n            var _error_response, _error_response1, _error_response2;\n            console.error(\"❌ [Frontend Test] 当前IP测试失败:\", {\n                错误类型: error.name,\n                错误消息: error.message,\n                响应状态: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status,\n                响应数据: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.data,\n                完整错误: error,\n                时间戳: new Date().toISOString()\n            });\n            setResult({\n                type: \"error\",\n                error: error.message,\n                response: (_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.data,\n                timestamp: new Date().toISOString()\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 测试查询指定IP\n    const testQueryIP = async ()=>{\n        setLoading(true);\n        try {\n            const testIP = \"*******\"; // Google DNS\n            console.log(\"\\uD83E\\uDDEA [Frontend Test] 开始测试IP查询...\");\n            console.log(\"\\uD83C\\uDFAF [Frontend Test] 查询参数:\", {\n                目标IP: testIP,\n                IP类型: \"Google DNS服务器\",\n                预期位置: \"美国\",\n                包含风险评估: false,\n                时间戳: new Date().toISOString()\n            });\n            const queryUrl = \"/api/v1/ip-location/query?ip=\".concat(testIP, \"&includeRisk=false\");\n            console.log(\"\\uD83D\\uDCE4 [Frontend Test] 准备发送查询请求:\", queryUrl);\n            const startTime = Date.now();\n            const response = await _lib_request__WEBPACK_IMPORTED_MODULE_2__[\"default\"].get(queryUrl);\n            const endTime = Date.now();\n            console.log(\"\\uD83D\\uDCE5 [Frontend Test] IP查询完成:\", {\n                查询IP: testIP,\n                耗时: \"\".concat(endTime - startTime, \"ms\"),\n                响应状态: response.status,\n                地理位置: response.data ? {\n                    国家: response.data.country,\n                    省份: response.data.province,\n                    城市: response.data.city,\n                    运营商: response.data.isp,\n                    置信度: response.data.confidence\n                } : \"无数据\",\n                完整响应: response.data,\n                时间戳: new Date().toISOString()\n            });\n            setResult({\n                type: \"query-ip\",\n                testIP,\n                data: response.data,\n                requestTime: endTime - startTime,\n                timestamp: new Date().toISOString()\n            });\n        } catch (error) {\n            var _error_response, _error_response1, _error_response2;\n            console.error(\"❌ [Frontend Test] IP查询失败:\", {\n                查询IP: \"*******\",\n                错误类型: error.name,\n                错误消息: error.message,\n                响应状态: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status,\n                响应数据: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.data,\n                可能原因: [\n                    \"后端服务未启动\",\n                    \"IP解析服务异常\",\n                    \"网络连接问题\"\n                ],\n                时间戳: new Date().toISOString()\n            });\n            setResult({\n                type: \"error\",\n                error: error.message,\n                response: (_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.data,\n                timestamp: new Date().toISOString()\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 测试登录接口（观察IP日志）\n    const testLoginIP = async ()=>{\n        setLoading(true);\n        try {\n            console.log(\"\\uD83E\\uDDEA [Frontend Test] 开始测试登录IP获取...\");\n            console.log(\"\\uD83D\\uDD10 [Frontend Test] 登录测试说明:\", {\n                目的: \"观察登录时的IP获取和记录过程\",\n                预期结果: \"登录失败（使用错误凭据）\",\n                观察重点: [\n                    \"IP地址获取\",\n                    \"登录日志记录\",\n                    \"错误处理\"\n                ],\n                测试凭据: {\n                    手机号: \"12345678910\",\n                    密码: \"123456 (错误密码)\"\n                },\n                时间戳: new Date().toISOString()\n            });\n            console.log(\"\\uD83D\\uDCE4 [Frontend Test] 发送登录请求...\");\n            const startTime = Date.now();\n            // 这里故意使用错误的登录信息，只是为了触发IP获取逻辑\n            const response = await _lib_request__WEBPACK_IMPORTED_MODULE_2__[\"default\"].post(\"/api/user-auth/password\", {\n                phone: \"12345678901\",\n                password: \"123456\"\n            });\n            const endTime = Date.now();\n            console.log(\"\\uD83D\\uDCE5 [Frontend Test] 登录响应 (意外成功):\", {\n                耗时: \"\".concat(endTime - startTime, \"ms\"),\n                响应状态: response.status,\n                响应数据: response.data,\n                注意: \"这不应该成功，请检查后端验证逻辑\",\n                时间戳: new Date().toISOString()\n            });\n            setResult({\n                type: \"login-test\",\n                data: response.data,\n                requestTime: endTime - startTime,\n                timestamp: new Date().toISOString()\n            });\n        } catch (error) {\n            var _error_response, _error_response1, _error_response2;\n            console.log(\"\\uD83D\\uDCDD [Frontend Test] 登录测试完成 (预期失败):\", {\n                错误类型: error.name,\n                错误消息: error.message,\n                响应状态: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status,\n                响应数据: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.data,\n                分析: {\n                    是否预期失败: true,\n                    失败原因: \"使用了错误的登录凭据\",\n                    IP获取状态: \"应该已触发IP获取和日志记录\",\n                    后续检查: \"查看后端控制台的 [LoginLog] 日志\"\n                },\n                时间戳: new Date().toISOString()\n            });\n            setResult({\n                type: \"login-test\",\n                error: error.message,\n                response: (_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.data,\n                note: \"这是预期的失败，主要用于观察IP获取日志\",\n                timestamp: new Date().toISOString()\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"24px\",\n            maxWidth: \"1200px\",\n            margin: \"0 auto\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                level: 2,\n                children: \"\\uD83E\\uDDEA IP地址获取测试页面\"\n            }, void 0, false, {\n                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                lineNumber: 372,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                children: \"这个页面用于测试前端到后端的IP地址传递和获取功能。 请打开浏览器开发者工具的控制台，以及后端服务器的日志，观察IP获取过程。\"\n            }, void 0, false, {\n                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                lineNumber: 374,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                direction: \"vertical\",\n                size: \"large\",\n                style: {\n                    width: \"100%\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83C\\uDFAF 测试功能\",\n                        size: \"small\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                wrap: true,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        type: \"primary\",\n                                        loading: loading,\n                                        onClick: getRealPublicIP,\n                                        style: {\n                                            background: \"#52c41a\",\n                                            borderColor: \"#52c41a\"\n                                        },\n                                        children: \"\\uD83C\\uDF0D 获取真实公网IP\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        loading: loading,\n                                        onClick: testCurrentIP,\n                                        children: \"测试获取当前IP位置\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                        lineNumber: 393,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        loading: loading,\n                                        onClick: testQueryIP,\n                                        children: \"测试查询指定IP (*******)\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        loading: loading,\n                                        onClick: testLoginIP,\n                                        danger: true,\n                                        children: \"测试登录IP获取 (会失败)\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                        lineNumber: 407,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        onClick: clearLogs,\n                                        style: {\n                                            marginLeft: \"12px\"\n                                        },\n                                        children: \"\\uD83E\\uDDF9 清空日志\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                        lineNumber: 415,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                lineNumber: 383,\n                                columnNumber: 11\n                            }, this),\n                            realPublicIP && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginTop: \"12px\",\n                                    padding: \"8px\",\n                                    background: \"#f6ffed\",\n                                    border: \"1px solid #b7eb8f\",\n                                    borderRadius: \"6px\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    strong: true,\n                                    style: {\n                                        color: \"#52c41a\"\n                                    },\n                                    children: [\n                                        \"\\uD83C\\uDF0D 你的真实公网IP: \",\n                                        realPublicIP\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 425,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                lineNumber: 424,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 382,\n                        columnNumber: 9\n                    }, this),\n                    result && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83D\\uDCCA 测试结果\",\n                        size: \"small\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                background: \"#f5f5f5\",\n                                padding: \"16px\",\n                                borderRadius: \"6px\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                style: {\n                                    margin: 0,\n                                    fontSize: \"12px\",\n                                    lineHeight: \"1.4\"\n                                },\n                                children: JSON.stringify(result, null, 2)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                lineNumber: 434,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                            lineNumber: 433,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 432,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83D\\uDCDD 实时前端日志\",\n                        size: \"small\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    background: \"#000\",\n                                    color: \"#00ff00\",\n                                    padding: \"12px\",\n                                    borderRadius: \"6px\",\n                                    fontFamily: \"Monaco, Consolas, monospace\",\n                                    fontSize: \"12px\",\n                                    maxHeight: \"300px\",\n                                    overflowY: \"auto\"\n                                },\n                                children: logs.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        color: \"#666\"\n                                    },\n                                    children: \"等待日志输出...\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 454,\n                                    columnNumber: 15\n                                }, this) : logs.map((log, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            marginBottom: \"2px\"\n                                        },\n                                        children: log\n                                    }, index, false, {\n                                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                        lineNumber: 457,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                lineNumber: 443,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginTop: \"8px\",\n                                    fontSize: \"12px\",\n                                    color: \"#666\"\n                                },\n                                children: \"\\uD83D\\uDCA1 提示：这里显示前端日志，完整日志请查看浏览器控制台\"\n                            }, void 0, false, {\n                                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                lineNumber: 463,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 442,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 468,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83D\\uDCCB 观察要点\",\n                        size: \"small\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            direction: \"vertical\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            children: \"\\uD83C\\uDF10 前端中间件日志：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 474,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"查看浏览器控制台，观察 [Middleware] 标记的日志\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 475,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 473,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            children: \"\\uD83D\\uDDA5️ 后端IP提取日志：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 479,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"查看后端控制台，观察 [Backend] 标记的日志\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 480,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 478,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            children: \"\\uD83D\\uDD10 登录日志：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 484,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"查看后端控制台，观察 [LoginLog] 标记的日志\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 485,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 483,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            children: \"\\uD83D\\uDD0D 重点观察：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 489,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"IP地址是否从前端正确传递到后端，以及各个环节的IP获取情况\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 490,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 488,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                            lineNumber: 472,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 471,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83E\\uDD14 为什么本地开发获取到127.0.0.1？\",\n                        size: \"small\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            direction: \"vertical\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            style: {\n                                                color: \"#fa8c16\"\n                                            },\n                                            children: \"\\uD83C\\uDFE0 本地开发环境：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 499,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"浏览器 → localhost:3000 → 后端API，所有请求都来自本机，所以IP是127.0.0.1\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 500,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 498,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            style: {\n                                                color: \"#52c41a\"\n                                            },\n                                            children: \"\\uD83C\\uDF0D 生产环境：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 504,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"用户浏览器 → CDN/负载均衡 → Web服务器 → 后端API，能获取到真实公网IP\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 505,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 503,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            style: {\n                                                color: \"#1890ff\"\n                                            },\n                                            children: \"\\uD83C\\uDFAD 模拟解决方案：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 509,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"中间件已配置在开发环境使用模拟公网IP (**************) 进行测试\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 510,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 508,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            style: {\n                                                color: \"#722ed1\"\n                                            },\n                                            children: \"\\uD83E\\uDDEA 真实IP对比：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 514,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: '点击\"获取真实公网IP\"按钮，对比你的真实公网IP和后端获取的IP'\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 515,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 513,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                            lineNumber: 497,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 496,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83C\\uDF0D 当前环境信息\",\n                        size: \"small\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            direction: \"vertical\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    children: [\n                                        \"浏览器 User-Agent: \",\n                                         true ? navigator.userAgent : 0\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 523,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    children: [\n                                        \"当前时间: \",\n                                        new Date().toISOString()\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 524,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    children: [\n                                        \"页面URL: \",\n                                         true ? window.location.href : 0\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 525,\n                                    columnNumber: 13\n                                }, this),\n                                 true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    style: {\n                                        color: window.location.hostname.includes(\"ngrok\") || window.location.hostname.includes(\"tunnel\") ? \"#52c41a\" : \"#fa8c16\",\n                                        fontWeight: \"bold\"\n                                    },\n                                    children: [\n                                        \"访问方式: \",\n                                        window.location.hostname.includes(\"ngrok\") || window.location.hostname.includes(\"tunnel\") ? \"\\uD83C\\uDF10 内网穿透访问 (可获取真实IP)\" : \"\\uD83C\\uDFE0 本地访问 (模拟IP)\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 527,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                            lineNumber: 522,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 521,\n                        columnNumber: 9\n                    }, this),\n                     true && !window.location.hostname.includes(\"ngrok\") && !window.location.hostname.includes(\"tunnel\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83D\\uDE80 想要测试真实IP获取？\",\n                        size: \"small\",\n                        style: {\n                            borderColor: \"#52c41a\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            direction: \"vertical\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    strong: true,\n                                    style: {\n                                        color: \"#52c41a\"\n                                    },\n                                    children: \"使用内网穿透获取真实IP：\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 545,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        background: \"#f6ffed\",\n                                        padding: \"12px\",\n                                        borderRadius: \"6px\",\n                                        border: \"1px solid #b7eb8f\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            code: true,\n                                            children: \"1. 安装ngrok: npm install -g ngrok\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 547,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 547,\n                                            columnNumber: 67\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            code: true,\n                                            children: \"2. 穿透前端: ngrok http 3000\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 548,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 548,\n                                            columnNumber: 59\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            code: true,\n                                            children: \"3. 访问ngrok提供的公网地址\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 549,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 549,\n                                            columnNumber: 52\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            code: true,\n                                            children: \"4. 重新测试IP获取功能\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 550,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 546,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    type: \"secondary\",\n                                    children: \"通过内网穿透，你可以模拟真实的生产环境，获取到真实的公网IP地址！\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 552,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                            lineNumber: 544,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 543,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                lineNumber: 379,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n        lineNumber: 371,\n        columnNumber: 5\n    }, this);\n}\n_s(TestIPPage, \"UKdI/aIOtKUiZXV4GhGqcrMqaTI=\");\n_c = TestIPPage;\nvar _c;\n$RefreshReg$(_c, \"TestIPPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC90ZXN0LWlwL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7OztBQUU0QztBQUMwQjtBQUNsQztBQUdwQyxNQUFNLEVBQUVRLEtBQUssRUFBRUMsSUFBSSxFQUFFQyxTQUFTLEVBQUUsR0FBR04sd0dBQVVBO0FBRTlCLFNBQVNPOztJQUN0QixNQUFNLENBQUNDLFNBQVNDLFdBQVcsR0FBR2IsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDYyxRQUFRQyxVQUFVLEdBQUdmLCtDQUFRQSxDQUFNO0lBQzFDLE1BQU0sQ0FBQ2dCLGNBQWNDLGdCQUFnQixHQUFHakIsK0NBQVFBLENBQVM7SUFDekQsTUFBTSxDQUFDa0IsTUFBTUMsUUFBUSxHQUFHbkIsK0NBQVFBLENBQVcsRUFBRTtJQUM3QyxNQUFNLENBQUNvQixXQUFXQyxhQUFhLEdBQUdyQiwrQ0FBUUEsQ0FBTTtJQUVoRCxZQUFZO0lBQ1osTUFBTXNCLFNBQVMsQ0FBQ0MsU0FBaUJDO1FBQy9CLE1BQU1DLFlBQVksSUFBSUMsT0FBT0Msa0JBQWtCO1FBQy9DLE1BQU1DLFdBQVcsSUFBa0JMLE9BQWRFLFdBQVUsTUFBWSxPQUFSRjtRQUVuQyxTQUFTO1FBQ1QsSUFBSUMsTUFBTTtZQUNSSyxRQUFRQyxHQUFHLENBQUNQLFNBQVNDO1FBQ3ZCLE9BQU87WUFDTEssUUFBUUMsR0FBRyxDQUFDUDtRQUNkO1FBRUEsVUFBVTtRQUNWSixRQUFRWSxDQUFBQSxPQUFRO21CQUFJQSxLQUFLQyxLQUFLLENBQUMsQ0FBQztnQkFBS0o7YUFBUyxHQUFHLFlBQVk7SUFDL0Q7SUFFQSxPQUFPO0lBQ1AsTUFBTUssWUFBWTtRQUNoQmQsUUFBUSxFQUFFO1FBQ1ZVLFFBQVFLLEtBQUs7UUFDYlosT0FBTztJQUNUO0lBRUEsY0FBYztJQUNkckIsZ0RBQVNBLENBQUM7WUF3QkUsdUJBQ0E7UUF4QlZxQixPQUFPO1FBQ1BPLFFBQVFDLEdBQUcsQ0FBQywwQ0FBZ0M7WUFDMUNLLE1BQU07Z0JBQ0pDLEtBQUtDLE9BQU9DLFFBQVEsQ0FBQ0MsSUFBSTtnQkFDekJDLElBQUlILE9BQU9DLFFBQVEsQ0FBQ0csUUFBUTtnQkFDNUJDLElBQUlMLE9BQU9DLFFBQVEsQ0FBQ0ssSUFBSTtnQkFDeEJDLElBQUlQLE9BQU9DLFFBQVEsQ0FBQ08sUUFBUTtnQkFDNUJDLElBQUlULE9BQU9DLFFBQVEsQ0FBQ1MsUUFBUTtZQUM5QjtZQUNBQyxNQUFNO2dCQUNKQyxRQUFRWixPQUFPQyxRQUFRLENBQUNHLFFBQVEsS0FBSztnQkFDckNTLFFBQVFiLE9BQU9DLFFBQVEsQ0FBQ0csUUFBUSxDQUFDVSxRQUFRLENBQUMsWUFBWWQsT0FBT0MsUUFBUSxDQUFDRyxRQUFRLENBQUNVLFFBQVEsQ0FBQztnQkFDeEZDLE1BQU1mLE9BQU9DLFFBQVEsQ0FBQ0csUUFBUSxLQUFLLGNBQWMsU0FDdkNKLE9BQU9DLFFBQVEsQ0FBQ0csUUFBUSxDQUFDVSxRQUFRLENBQUMsV0FBVyxZQUM3Q2QsT0FBT0MsUUFBUSxDQUFDRyxRQUFRLENBQUNVLFFBQVEsQ0FBQyxZQUFZLFNBQVM7WUFDbkU7WUFDQUUsT0FBTztnQkFDTEMsTUFBTUMsVUFBVUMsU0FBUztnQkFDekJDLElBQUlGLFVBQVVHLFFBQVE7Z0JBQ3RCQyxJQUFJSixVQUFVSyxRQUFRO2dCQUN0QkMsTUFBTU4sVUFBVU8sTUFBTTtZQUN4QjtZQUNBQyxNQUFNO2dCQUNKQyxNQUFNLG9DQUFtQkMsVUFBVSxjQUE3QixrRUFBK0JDLGFBQWEsS0FBSTtnQkFDdERDLE1BQU0scUNBQW1CRixVQUFVLGNBQTdCLG9FQUErQkcsUUFBUSxJQUFHLEdBQTBDLE9BQXZDLFVBQW1CSCxVQUFVLENBQUNHLFFBQVEsRUFBQyxVQUFRO1lBQ3BHO1lBQ0FDLE1BQU07Z0JBQ0pDLE1BQU0sSUFBSTVDLE9BQU82QyxXQUFXO2dCQUM1QkMsSUFBSUMsS0FBS0MsY0FBYyxHQUFHQyxlQUFlLEdBQUdDLFFBQVE7Z0JBQ3BEQyxNQUFNLElBQUluRCxPQUFPb0QsaUJBQWlCO1lBQ3BDO1FBQ0Y7UUFFQSxZQUFZO1FBQ1pqRCxRQUFRQyxHQUFHLENBQUMsd0NBQThCO1lBQ3hDaUQsTUFBTTtZQUNOQyxNQUFNO2dCQUNKO2dCQUNBO2dCQUNBO2dCQUNBO2FBQ0Q7WUFDREMsTUFBTTtnQkFDSjtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTthQUNEO1lBQ0RDLE1BQU07Z0JBQ0pDLE1BQU07Z0JBQ05DLE1BQU07Z0JBQ05DLE1BQU07WUFDUjtRQUNGO0lBQ0YsR0FBRyxFQUFFO0lBRUwsY0FBYztJQUNkLE1BQU1DLGtCQUFrQjtRQUN0QnpFLFdBQVc7UUFDWCxJQUFJO1lBQ0ZnQixRQUFRQyxHQUFHLENBQUM7WUFFWixtQkFBbUI7WUFDbkIsTUFBTXlELGFBQWE7Z0JBQ2pCO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2FBQ0Q7WUFFRCxLQUFLLE1BQU1DLFdBQVdELFdBQVk7Z0JBQ2hDLElBQUk7b0JBQ0YxRCxRQUFRQyxHQUFHLENBQUMsMEJBQXdCLE9BQVIwRDtvQkFFNUIsTUFBTUMsYUFBYSxJQUFJQztvQkFDdkIsTUFBTUMsWUFBWUMsV0FBVyxJQUFNSCxXQUFXSSxLQUFLLElBQUk7b0JBRXZELE1BQU1DLFdBQVcsTUFBTUMsTUFBTVAsU0FBUzt3QkFDcENRLFFBQVE7d0JBQ1JDLFFBQVFSLFdBQVdRLE1BQU07b0JBQzNCO29CQUVBQyxhQUFhUDtvQkFFYixJQUFJLENBQUNHLFNBQVNLLEVBQUUsRUFBRTtvQkFFbEIsSUFBSTNFO29CQUNKLE1BQU00RSxjQUFjTixTQUFTTyxPQUFPLENBQUNDLEdBQUcsQ0FBQztvQkFFekMsSUFBSUYsZUFBZUEsWUFBWWpELFFBQVEsQ0FBQyxxQkFBcUI7d0JBQzNEM0IsT0FBTyxNQUFNc0UsU0FBU1MsSUFBSTt3QkFDMUIsTUFBTUMsS0FBS2hGLEtBQUtnRixFQUFFLElBQUloRixLQUFLaUYsTUFBTSxJQUFJakYsS0FBS2tGLEtBQUs7d0JBQy9DLElBQUlGLE1BQU1HLGlCQUFpQkgsS0FBSzs0QkFDOUIzRSxRQUFRQyxHQUFHLENBQUMsZUFBMEIwRCxPQUFYZ0IsSUFBRyxVQUFnQixPQUFSaEIsU0FBUTs0QkFDOUN2RSxnQkFBZ0J1Rjs0QkFDaEJ6RixVQUFVO2dDQUNSNkYsTUFBTTtnQ0FDTkosSUFBSUE7Z0NBQ0pLLFFBQVFyQjtnQ0FDUi9ELFdBQVcsSUFBSUMsT0FBTzZDLFdBQVc7NEJBQ25DOzRCQUNBO3dCQUNGO29CQUNGLE9BQU87d0JBQ0wsTUFBTXVDLE9BQU8sTUFBTWhCLFNBQVNnQixJQUFJO3dCQUNoQyxNQUFNTixLQUFLTSxLQUFLQyxJQUFJO3dCQUNwQixJQUFJSixpQkFBaUJILEtBQUs7NEJBQ3hCM0UsUUFBUUMsR0FBRyxDQUFDLGVBQTBCMEQsT0FBWGdCLElBQUcsVUFBZ0IsT0FBUmhCLFNBQVE7NEJBQzlDdkUsZ0JBQWdCdUY7NEJBQ2hCekYsVUFBVTtnQ0FDUjZGLE1BQU07Z0NBQ05KLElBQUlBO2dDQUNKSyxRQUFRckI7Z0NBQ1IvRCxXQUFXLElBQUlDLE9BQU82QyxXQUFXOzRCQUNuQzs0QkFDQTt3QkFDRjtvQkFDRjtnQkFDRixFQUFFLE9BQU95QyxPQUFPO29CQUNkbkYsUUFBUUMsR0FBRyxDQUFDLGVBQTRCa0YsT0FBYnhCLFNBQVEsT0FBVyxPQUFOd0I7b0JBQ3hDO2dCQUNGO1lBQ0Y7WUFFQSxNQUFNLElBQUlDLE1BQU07UUFDbEIsRUFBRSxPQUFPRCxPQUFZO1lBQ25CbkYsUUFBUW1GLEtBQUssQ0FBQyw0QkFBNEJBO1lBQzFDakcsVUFBVTtnQkFDUjZGLE1BQU07Z0JBQ05JLE9BQU9BLE1BQU16RixPQUFPO2dCQUNwQkUsV0FBVyxJQUFJQyxPQUFPNkMsV0FBVztZQUNuQztRQUNGLFNBQVU7WUFDUjFELFdBQVc7UUFDYjtJQUNGO0lBRUEsV0FBVztJQUNYLE1BQU04RixtQkFBbUIsQ0FBQ0g7UUFDeEIsTUFBTVUsWUFBWTtRQUNsQixPQUFPQSxVQUFVQyxJQUFJLENBQUNYO0lBQ3hCO0lBRUEsYUFBYTtJQUNiLE1BQU1ZLGdCQUFnQjtRQUNwQnZHLFdBQVc7UUFDWCxJQUFJO1lBQ0ZTLE9BQU87WUFDUEEsT0FBTyx3Q0FBOEI7Z0JBQ25DK0YsT0FBT2hGLE9BQU9DLFFBQVEsQ0FBQ0MsSUFBSTtnQkFDM0JDLElBQUlILE9BQU9DLFFBQVEsQ0FBQ0csUUFBUTtnQkFDNUJTLFFBQVFiLE9BQU9DLFFBQVEsQ0FBQ0csUUFBUSxDQUFDVSxRQUFRLENBQUMsWUFBWWQsT0FBT0MsUUFBUSxDQUFDRyxRQUFRLENBQUNVLFFBQVEsQ0FBQztnQkFDeEZHLE1BQU1DLFVBQVVDLFNBQVMsQ0FBQzhELFNBQVMsQ0FBQyxHQUFHLE9BQU87Z0JBQzlDQyxLQUFLLElBQUk3RixPQUFPNkMsV0FBVztZQUM3QjtZQUVBMUMsUUFBUUMsR0FBRyxDQUFDO1lBQ1osTUFBTTBGLFlBQVk5RixLQUFLK0YsR0FBRztZQUUxQixNQUFNM0IsV0FBVyxNQUFNdkYsb0RBQU9BLENBQUMrRixHQUFHLENBQUM7WUFFbkMsTUFBTW9CLFVBQVVoRyxLQUFLK0YsR0FBRztZQUN4QjVGLFFBQVFDLEdBQUcsQ0FBQyxzQ0FBNEI7Z0JBQ3RDNkYsSUFBSSxHQUF1QixPQUFwQkQsVUFBVUYsV0FBVTtnQkFDM0JJLE1BQU05QixTQUFTK0IsTUFBTTtnQkFDckJDLE1BQU1oQyxTQUFTdEUsSUFBSTtnQkFDbkIrRixLQUFLLElBQUk3RixPQUFPNkMsV0FBVztZQUM3QjtZQUVBeEQsVUFBVTtnQkFDUjZGLE1BQU07Z0JBQ05wRixNQUFNc0UsU0FBU3RFLElBQUk7Z0JBQ25CdUcsYUFBYUwsVUFBVUY7Z0JBQ3ZCL0YsV0FBVyxJQUFJQyxPQUFPNkMsV0FBVztZQUNuQztRQUNGLEVBQUUsT0FBT3lDLE9BQVk7Z0JBSVhBLGlCQUNBQSxrQkFPSUE7WUFYWm5GLFFBQVFtRixLQUFLLENBQUMsK0JBQStCO2dCQUMzQ2dCLE1BQU1oQixNQUFNaUIsSUFBSTtnQkFDaEJDLE1BQU1sQixNQUFNekYsT0FBTztnQkFDbkJxRyxJQUFJLEdBQUVaLGtCQUFBQSxNQUFNbEIsUUFBUSxjQUFka0Isc0NBQUFBLGdCQUFnQmEsTUFBTTtnQkFDNUJDLElBQUksR0FBRWQsbUJBQUFBLE1BQU1sQixRQUFRLGNBQWRrQix1Q0FBQUEsaUJBQWdCeEYsSUFBSTtnQkFDMUIyRyxNQUFNbkI7Z0JBQ05PLEtBQUssSUFBSTdGLE9BQU82QyxXQUFXO1lBQzdCO1lBQ0F4RCxVQUFVO2dCQUNSNkYsTUFBTTtnQkFDTkksT0FBT0EsTUFBTXpGLE9BQU87Z0JBQ3BCdUUsUUFBUSxHQUFFa0IsbUJBQUFBLE1BQU1sQixRQUFRLGNBQWRrQix1Q0FBQUEsaUJBQWdCeEYsSUFBSTtnQkFDOUJDLFdBQVcsSUFBSUMsT0FBTzZDLFdBQVc7WUFDbkM7UUFDRixTQUFVO1lBQ1IxRCxXQUFXO1FBQ2I7SUFDRjtJQUVBLFdBQVc7SUFDWCxNQUFNdUgsY0FBYztRQUNsQnZILFdBQVc7UUFDWCxJQUFJO1lBQ0YsTUFBTXdILFNBQVMsV0FBVyxhQUFhO1lBQ3ZDeEcsUUFBUUMsR0FBRyxDQUFDO1lBQ1pELFFBQVFDLEdBQUcsQ0FBQyxzQ0FBNEI7Z0JBQ3RDd0csTUFBTUQ7Z0JBQ05FLE1BQU07Z0JBQ05DLE1BQU07Z0JBQ05DLFFBQVE7Z0JBQ1JsQixLQUFLLElBQUk3RixPQUFPNkMsV0FBVztZQUM3QjtZQUVBLE1BQU1tRSxXQUFXLGdDQUF1QyxPQUFQTCxRQUFPO1lBQ3hEeEcsUUFBUUMsR0FBRyxDQUFDLDBDQUFnQzRHO1lBRTVDLE1BQU1sQixZQUFZOUYsS0FBSytGLEdBQUc7WUFDMUIsTUFBTTNCLFdBQVcsTUFBTXZGLG9EQUFPQSxDQUFDK0YsR0FBRyxDQUFDb0M7WUFDbkMsTUFBTWhCLFVBQVVoRyxLQUFLK0YsR0FBRztZQUV4QjVGLFFBQVFDLEdBQUcsQ0FBQyx3Q0FBOEI7Z0JBQ3hDNkcsTUFBTU47Z0JBQ05WLElBQUksR0FBdUIsT0FBcEJELFVBQVVGLFdBQVU7Z0JBQzNCSSxNQUFNOUIsU0FBUytCLE1BQU07Z0JBQ3JCeEMsTUFBTVMsU0FBU3RFLElBQUksR0FBRztvQkFDcEJvSCxJQUFJOUMsU0FBU3RFLElBQUksQ0FBQ3FILE9BQU87b0JBQ3pCQyxJQUFJaEQsU0FBU3RFLElBQUksQ0FBQ3VILFFBQVE7b0JBQzFCQyxJQUFJbEQsU0FBU3RFLElBQUksQ0FBQ3lILElBQUk7b0JBQ3RCQyxLQUFLcEQsU0FBU3RFLElBQUksQ0FBQzJILEdBQUc7b0JBQ3RCQyxLQUFLdEQsU0FBU3RFLElBQUksQ0FBQzZILFVBQVU7Z0JBQy9CLElBQUk7Z0JBQ0pDLE1BQU14RCxTQUFTdEUsSUFBSTtnQkFDbkIrRixLQUFLLElBQUk3RixPQUFPNkMsV0FBVztZQUM3QjtZQUVBeEQsVUFBVTtnQkFDUjZGLE1BQU07Z0JBQ055QjtnQkFDQTdHLE1BQU1zRSxTQUFTdEUsSUFBSTtnQkFDbkJ1RyxhQUFhTCxVQUFVRjtnQkFDdkIvRixXQUFXLElBQUlDLE9BQU82QyxXQUFXO1lBQ25DO1FBQ0YsRUFBRSxPQUFPeUMsT0FBWTtnQkFLWEEsaUJBQ0FBLGtCQU9JQTtZQVpabkYsUUFBUW1GLEtBQUssQ0FBQyw2QkFBNkI7Z0JBQ3pDMkIsTUFBTTtnQkFDTlgsTUFBTWhCLE1BQU1pQixJQUFJO2dCQUNoQkMsTUFBTWxCLE1BQU16RixPQUFPO2dCQUNuQnFHLElBQUksR0FBRVosa0JBQUFBLE1BQU1sQixRQUFRLGNBQWRrQixzQ0FBQUEsZ0JBQWdCYSxNQUFNO2dCQUM1QkMsSUFBSSxHQUFFZCxtQkFBQUEsTUFBTWxCLFFBQVEsY0FBZGtCLHVDQUFBQSxpQkFBZ0J4RixJQUFJO2dCQUMxQitILE1BQU07b0JBQUM7b0JBQVc7b0JBQVk7aUJBQVM7Z0JBQ3ZDaEMsS0FBSyxJQUFJN0YsT0FBTzZDLFdBQVc7WUFDN0I7WUFDQXhELFVBQVU7Z0JBQ1I2RixNQUFNO2dCQUNOSSxPQUFPQSxNQUFNekYsT0FBTztnQkFDcEJ1RSxRQUFRLEdBQUVrQixtQkFBQUEsTUFBTWxCLFFBQVEsY0FBZGtCLHVDQUFBQSxpQkFBZ0J4RixJQUFJO2dCQUM5QkMsV0FBVyxJQUFJQyxPQUFPNkMsV0FBVztZQUNuQztRQUNGLFNBQVU7WUFDUjFELFdBQVc7UUFDYjtJQUNGO0lBRUEsaUJBQWlCO0lBQ2pCLE1BQU0ySSxjQUFjO1FBQ2xCM0ksV0FBVztRQUNYLElBQUk7WUFDRmdCLFFBQVFDLEdBQUcsQ0FBQztZQUNaRCxRQUFRQyxHQUFHLENBQUMsd0NBQThCO2dCQUN4QzJILElBQUk7Z0JBQ0p2RSxNQUFNO2dCQUNOd0UsTUFBTTtvQkFBQztvQkFBVTtvQkFBVTtpQkFBTztnQkFDbENDLE1BQU07b0JBQ0pDLEtBQUs7b0JBQ0xDLElBQUk7Z0JBQ047Z0JBQ0F0QyxLQUFLLElBQUk3RixPQUFPNkMsV0FBVztZQUM3QjtZQUVBMUMsUUFBUUMsR0FBRyxDQUFDO1lBQ1osTUFBTTBGLFlBQVk5RixLQUFLK0YsR0FBRztZQUUxQiw2QkFBNkI7WUFDN0IsTUFBTTNCLFdBQVcsTUFBTXZGLG9EQUFPQSxDQUFDdUosSUFBSSxDQUFDLDJCQUEyQjtnQkFDN0RDLE9BQU87Z0JBQ1BDLFVBQVU7WUFDWjtZQUVBLE1BQU10QyxVQUFVaEcsS0FBSytGLEdBQUc7WUFDeEI1RixRQUFRQyxHQUFHLENBQUMsNkNBQW1DO2dCQUM3QzZGLElBQUksR0FBdUIsT0FBcEJELFVBQVVGLFdBQVU7Z0JBQzNCSSxNQUFNOUIsU0FBUytCLE1BQU07Z0JBQ3JCQyxNQUFNaEMsU0FBU3RFLElBQUk7Z0JBQ25CeUksSUFBSTtnQkFDSjFDLEtBQUssSUFBSTdGLE9BQU82QyxXQUFXO1lBQzdCO1lBRUF4RCxVQUFVO2dCQUNSNkYsTUFBTTtnQkFDTnBGLE1BQU1zRSxTQUFTdEUsSUFBSTtnQkFDbkJ1RyxhQUFhTCxVQUFVRjtnQkFDdkIvRixXQUFXLElBQUlDLE9BQU82QyxXQUFXO1lBQ25DO1FBQ0YsRUFBRSxPQUFPeUMsT0FBWTtnQkFJWEEsaUJBQ0FBLGtCQVlJQTtZQWhCWm5GLFFBQVFDLEdBQUcsQ0FBQywrQ0FBcUM7Z0JBQy9Da0csTUFBTWhCLE1BQU1pQixJQUFJO2dCQUNoQkMsTUFBTWxCLE1BQU16RixPQUFPO2dCQUNuQnFHLElBQUksR0FBRVosa0JBQUFBLE1BQU1sQixRQUFRLGNBQWRrQixzQ0FBQUEsZ0JBQWdCYSxNQUFNO2dCQUM1QkMsSUFBSSxHQUFFZCxtQkFBQUEsTUFBTWxCLFFBQVEsY0FBZGtCLHVDQUFBQSxpQkFBZ0J4RixJQUFJO2dCQUMxQjBJLElBQUk7b0JBQ0ZDLFFBQVE7b0JBQ1JDLE1BQU07b0JBQ05DLFFBQVE7b0JBQ1JDLE1BQU07Z0JBQ1I7Z0JBQ0EvQyxLQUFLLElBQUk3RixPQUFPNkMsV0FBVztZQUM3QjtZQUNBeEQsVUFBVTtnQkFDUjZGLE1BQU07Z0JBQ05JLE9BQU9BLE1BQU16RixPQUFPO2dCQUNwQnVFLFFBQVEsR0FBRWtCLG1CQUFBQSxNQUFNbEIsUUFBUSxjQUFka0IsdUNBQUFBLGlCQUFnQnhGLElBQUk7Z0JBQzlCK0ksTUFBTTtnQkFDTjlJLFdBQVcsSUFBSUMsT0FBTzZDLFdBQVc7WUFDbkM7UUFDRixTQUFVO1lBQ1IxRCxXQUFXO1FBQ2I7SUFDRjtJQUVBLHFCQUNFLDhEQUFDMko7UUFBSUMsT0FBTztZQUFFQyxTQUFTO1lBQVFDLFVBQVU7WUFBVUMsUUFBUTtRQUFTOzswQkFDbEUsOERBQUNwSztnQkFBTXFLLE9BQU87MEJBQUc7Ozs7OzswQkFFakIsOERBQUNuSzswQkFBVTs7Ozs7OzBCQUtYLDhEQUFDTCx3R0FBS0E7Z0JBQUN5SyxXQUFVO2dCQUFXQyxNQUFLO2dCQUFRTixPQUFPO29CQUFFTyxPQUFPO2dCQUFPOztrQ0FHOUQsOERBQUM3Syx3R0FBSUE7d0JBQUM4SyxPQUFNO3dCQUFVRixNQUFLOzswQ0FDekIsOERBQUMxSyx3R0FBS0E7Z0NBQUM2SyxJQUFJOztrREFDVCw4REFBQ2hMLHdHQUFNQTt3Q0FDTDBHLE1BQUs7d0NBQ0xoRyxTQUFTQTt3Q0FDVHVLLFNBQVM3Rjt3Q0FDVG1GLE9BQU87NENBQUVXLFlBQVk7NENBQVdDLGFBQWE7d0NBQVU7a0RBQ3hEOzs7Ozs7a0RBSUQsOERBQUNuTCx3R0FBTUE7d0NBQ0xVLFNBQVNBO3dDQUNUdUssU0FBUy9EO2tEQUNWOzs7Ozs7a0RBSUQsOERBQUNsSCx3R0FBTUE7d0NBQ0xVLFNBQVNBO3dDQUNUdUssU0FBUy9DO2tEQUNWOzs7Ozs7a0RBSUQsOERBQUNsSSx3R0FBTUE7d0NBQ0xVLFNBQVNBO3dDQUNUdUssU0FBUzNCO3dDQUNUOEIsTUFBTTtrREFDUDs7Ozs7O2tEQUlELDhEQUFDcEwsd0dBQU1BO3dDQUNMaUwsU0FBU2xKO3dDQUNUd0ksT0FBTzs0Q0FBRWMsWUFBWTt3Q0FBTztrREFDN0I7Ozs7Ozs7Ozs7Ozs0QkFLRnZLLDhCQUNDLDhEQUFDd0o7Z0NBQUlDLE9BQU87b0NBQUVlLFdBQVc7b0NBQVFkLFNBQVM7b0NBQU9VLFlBQVk7b0NBQVdLLFFBQVE7b0NBQXFCQyxjQUFjO2dDQUFNOzBDQUN2SCw0RUFBQ2pMO29DQUFLa0wsTUFBTTtvQ0FBQ2xCLE9BQU87d0NBQUVtQixPQUFPO29DQUFVOzt3Q0FBRzt3Q0FBYzVLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7b0JBTTdERix3QkFDQyw4REFBQ1gsd0dBQUlBO3dCQUFDOEssT0FBTTt3QkFBVUYsTUFBSztrQ0FDekIsNEVBQUNQOzRCQUFJQyxPQUFPO2dDQUFFVyxZQUFZO2dDQUFXVixTQUFTO2dDQUFRZ0IsY0FBYzs0QkFBTTtzQ0FDeEUsNEVBQUNHO2dDQUFJcEIsT0FBTztvQ0FBRUcsUUFBUTtvQ0FBR2tCLFVBQVU7b0NBQVFDLFlBQVk7Z0NBQU07MENBQzFEQyxLQUFLQyxTQUFTLENBQUNuTCxRQUFRLE1BQU07Ozs7Ozs7Ozs7Ozs7Ozs7a0NBT3RDLDhEQUFDWCx3R0FBSUE7d0JBQUM4SyxPQUFNO3dCQUFZRixNQUFLOzswQ0FDM0IsOERBQUNQO2dDQUFJQyxPQUFPO29DQUNWVyxZQUFZO29DQUNaUSxPQUFPO29DQUNQbEIsU0FBUztvQ0FDVGdCLGNBQWM7b0NBQ2RRLFlBQVk7b0NBQ1pKLFVBQVU7b0NBQ1ZLLFdBQVc7b0NBQ1hDLFdBQVc7Z0NBQ2I7MENBQ0dsTCxLQUFLbUwsTUFBTSxLQUFLLGtCQUNmLDhEQUFDN0I7b0NBQUlDLE9BQU87d0NBQUVtQixPQUFPO29DQUFPOzhDQUFHOzs7OzsyQ0FFL0IxSyxLQUFLb0wsR0FBRyxDQUFDLENBQUN4SyxLQUFLeUssc0JBQ2IsOERBQUMvQjt3Q0FBZ0JDLE9BQU87NENBQUUrQixjQUFjO3dDQUFNO2tEQUMzQzFLO3VDQURPeUs7Ozs7Ozs7Ozs7MENBTWhCLDhEQUFDL0I7Z0NBQUlDLE9BQU87b0NBQUVlLFdBQVc7b0NBQU9NLFVBQVU7b0NBQVFGLE9BQU87Z0NBQU87MENBQUc7Ozs7Ozs7Ozs7OztrQ0FLckUsOERBQUN0TCx3R0FBT0E7Ozs7O2tDQUdSLDhEQUFDSCx3R0FBSUE7d0JBQUM4SyxPQUFNO3dCQUFVRixNQUFLO2tDQUN6Qiw0RUFBQzFLLHdHQUFLQTs0QkFBQ3lLLFdBQVU7OzhDQUNmLDhEQUFDTjs7c0RBQ0MsOERBQUMvSjs0Q0FBS2tMLE1BQU07c0RBQUM7Ozs7OztzREFDYiw4REFBQ2xMO3NEQUFLOzs7Ozs7Ozs7Ozs7OENBR1IsOERBQUMrSjs7c0RBQ0MsOERBQUMvSjs0Q0FBS2tMLE1BQU07c0RBQUM7Ozs7OztzREFDYiw4REFBQ2xMO3NEQUFLOzs7Ozs7Ozs7Ozs7OENBR1IsOERBQUMrSjs7c0RBQ0MsOERBQUMvSjs0Q0FBS2tMLE1BQU07c0RBQUM7Ozs7OztzREFDYiw4REFBQ2xMO3NEQUFLOzs7Ozs7Ozs7Ozs7OENBR1IsOERBQUMrSjs7c0RBQ0MsOERBQUMvSjs0Q0FBS2tMLE1BQU07c0RBQUM7Ozs7OztzREFDYiw4REFBQ2xMO3NEQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FNWiw4REFBQ04sd0dBQUlBO3dCQUFDOEssT0FBTTt3QkFBMEJGLE1BQUs7a0NBQ3pDLDRFQUFDMUssd0dBQUtBOzRCQUFDeUssV0FBVTs7OENBQ2YsOERBQUNOOztzREFDQyw4REFBQy9KOzRDQUFLa0wsTUFBTTs0Q0FBQ2xCLE9BQU87Z0RBQUVtQixPQUFPOzRDQUFVO3NEQUFHOzs7Ozs7c0RBQzFDLDhEQUFDbkw7c0RBQUs7Ozs7Ozs7Ozs7Ozs4Q0FHUiw4REFBQytKOztzREFDQyw4REFBQy9KOzRDQUFLa0wsTUFBTTs0Q0FBQ2xCLE9BQU87Z0RBQUVtQixPQUFPOzRDQUFVO3NEQUFHOzs7Ozs7c0RBQzFDLDhEQUFDbkw7c0RBQUs7Ozs7Ozs7Ozs7Ozs4Q0FHUiw4REFBQytKOztzREFDQyw4REFBQy9KOzRDQUFLa0wsTUFBTTs0Q0FBQ2xCLE9BQU87Z0RBQUVtQixPQUFPOzRDQUFVO3NEQUFHOzs7Ozs7c0RBQzFDLDhEQUFDbkw7c0RBQUs7Ozs7Ozs7Ozs7Ozs4Q0FHUiw4REFBQytKOztzREFDQyw4REFBQy9KOzRDQUFLa0wsTUFBTTs0Q0FBQ2xCLE9BQU87Z0RBQUVtQixPQUFPOzRDQUFVO3NEQUFHOzs7Ozs7c0RBQzFDLDhEQUFDbkw7c0RBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQU1aLDhEQUFDTix3R0FBSUE7d0JBQUM4SyxPQUFNO3dCQUFZRixNQUFLO2tDQUMzQiw0RUFBQzFLLHdHQUFLQTs0QkFBQ3lLLFdBQVU7OzhDQUNmLDhEQUFDcks7O3dDQUFLO3dDQUFpQixLQUFrQixHQUFjOEMsVUFBVUMsU0FBUyxHQUFHOzs7Ozs7OzhDQUM3RSw4REFBQy9DOzt3Q0FBSzt3Q0FBTyxJQUFJaUIsT0FBTzZDLFdBQVc7Ozs7Ozs7OENBQ25DLDhEQUFDOUQ7O3dDQUFLO3dDQUFRLEtBQWtCLEdBQWM0QixPQUFPQyxRQUFRLENBQUNDLElBQUksR0FBRzs7Ozs7OztnQ0FDcEUsS0FBa0Isa0JBQ2pCLDhEQUFDOUI7b0NBQUtnSyxPQUFPO3dDQUNYbUIsT0FBT3ZKLE9BQU9DLFFBQVEsQ0FBQ0csUUFBUSxDQUFDVSxRQUFRLENBQUMsWUFBWWQsT0FBT0MsUUFBUSxDQUFDRyxRQUFRLENBQUNVLFFBQVEsQ0FBQyxZQUFZLFlBQVk7d0NBQy9Hc0osWUFBWTtvQ0FDZDs7d0NBQUc7d0NBRUNwSyxPQUFPQyxRQUFRLENBQUNHLFFBQVEsQ0FBQ1UsUUFBUSxDQUFDLFlBQVlkLE9BQU9DLFFBQVEsQ0FBQ0csUUFBUSxDQUFDVSxRQUFRLENBQUMsWUFDNUUsa0NBQ0E7Ozs7Ozs7Ozs7Ozs7Ozs7OztvQkFRYixLQUFrQixJQUFlLENBQUNkLE9BQU9DLFFBQVEsQ0FBQ0csUUFBUSxDQUFDVSxRQUFRLENBQUMsWUFBWSxDQUFDZCxPQUFPQyxRQUFRLENBQUNHLFFBQVEsQ0FBQ1UsUUFBUSxDQUFDLDJCQUNsSCw4REFBQ2hELHdHQUFJQTt3QkFBQzhLLE9BQU07d0JBQWlCRixNQUFLO3dCQUFRTixPQUFPOzRCQUFFWSxhQUFhO3dCQUFVO2tDQUN4RSw0RUFBQ2hMLHdHQUFLQTs0QkFBQ3lLLFdBQVU7OzhDQUNmLDhEQUFDcks7b0NBQUtrTCxNQUFNO29DQUFDbEIsT0FBTzt3Q0FBRW1CLE9BQU87b0NBQVU7OENBQUc7Ozs7Ozs4Q0FDMUMsOERBQUNwQjtvQ0FBSUMsT0FBTzt3Q0FBRVcsWUFBWTt3Q0FBV1YsU0FBUzt3Q0FBUWdCLGNBQWM7d0NBQU9ELFFBQVE7b0NBQW9COztzREFDckcsOERBQUNoTDs0Q0FBS2lNLElBQUk7c0RBQUM7Ozs7OztzREFBdUMsOERBQUNDOzs7OztzREFDbkQsOERBQUNsTTs0Q0FBS2lNLElBQUk7c0RBQUM7Ozs7OztzREFBK0IsOERBQUNDOzs7OztzREFDM0MsOERBQUNsTTs0Q0FBS2lNLElBQUk7c0RBQUM7Ozs7OztzREFBd0IsOERBQUNDOzs7OztzREFDcEMsOERBQUNsTTs0Q0FBS2lNLElBQUk7c0RBQUM7Ozs7Ozs7Ozs7Ozs4Q0FFYiw4REFBQ2pNO29DQUFLbUcsTUFBSzs4Q0FBWTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFRckM7R0F0aUJ3QmpHO0tBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL2FwcC90ZXN0LWlwL3BhZ2UudHN4PzQyMjgiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBCdXR0b24sIENhcmQsIFR5cG9ncmFwaHksIFNwYWNlLCBEaXZpZGVyLCBUYWJzIH0gZnJvbSAnYW50ZCc7XG5pbXBvcnQgcmVxdWVzdCBmcm9tICdAL2xpYi9yZXF1ZXN0JztcbmltcG9ydCBUdW5uZWxEZWJ1Z0NvbXBvbmVudCBmcm9tICcuL3R1bm5lbC1kZWJ1Zyc7XG5cbmNvbnN0IHsgVGl0bGUsIFRleHQsIFBhcmFncmFwaCB9ID0gVHlwb2dyYXBoeTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gVGVzdElQUGFnZSgpIHtcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbcmVzdWx0LCBzZXRSZXN1bHRdID0gdXNlU3RhdGU8YW55PihudWxsKTtcbiAgY29uc3QgW3JlYWxQdWJsaWNJUCwgc2V0UmVhbFB1YmxpY0lQXSA9IHVzZVN0YXRlPHN0cmluZz4oJycpO1xuICBjb25zdCBbbG9ncywgc2V0TG9nc10gPSB1c2VTdGF0ZTxzdHJpbmdbXT4oW10pO1xuICBjb25zdCBbZGVidWdJbmZvLCBzZXREZWJ1Z0luZm9dID0gdXNlU3RhdGU8YW55PihudWxsKTtcblxuICAvLyDmt7vliqDml6Xlv5fnmoTovoXliqnlh73mlbBcbiAgY29uc3QgYWRkTG9nID0gKG1lc3NhZ2U6IHN0cmluZywgZGF0YT86IGFueSkgPT4ge1xuICAgIGNvbnN0IHRpbWVzdGFtcCA9IG5ldyBEYXRlKCkudG9Mb2NhbGVUaW1lU3RyaW5nKCk7XG4gICAgY29uc3QgbG9nRW50cnkgPSBgWyR7dGltZXN0YW1wfV0gJHttZXNzYWdlfWA7XG5cbiAgICAvLyDovpPlh7rliLDmjqfliLblj7BcbiAgICBpZiAoZGF0YSkge1xuICAgICAgY29uc29sZS5sb2cobWVzc2FnZSwgZGF0YSk7XG4gICAgfSBlbHNlIHtcbiAgICAgIGNvbnNvbGUubG9nKG1lc3NhZ2UpO1xuICAgIH1cblxuICAgIC8vIOa3u+WKoOWIsOmhtemdouaXpeW/l1xuICAgIHNldExvZ3MocHJldiA9PiBbLi4ucHJldi5zbGljZSgtMTkpLCBsb2dFbnRyeV0pOyAvLyDkv53nlZnmnIDov5EyMOadoeaXpeW/l1xuICB9O1xuXG4gIC8vIOa4heepuuaXpeW/l1xuICBjb25zdCBjbGVhckxvZ3MgPSAoKSA9PiB7XG4gICAgc2V0TG9ncyhbXSk7XG4gICAgY29uc29sZS5jbGVhcigpO1xuICAgIGFkZExvZygn8J+nuSBbRnJvbnRlbmRdIOaXpeW/l+W3sua4heepuicpO1xuICB9O1xuXG4gIC8vIOmhtemdouWKoOi9veaXtui+k+WHuueOr+Wig+S/oeaBr1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGFkZExvZygn8J+MkCBbRnJvbnRlbmQgSW5pdF0g5rWL6K+V6aG16Z2i5Yid5aeL5YyWLi4uJyk7XG4gICAgY29uc29sZS5sb2coJ/CflqXvuI8gW0Zyb250ZW5kIEluaXRdIOa1j+iniOWZqOeOr+Wig+S/oeaBrzonLCB7XG4gICAgICDpobXpnaLkv6Hmga86IHtcbiAgICAgICAgVVJMOiB3aW5kb3cubG9jYXRpb24uaHJlZixcbiAgICAgICAg5Z+f5ZCNOiB3aW5kb3cubG9jYXRpb24uaG9zdG5hbWUsXG4gICAgICAgIOerr+WPozogd2luZG93LmxvY2F0aW9uLnBvcnQsXG4gICAgICAgIOWNj+iurjogd2luZG93LmxvY2F0aW9uLnByb3RvY29sLFxuICAgICAgICDot6/lvoQ6IHdpbmRvdy5sb2NhdGlvbi5wYXRobmFtZSxcbiAgICAgIH0sXG4gICAgICDorr/pl67mlrnlvI86IHtcbiAgICAgICAg5piv5ZCm5pys5Zyw6K6/6ZeuOiB3aW5kb3cubG9jYXRpb24uaG9zdG5hbWUgPT09ICdsb2NhbGhvc3QnLFxuICAgICAgICDmmK/lkKblhoXnvZHnqb/pgI86IHdpbmRvdy5sb2NhdGlvbi5ob3N0bmFtZS5pbmNsdWRlcygnbmdyb2snKSB8fCB3aW5kb3cubG9jYXRpb24uaG9zdG5hbWUuaW5jbHVkZXMoJ3R1bm5lbCcpLFxuICAgICAgICDorr/pl67nsbvlnos6IHdpbmRvdy5sb2NhdGlvbi5ob3N0bmFtZSA9PT0gJ2xvY2FsaG9zdCcgPyAn5pys5Zyw5byA5Y+RJyA6XG4gICAgICAgICAgICAgICAgICB3aW5kb3cubG9jYXRpb24uaG9zdG5hbWUuaW5jbHVkZXMoJ25ncm9rJykgPyAnbmdyb2vnqb/pgI8nIDpcbiAgICAgICAgICAgICAgICAgIHdpbmRvdy5sb2NhdGlvbi5ob3N0bmFtZS5pbmNsdWRlcygndHVubmVsJykgPyAn5YW25LuW56m/6YCPJyA6ICfmnKrnn6UnXG4gICAgICB9LFxuICAgICAg5rWP6KeI5Zmo5L+h5oGvOiB7XG4gICAgICAgIOeUqOaIt+S7o+eQhjogbmF2aWdhdG9yLnVzZXJBZ2VudCxcbiAgICAgICAg6K+t6KiAOiBuYXZpZ2F0b3IubGFuZ3VhZ2UsXG4gICAgICAgIOW5s+WPsDogbmF2aWdhdG9yLnBsYXRmb3JtLFxuICAgICAgICDlnKjnur/nirbmgIE6IG5hdmlnYXRvci5vbkxpbmUsXG4gICAgICB9LFxuICAgICAg572R57uc5L+h5oGvOiB7XG4gICAgICAgIOi/nuaOpeexu+WeizogKG5hdmlnYXRvciBhcyBhbnkpLmNvbm5lY3Rpb24/LmVmZmVjdGl2ZVR5cGUgfHwgJ+acquefpScsXG4gICAgICAgIOe9kee7nOeKtuaAgTogKG5hdmlnYXRvciBhcyBhbnkpLmNvbm5lY3Rpb24/LmRvd25saW5rID8gYCR7KG5hdmlnYXRvciBhcyBhbnkpLmNvbm5lY3Rpb24uZG93bmxpbmt9TWJwc2AgOiAn5pyq55+lJyxcbiAgICAgIH0sXG4gICAgICDml7bpl7Tkv6Hmga86IHtcbiAgICAgICAg5pys5Zyw5pe26Ze0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICAgIOaXtuWMujogSW50bC5EYXRlVGltZUZvcm1hdCgpLnJlc29sdmVkT3B0aW9ucygpLnRpbWVab25lLFxuICAgICAgICDml7bljLrlgY/np7s6IG5ldyBEYXRlKCkuZ2V0VGltZXpvbmVPZmZzZXQoKSxcbiAgICAgIH1cbiAgICB9KTtcblxuICAgIC8vIOi+k+WHuumihOacn+eahOa1i+ivlea1geeoi1xuICAgIGNvbnNvbGUubG9nKCfwn5OLIFtGcm9udGVuZCBJbml0XSDmtYvor5XmtYHnqIvor7TmmI46Jywge1xuICAgICAg5rWL6K+V55uu5qCHOiAnSVDlnLDlnYDojrflj5blkozkvKDpgJLlip/og73pqozor4EnLFxuICAgICAg5rWL6K+V5q2l6aqkOiBbXG4gICAgICAgICcxLiDojrflj5bnnJ/lrp7lhaznvZFJUCAo6YCa6L+H56ys5LiJ5pa5QVBJKScsXG4gICAgICAgICcyLiDmtYvor5XlvZPliY1JUOS9jee9ruiOt+WPliAo5ZCO56uvQVBJKScsXG4gICAgICAgICczLiDmtYvor5XmjIflrppJUOafpeivoiAoOC44LjguOCknLFxuICAgICAgICAnNC4g5rWL6K+V55m75b2VSVDorrDlvZUgKOaooeaLn+eZu+W9leWksei0pSknXG4gICAgICBdLFxuICAgICAg6KeC5a+f6KaB54K5OiBbXG4gICAgICAgICfliY3nq6/or7fmsYLml6Xlv5cgKPCfk6QgW0Zyb250ZW5kIFJlcXVlc3RdKScsXG4gICAgICAgICfliY3nq6/lk43lupTml6Xlv5cgKPCfk6UgW0Zyb250ZW5kIFJlc3BvbnNlXSknLFxuICAgICAgICAn5Lit6Ze05Lu2SVDlpITnkIYgKPCfjJAgW01pZGRsZXdhcmVdKScsXG4gICAgICAgICflkI7nq69JUOaPkOWPliAo8J+Wpe+4jyBbQmFja2VuZF0pJyxcbiAgICAgICAgJ+eZu+W9lUlQ6K6w5b2VICjwn5SQIFtMb2dpbkxvZ10pJ1xuICAgICAgXSxcbiAgICAgIOmihOacn+e7k+aenDoge1xuICAgICAgICDmnKzlnLDorr/pl646ICdJUOS4uuaooeaLn+WAvCAoMjIwLjE4MS4zOC4xNDgpJyxcbiAgICAgICAg56m/6YCP6K6/6ZeuOiAnSVDkuLrnnJ/lrp7lhaznvZFJUCcsXG4gICAgICAgIOWcsOeQhuS9jee9rjogJ+agueaNrklQ6Kej5p6Q5Ye65a+55bqU5L2N572uJ1xuICAgICAgfVxuICAgIH0pO1xuICB9LCBbXSk7XG5cbiAgLy8g6I635Y+W55yf5a6e55qE5YWs572RSVDlnLDlnYBcbiAgY29uc3QgZ2V0UmVhbFB1YmxpY0lQID0gYXN5bmMgKCkgPT4ge1xuICAgIHNldExvYWRpbmcodHJ1ZSk7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnNvbGUubG9nKCfwn4yNIFtGcm9udGVuZF0g5byA5aeL6I635Y+W55yf5a6e5YWs572RSVAuLi4nKTtcblxuICAgICAgLy8g5L2/55So5aSa5LiqSVDmn6Xor6LmnI3liqHvvIzmj5Dpq5jmiJDlip/njodcbiAgICAgIGNvbnN0IGlwU2VydmljZXMgPSBbXG4gICAgICAgICdodHRwczovL2FwaS5pcGlmeS5vcmc/Zm9ybWF0PWpzb24nLFxuICAgICAgICAnaHR0cHM6Ly9pcGFwaS5jby9qc29uLycsXG4gICAgICAgICdodHRwczovL2h0dHBiaW4ub3JnL2lwJyxcbiAgICAgICAgJ2h0dHBzOi8vYXBpLmlwLnNiL2lwJyxcbiAgICAgICAgJ2h0dHBzOi8vaWZjb25maWcubWUvaXAnLFxuICAgICAgICAnaHR0cHM6Ly9pY2FuaGF6aXAuY29tJ1xuICAgICAgXTtcblxuICAgICAgZm9yIChjb25zdCBzZXJ2aWNlIG9mIGlwU2VydmljZXMpIHtcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICBjb25zb2xlLmxvZyhg8J+UjSDlsJ3or5Xojrflj5blhaznvZFJUDogJHtzZXJ2aWNlfWApO1xuXG4gICAgICAgICAgY29uc3QgY29udHJvbGxlciA9IG5ldyBBYm9ydENvbnRyb2xsZXIoKTtcbiAgICAgICAgICBjb25zdCB0aW1lb3V0SWQgPSBzZXRUaW1lb3V0KCgpID0+IGNvbnRyb2xsZXIuYWJvcnQoKSwgNTAwMCk7XG5cbiAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKHNlcnZpY2UsIHtcbiAgICAgICAgICAgIG1ldGhvZDogJ0dFVCcsXG4gICAgICAgICAgICBzaWduYWw6IGNvbnRyb2xsZXIuc2lnbmFsXG4gICAgICAgICAgfSk7XG5cbiAgICAgICAgICBjbGVhclRpbWVvdXQodGltZW91dElkKTtcblxuICAgICAgICAgIGlmICghcmVzcG9uc2Uub2spIGNvbnRpbnVlO1xuXG4gICAgICAgICAgbGV0IGRhdGE7XG4gICAgICAgICAgY29uc3QgY29udGVudFR5cGUgPSByZXNwb25zZS5oZWFkZXJzLmdldCgnY29udGVudC10eXBlJyk7XG5cbiAgICAgICAgICBpZiAoY29udGVudFR5cGUgJiYgY29udGVudFR5cGUuaW5jbHVkZXMoJ2FwcGxpY2F0aW9uL2pzb24nKSkge1xuICAgICAgICAgICAgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgICAgICAgIGNvbnN0IGlwID0gZGF0YS5pcCB8fCBkYXRhLm9yaWdpbiB8fCBkYXRhLnF1ZXJ5O1xuICAgICAgICAgICAgaWYgKGlwICYmIGlzVmFsaWRJUEFkZHJlc3MoaXApKSB7XG4gICAgICAgICAgICAgIGNvbnNvbGUubG9nKGDinIUg5oiQ5Yqf6I635Y+W5YWs572RSVA6ICR7aXB9ICjmnaXmupA6ICR7c2VydmljZX0pYCk7XG4gICAgICAgICAgICAgIHNldFJlYWxQdWJsaWNJUChpcCk7XG4gICAgICAgICAgICAgIHNldFJlc3VsdCh7XG4gICAgICAgICAgICAgICAgdHlwZTogJ3JlYWwtcHVibGljLWlwJyxcbiAgICAgICAgICAgICAgICBpcDogaXAsXG4gICAgICAgICAgICAgICAgc291cmNlOiBzZXJ2aWNlLFxuICAgICAgICAgICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIGNvbnN0IHRleHQgPSBhd2FpdCByZXNwb25zZS50ZXh0KCk7XG4gICAgICAgICAgICBjb25zdCBpcCA9IHRleHQudHJpbSgpO1xuICAgICAgICAgICAgaWYgKGlzVmFsaWRJUEFkZHJlc3MoaXApKSB7XG4gICAgICAgICAgICAgIGNvbnNvbGUubG9nKGDinIUg5oiQ5Yqf6I635Y+W5YWs572RSVA6ICR7aXB9ICjmnaXmupA6ICR7c2VydmljZX0pYCk7XG4gICAgICAgICAgICAgIHNldFJlYWxQdWJsaWNJUChpcCk7XG4gICAgICAgICAgICAgIHNldFJlc3VsdCh7XG4gICAgICAgICAgICAgICAgdHlwZTogJ3JlYWwtcHVibGljLWlwJyxcbiAgICAgICAgICAgICAgICBpcDogaXAsXG4gICAgICAgICAgICAgICAgc291cmNlOiBzZXJ2aWNlLFxuICAgICAgICAgICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfVxuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgIGNvbnNvbGUubG9nKGDinYwg6I635Y+W5YWs572RSVDlpLHotKU6ICR7c2VydmljZX0gLSAke2Vycm9yfWApO1xuICAgICAgICAgIGNvbnRpbnVlO1xuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIHRocm93IG5ldyBFcnJvcign5omA5pyJ5YWs572RSVDmnI3liqHpg73ml6Dms5Xorr/pl64nKTtcbiAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfinYwgW0Zyb250ZW5kXSDojrflj5bnnJ/lrp7lhaznvZFJUOWksei0pTonLCBlcnJvcik7XG4gICAgICBzZXRSZXN1bHQoe1xuICAgICAgICB0eXBlOiAnZXJyb3InLFxuICAgICAgICBlcnJvcjogZXJyb3IubWVzc2FnZSxcbiAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICAgIH0pO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgLy8g6aqM6K+BSVDlnLDlnYDmoLzlvI9cbiAgY29uc3QgaXNWYWxpZElQQWRkcmVzcyA9IChpcDogc3RyaW5nKTogYm9vbGVhbiA9PiB7XG4gICAgY29uc3QgaXB2NFJlZ2V4ID0gL14oPzooPzoyNVswLTVdfDJbMC00XVswLTldfFswMV0/WzAtOV1bMC05XT8pXFwuKXszfSg/OjI1WzAtNV18MlswLTRdWzAtOV18WzAxXT9bMC05XVswLTldPykkLztcbiAgICByZXR1cm4gaXB2NFJlZ2V4LnRlc3QoaXApO1xuICB9O1xuXG4gIC8vIOa1i+ivleiOt+WPluW9k+WJjUlQ5L2N572uXG4gIGNvbnN0IHRlc3RDdXJyZW50SVAgPSBhc3luYyAoKSA9PiB7XG4gICAgc2V0TG9hZGluZyh0cnVlKTtcbiAgICB0cnkge1xuICAgICAgYWRkTG9nKCfwn6eqIFtGcm9udGVuZCBUZXN0XSDlvIDlp4vmtYvor5XlvZPliY1JUOiOt+WPli4uLicpO1xuICAgICAgYWRkTG9nKCfwn4yQIFtGcm9udGVuZCBUZXN0XSDlvZPliY3njq/looPkv6Hmga86Jywge1xuICAgICAgICDpobXpnaJVUkw6IHdpbmRvdy5sb2NhdGlvbi5ocmVmLFxuICAgICAgICDln5/lkI06IHdpbmRvdy5sb2NhdGlvbi5ob3N0bmFtZSxcbiAgICAgICAg5piv5ZCm5YaF572R56m/6YCPOiB3aW5kb3cubG9jYXRpb24uaG9zdG5hbWUuaW5jbHVkZXMoJ25ncm9rJykgfHwgd2luZG93LmxvY2F0aW9uLmhvc3RuYW1lLmluY2x1ZGVzKCd0dW5uZWwnKSxcbiAgICAgICAg55So5oi35Luj55CGOiBuYXZpZ2F0b3IudXNlckFnZW50LnN1YnN0cmluZygwLCAxMDApICsgJy4uLicsXG4gICAgICAgIOaXtumXtOaIszogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgICB9KTtcblxuICAgICAgY29uc29sZS5sb2coJ/Cfk6QgW0Zyb250ZW5kIFRlc3RdIOWHhuWkh+WPkemAgeivt+axguWIsDogL2FwaS92MS9pcC1sb2NhdGlvbi9jdXJyZW50Jyk7XG4gICAgICBjb25zdCBzdGFydFRpbWUgPSBEYXRlLm5vdygpO1xuXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHJlcXVlc3QuZ2V0KCcvYXBpL3YxL2lwLWxvY2F0aW9uL2N1cnJlbnQnKTtcblxuICAgICAgY29uc3QgZW5kVGltZSA9IERhdGUubm93KCk7XG4gICAgICBjb25zb2xlLmxvZygn8J+TpSBbRnJvbnRlbmQgVGVzdF0g6K+35rGC5a6M5oiQOicsIHtcbiAgICAgICAg6ICX5pe2OiBgJHtlbmRUaW1lIC0gc3RhcnRUaW1lfW1zYCxcbiAgICAgICAg5ZON5bqU54q25oCBOiByZXNwb25zZS5zdGF0dXMsXG4gICAgICAgIOWTjeW6lOaVsOaNrjogcmVzcG9uc2UuZGF0YSxcbiAgICAgICAg5pe26Ze05oizOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICAgIH0pO1xuXG4gICAgICBzZXRSZXN1bHQoe1xuICAgICAgICB0eXBlOiAnY3VycmVudC1pcCcsXG4gICAgICAgIGRhdGE6IHJlc3BvbnNlLmRhdGEsXG4gICAgICAgIHJlcXVlc3RUaW1lOiBlbmRUaW1lIC0gc3RhcnRUaW1lLFxuICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgfSk7XG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgY29uc29sZS5lcnJvcign4p2MIFtGcm9udGVuZCBUZXN0XSDlvZPliY1JUOa1i+ivleWksei0pTonLCB7XG4gICAgICAgIOmUmeivr+exu+WeizogZXJyb3IubmFtZSxcbiAgICAgICAg6ZSZ6K+v5raI5oGvOiBlcnJvci5tZXNzYWdlLFxuICAgICAgICDlk43lupTnirbmgIE6IGVycm9yLnJlc3BvbnNlPy5zdGF0dXMsXG4gICAgICAgIOWTjeW6lOaVsOaNrjogZXJyb3IucmVzcG9uc2U/LmRhdGEsXG4gICAgICAgIOWujOaVtOmUmeivrzogZXJyb3IsXG4gICAgICAgIOaXtumXtOaIszogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgICB9KTtcbiAgICAgIHNldFJlc3VsdCh7XG4gICAgICAgIHR5cGU6ICdlcnJvcicsXG4gICAgICAgIGVycm9yOiBlcnJvci5tZXNzYWdlLFxuICAgICAgICByZXNwb25zZTogZXJyb3IucmVzcG9uc2U/LmRhdGEsXG4gICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgICB9KTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIC8vIOa1i+ivleafpeivouaMh+WumklQXG4gIGNvbnN0IHRlc3RRdWVyeUlQID0gYXN5bmMgKCkgPT4ge1xuICAgIHNldExvYWRpbmcodHJ1ZSk7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHRlc3RJUCA9ICc4LjguOC44JzsgLy8gR29vZ2xlIEROU1xuICAgICAgY29uc29sZS5sb2coJ/Cfp6ogW0Zyb250ZW5kIFRlc3RdIOW8gOWni+a1i+ivlUlQ5p+l6K+iLi4uJyk7XG4gICAgICBjb25zb2xlLmxvZygn8J+OryBbRnJvbnRlbmQgVGVzdF0g5p+l6K+i5Y+C5pWwOicsIHtcbiAgICAgICAg55uu5qCHSVA6IHRlc3RJUCxcbiAgICAgICAgSVDnsbvlnos6ICdHb29nbGUgRE5T5pyN5Yqh5ZmoJyxcbiAgICAgICAg6aKE5pyf5L2N572uOiAn576O5Zu9JyxcbiAgICAgICAg5YyF5ZCr6aOO6Zmp6K+E5LywOiBmYWxzZSxcbiAgICAgICAg5pe26Ze05oizOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICAgIH0pO1xuXG4gICAgICBjb25zdCBxdWVyeVVybCA9IGAvYXBpL3YxL2lwLWxvY2F0aW9uL3F1ZXJ5P2lwPSR7dGVzdElQfSZpbmNsdWRlUmlzaz1mYWxzZWA7XG4gICAgICBjb25zb2xlLmxvZygn8J+TpCBbRnJvbnRlbmQgVGVzdF0g5YeG5aSH5Y+R6YCB5p+l6K+i6K+35rGCOicsIHF1ZXJ5VXJsKTtcblxuICAgICAgY29uc3Qgc3RhcnRUaW1lID0gRGF0ZS5ub3coKTtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgcmVxdWVzdC5nZXQocXVlcnlVcmwpO1xuICAgICAgY29uc3QgZW5kVGltZSA9IERhdGUubm93KCk7XG5cbiAgICAgIGNvbnNvbGUubG9nKCfwn5OlIFtGcm9udGVuZCBUZXN0XSBJUOafpeivouWujOaIkDonLCB7XG4gICAgICAgIOafpeivoklQOiB0ZXN0SVAsXG4gICAgICAgIOiAl+aXtjogYCR7ZW5kVGltZSAtIHN0YXJ0VGltZX1tc2AsXG4gICAgICAgIOWTjeW6lOeKtuaAgTogcmVzcG9uc2Uuc3RhdHVzLFxuICAgICAgICDlnLDnkIbkvY3nva46IHJlc3BvbnNlLmRhdGEgPyB7XG4gICAgICAgICAg5Zu95a62OiByZXNwb25zZS5kYXRhLmNvdW50cnksXG4gICAgICAgICAg55yB5Lu9OiByZXNwb25zZS5kYXRhLnByb3ZpbmNlLFxuICAgICAgICAgIOWfjuW4gjogcmVzcG9uc2UuZGF0YS5jaXR5LFxuICAgICAgICAgIOi/kOiQpeWVhjogcmVzcG9uc2UuZGF0YS5pc3AsXG4gICAgICAgICAg572u5L+h5bqmOiByZXNwb25zZS5kYXRhLmNvbmZpZGVuY2VcbiAgICAgICAgfSA6ICfml6DmlbDmja4nLFxuICAgICAgICDlrozmlbTlk43lupQ6IHJlc3BvbnNlLmRhdGEsXG4gICAgICAgIOaXtumXtOaIszogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgICB9KTtcblxuICAgICAgc2V0UmVzdWx0KHtcbiAgICAgICAgdHlwZTogJ3F1ZXJ5LWlwJyxcbiAgICAgICAgdGVzdElQLFxuICAgICAgICBkYXRhOiByZXNwb25zZS5kYXRhLFxuICAgICAgICByZXF1ZXN0VGltZTogZW5kVGltZSAtIHN0YXJ0VGltZSxcbiAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICAgIH0pO1xuICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBbRnJvbnRlbmQgVGVzdF0gSVDmn6Xor6LlpLHotKU6Jywge1xuICAgICAgICDmn6Xor6JJUDogJzguOC44LjgnLFxuICAgICAgICDplJnor6/nsbvlnos6IGVycm9yLm5hbWUsXG4gICAgICAgIOmUmeivr+a2iOaBrzogZXJyb3IubWVzc2FnZSxcbiAgICAgICAg5ZON5bqU54q25oCBOiBlcnJvci5yZXNwb25zZT8uc3RhdHVzLFxuICAgICAgICDlk43lupTmlbDmja46IGVycm9yLnJlc3BvbnNlPy5kYXRhLFxuICAgICAgICDlj6/og73ljp/lm6A6IFsn5ZCO56uv5pyN5Yqh5pyq5ZCv5YqoJywgJ0lQ6Kej5p6Q5pyN5Yqh5byC5bi4JywgJ+e9kee7nOi/nuaOpemXrumimCddLFxuICAgICAgICDml7bpl7TmiLM6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgfSk7XG4gICAgICBzZXRSZXN1bHQoe1xuICAgICAgICB0eXBlOiAnZXJyb3InLFxuICAgICAgICBlcnJvcjogZXJyb3IubWVzc2FnZSxcbiAgICAgICAgcmVzcG9uc2U6IGVycm9yLnJlc3BvbnNlPy5kYXRhLFxuICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgfSk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICAvLyDmtYvor5XnmbvlvZXmjqXlj6PvvIjop4Llr59JUOaXpeW/l++8iVxuICBjb25zdCB0ZXN0TG9naW5JUCA9IGFzeW5jICgpID0+IHtcbiAgICBzZXRMb2FkaW5nKHRydWUpO1xuICAgIHRyeSB7XG4gICAgICBjb25zb2xlLmxvZygn8J+nqiBbRnJvbnRlbmQgVGVzdF0g5byA5aeL5rWL6K+V55m75b2VSVDojrflj5YuLi4nKTtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5SQIFtGcm9udGVuZCBUZXN0XSDnmbvlvZXmtYvor5Xor7TmmI46Jywge1xuICAgICAgICDnm67nmoQ6ICfop4Llr5/nmbvlvZXml7bnmoRJUOiOt+WPluWSjOiusOW9lei/h+eoiycsXG4gICAgICAgIOmihOacn+e7k+aenDogJ+eZu+W9leWksei0pe+8iOS9v+eUqOmUmeivr+WHreaNru+8iScsXG4gICAgICAgIOinguWvn+mHjeeCuTogWydJUOWcsOWdgOiOt+WPlicsICfnmbvlvZXml6Xlv5forrDlvZUnLCAn6ZSZ6K+v5aSE55CGJ10sXG4gICAgICAgIOa1i+ivleWHreaNrjoge1xuICAgICAgICAgIOaJi+acuuWPtzogJzEyMzQ1Njc4OTEwJyxcbiAgICAgICAgICDlr4bnoIE6ICcxMjM0NTYgKOmUmeivr+WvhueggSknXG4gICAgICAgIH0sXG4gICAgICAgIOaXtumXtOaIszogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgICB9KTtcblxuICAgICAgY29uc29sZS5sb2coJ/Cfk6QgW0Zyb250ZW5kIFRlc3RdIOWPkemAgeeZu+W9leivt+axgi4uLicpO1xuICAgICAgY29uc3Qgc3RhcnRUaW1lID0gRGF0ZS5ub3coKTtcblxuICAgICAgLy8g6L+Z6YeM5pWF5oSP5L2/55So6ZSZ6K+v55qE55m75b2V5L+h5oGv77yM5Y+q5piv5Li65LqG6Kem5Y+RSVDojrflj5bpgLvovpFcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgcmVxdWVzdC5wb3N0KCcvYXBpL3VzZXItYXV0aC9wYXNzd29yZCcsIHtcbiAgICAgICAgcGhvbmU6ICcxMjM0NTY3ODkwMScsXG4gICAgICAgIHBhc3N3b3JkOiAnMTIzNDU2J1xuICAgICAgfSk7XG5cbiAgICAgIGNvbnN0IGVuZFRpbWUgPSBEYXRlLm5vdygpO1xuICAgICAgY29uc29sZS5sb2coJ/Cfk6UgW0Zyb250ZW5kIFRlc3RdIOeZu+W9leWTjeW6lCAo5oSP5aSW5oiQ5YqfKTonLCB7XG4gICAgICAgIOiAl+aXtjogYCR7ZW5kVGltZSAtIHN0YXJ0VGltZX1tc2AsXG4gICAgICAgIOWTjeW6lOeKtuaAgTogcmVzcG9uc2Uuc3RhdHVzLFxuICAgICAgICDlk43lupTmlbDmja46IHJlc3BvbnNlLmRhdGEsXG4gICAgICAgIOazqOaEjzogJ+i/meS4jeW6lOivpeaIkOWKn++8jOivt+ajgOafpeWQjuerr+mqjOivgemAu+i+kScsXG4gICAgICAgIOaXtumXtOaIszogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgICB9KTtcblxuICAgICAgc2V0UmVzdWx0KHtcbiAgICAgICAgdHlwZTogJ2xvZ2luLXRlc3QnLFxuICAgICAgICBkYXRhOiByZXNwb25zZS5kYXRhLFxuICAgICAgICByZXF1ZXN0VGltZTogZW5kVGltZSAtIHN0YXJ0VGltZSxcbiAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICAgIH0pO1xuICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5OdIFtGcm9udGVuZCBUZXN0XSDnmbvlvZXmtYvor5XlrozmiJAgKOmihOacn+Wksei0pSk6Jywge1xuICAgICAgICDplJnor6/nsbvlnos6IGVycm9yLm5hbWUsXG4gICAgICAgIOmUmeivr+a2iOaBrzogZXJyb3IubWVzc2FnZSxcbiAgICAgICAg5ZON5bqU54q25oCBOiBlcnJvci5yZXNwb25zZT8uc3RhdHVzLFxuICAgICAgICDlk43lupTmlbDmja46IGVycm9yLnJlc3BvbnNlPy5kYXRhLFxuICAgICAgICDliIbmnpA6IHtcbiAgICAgICAgICDmmK/lkKbpooTmnJ/lpLHotKU6IHRydWUsXG4gICAgICAgICAg5aSx6LSl5Y6f5ZugOiAn5L2/55So5LqG6ZSZ6K+v55qE55m75b2V5Yet5o2uJyxcbiAgICAgICAgICBJUOiOt+WPlueKtuaAgTogJ+W6lOivpeW3suinpuWPkUlQ6I635Y+W5ZKM5pel5b+X6K6w5b2VJyxcbiAgICAgICAgICDlkI7nu63mo4Dmn6U6ICfmn6XnnIvlkI7nq6/mjqfliLblj7DnmoQgW0xvZ2luTG9nXSDml6Xlv5cnXG4gICAgICAgIH0sXG4gICAgICAgIOaXtumXtOaIszogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgICB9KTtcbiAgICAgIHNldFJlc3VsdCh7XG4gICAgICAgIHR5cGU6ICdsb2dpbi10ZXN0JyxcbiAgICAgICAgZXJyb3I6IGVycm9yLm1lc3NhZ2UsXG4gICAgICAgIHJlc3BvbnNlOiBlcnJvci5yZXNwb25zZT8uZGF0YSxcbiAgICAgICAgbm90ZTogJ+i/meaYr+mihOacn+eahOWksei0pe+8jOS4u+imgeeUqOS6juinguWvn0lQ6I635Y+W5pel5b+XJyxcbiAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICAgIH0pO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IHN0eWxlPXt7IHBhZGRpbmc6ICcyNHB4JywgbWF4V2lkdGg6ICcxMjAwcHgnLCBtYXJnaW46ICcwIGF1dG8nIH19PlxuICAgICAgPFRpdGxlIGxldmVsPXsyfT7wn6eqIElQ5Zyw5Z2A6I635Y+W5rWL6K+V6aG16Z2iPC9UaXRsZT5cbiAgICAgIFxuICAgICAgPFBhcmFncmFwaD5cbiAgICAgICAg6L+Z5Liq6aG16Z2i55So5LqO5rWL6K+V5YmN56uv5Yiw5ZCO56uv55qESVDlnLDlnYDkvKDpgJLlkozojrflj5blip/og73jgIJcbiAgICAgICAg6K+35omT5byA5rWP6KeI5Zmo5byA5Y+R6ICF5bel5YW355qE5o6n5Yi25Y+w77yM5Lul5Y+K5ZCO56uv5pyN5Yqh5Zmo55qE5pel5b+X77yM6KeC5a+fSVDojrflj5bov4fnqIvjgIJcbiAgICAgIDwvUGFyYWdyYXBoPlxuXG4gICAgICA8U3BhY2UgZGlyZWN0aW9uPVwidmVydGljYWxcIiBzaXplPVwibGFyZ2VcIiBzdHlsZT17eyB3aWR0aDogJzEwMCUnIH19PlxuICAgICAgICBcbiAgICAgICAgey8qIOa1i+ivleaMiemSruWMuuWfnyAqL31cbiAgICAgICAgPENhcmQgdGl0bGU9XCLwn46vIOa1i+ivleWKn+iDvVwiIHNpemU9XCJzbWFsbFwiPlxuICAgICAgICAgIDxTcGFjZSB3cmFwPlxuICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICB0eXBlPVwicHJpbWFyeVwiXG4gICAgICAgICAgICAgIGxvYWRpbmc9e2xvYWRpbmd9XG4gICAgICAgICAgICAgIG9uQ2xpY2s9e2dldFJlYWxQdWJsaWNJUH1cbiAgICAgICAgICAgICAgc3R5bGU9e3sgYmFja2dyb3VuZDogJyM1MmM0MWEnLCBib3JkZXJDb2xvcjogJyM1MmM0MWEnIH19XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIPCfjI0g6I635Y+W55yf5a6e5YWs572RSVBcbiAgICAgICAgICAgIDwvQnV0dG9uPlxuXG4gICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgIGxvYWRpbmc9e2xvYWRpbmd9XG4gICAgICAgICAgICAgIG9uQ2xpY2s9e3Rlc3RDdXJyZW50SVB9XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIOa1i+ivleiOt+WPluW9k+WJjUlQ5L2N572uXG4gICAgICAgICAgICA8L0J1dHRvbj5cblxuICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICBsb2FkaW5nPXtsb2FkaW5nfVxuICAgICAgICAgICAgICBvbkNsaWNrPXt0ZXN0UXVlcnlJUH1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAg5rWL6K+V5p+l6K+i5oyH5a6aSVAgKDguOC44LjgpXG4gICAgICAgICAgICA8L0J1dHRvbj5cblxuICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICBsb2FkaW5nPXtsb2FkaW5nfVxuICAgICAgICAgICAgICBvbkNsaWNrPXt0ZXN0TG9naW5JUH1cbiAgICAgICAgICAgICAgZGFuZ2VyXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIOa1i+ivleeZu+W9lUlQ6I635Y+WICjkvJrlpLHotKUpXG4gICAgICAgICAgICA8L0J1dHRvbj5cblxuICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXtjbGVhckxvZ3N9XG4gICAgICAgICAgICAgIHN0eWxlPXt7IG1hcmdpbkxlZnQ6ICcxMnB4JyB9fVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICDwn6e5IOa4heepuuaXpeW/l1xuICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgPC9TcGFjZT5cblxuICAgICAgICAgIHtyZWFsUHVibGljSVAgJiYgKFxuICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBtYXJnaW5Ub3A6ICcxMnB4JywgcGFkZGluZzogJzhweCcsIGJhY2tncm91bmQ6ICcjZjZmZmVkJywgYm9yZGVyOiAnMXB4IHNvbGlkICNiN2ViOGYnLCBib3JkZXJSYWRpdXM6ICc2cHgnIH19PlxuICAgICAgICAgICAgICA8VGV4dCBzdHJvbmcgc3R5bGU9e3sgY29sb3I6ICcjNTJjNDFhJyB9fT7wn4yNIOS9oOeahOecn+WunuWFrOe9kUlQOiB7cmVhbFB1YmxpY0lQfTwvVGV4dD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG4gICAgICAgIDwvQ2FyZD5cblxuICAgICAgICB7Lyog57uT5p6c5pi+56S65Yy65Z+fICovfVxuICAgICAgICB7cmVzdWx0ICYmIChcbiAgICAgICAgICA8Q2FyZCB0aXRsZT1cIvCfk4og5rWL6K+V57uT5p6cXCIgc2l6ZT1cInNtYWxsXCI+XG4gICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGJhY2tncm91bmQ6ICcjZjVmNWY1JywgcGFkZGluZzogJzE2cHgnLCBib3JkZXJSYWRpdXM6ICc2cHgnIH19PlxuICAgICAgICAgICAgICA8cHJlIHN0eWxlPXt7IG1hcmdpbjogMCwgZm9udFNpemU6ICcxMnB4JywgbGluZUhlaWdodDogJzEuNCcgfX0+XG4gICAgICAgICAgICAgICAge0pTT04uc3RyaW5naWZ5KHJlc3VsdCwgbnVsbCwgMil9XG4gICAgICAgICAgICAgIDwvcHJlPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9DYXJkPlxuICAgICAgICApfVxuXG4gICAgICAgIHsvKiDlrp7ml7bml6Xlv5fmmL7npLogKi99XG4gICAgICAgIDxDYXJkIHRpdGxlPVwi8J+TnSDlrp7ml7bliY3nq6/ml6Xlv5dcIiBzaXplPVwic21hbGxcIj5cbiAgICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgICBiYWNrZ3JvdW5kOiAnIzAwMCcsXG4gICAgICAgICAgICBjb2xvcjogJyMwMGZmMDAnLFxuICAgICAgICAgICAgcGFkZGluZzogJzEycHgnLFxuICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnNnB4JyxcbiAgICAgICAgICAgIGZvbnRGYW1pbHk6ICdNb25hY28sIENvbnNvbGFzLCBtb25vc3BhY2UnLFxuICAgICAgICAgICAgZm9udFNpemU6ICcxMnB4JyxcbiAgICAgICAgICAgIG1heEhlaWdodDogJzMwMHB4JyxcbiAgICAgICAgICAgIG92ZXJmbG93WTogJ2F1dG8nXG4gICAgICAgICAgfX0+XG4gICAgICAgICAgICB7bG9ncy5sZW5ndGggPT09IDAgPyAoXG4gICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgY29sb3I6ICcjNjY2JyB9fT7nrYnlvoXml6Xlv5fovpPlh7ouLi48L2Rpdj5cbiAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgIGxvZ3MubWFwKChsb2csIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgICAgPGRpdiBrZXk9e2luZGV4fSBzdHlsZT17eyBtYXJnaW5Cb3R0b206ICcycHgnIH19PlxuICAgICAgICAgICAgICAgICAge2xvZ31cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKSlcbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPGRpdiBzdHlsZT17eyBtYXJnaW5Ub3A6ICc4cHgnLCBmb250U2l6ZTogJzEycHgnLCBjb2xvcjogJyM2NjYnIH19PlxuICAgICAgICAgICAg8J+SoSDmj5DnpLrvvJrov5nph4zmmL7npLrliY3nq6/ml6Xlv5fvvIzlrozmlbTml6Xlv5for7fmn6XnnIvmtY/op4jlmajmjqfliLblj7BcbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9DYXJkPlxuXG4gICAgICAgIDxEaXZpZGVyIC8+XG5cbiAgICAgICAgey8qIOivtOaYjuS/oeaBryAqL31cbiAgICAgICAgPENhcmQgdGl0bGU9XCLwn5OLIOinguWvn+imgeeCuVwiIHNpemU9XCJzbWFsbFwiPlxuICAgICAgICAgIDxTcGFjZSBkaXJlY3Rpb249XCJ2ZXJ0aWNhbFwiPlxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPFRleHQgc3Ryb25nPvCfjJAg5YmN56uv5Lit6Ze05Lu25pel5b+X77yaPC9UZXh0PlxuICAgICAgICAgICAgICA8VGV4dD7mn6XnnIvmtY/op4jlmajmjqfliLblj7DvvIzop4Llr58gW01pZGRsZXdhcmVdIOagh+iusOeahOaXpeW/lzwvVGV4dD5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8VGV4dCBzdHJvbmc+8J+Wpe+4jyDlkI7nq69JUOaPkOWPluaXpeW/l++8mjwvVGV4dD5cbiAgICAgICAgICAgICAgPFRleHQ+5p+l55yL5ZCO56uv5o6n5Yi25Y+w77yM6KeC5a+fIFtCYWNrZW5kXSDmoIforrDnmoTml6Xlv5c8L1RleHQ+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPFRleHQgc3Ryb25nPvCflJAg55m75b2V5pel5b+X77yaPC9UZXh0PlxuICAgICAgICAgICAgICA8VGV4dD7mn6XnnIvlkI7nq6/mjqfliLblj7DvvIzop4Llr58gW0xvZ2luTG9nXSDmoIforrDnmoTml6Xlv5c8L1RleHQ+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPFRleHQgc3Ryb25nPvCflI0g6YeN54K56KeC5a+f77yaPC9UZXh0PlxuICAgICAgICAgICAgICA8VGV4dD5JUOWcsOWdgOaYr+WQpuS7juWJjeerr+ato+ehruS8oOmAkuWIsOWQjuerr++8jOS7peWPiuWQhOS4queOr+iKgueahElQ6I635Y+W5oOF5Ya1PC9UZXh0PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9TcGFjZT5cbiAgICAgICAgPC9DYXJkPlxuXG4gICAgICAgIHsvKiBJUOiOt+WPluWOn+eQhuivtOaYjiAqL31cbiAgICAgICAgPENhcmQgdGl0bGU9XCLwn6SUIOS4uuS7gOS5iOacrOWcsOW8gOWPkeiOt+WPluWIsDEyNy4wLjAuMe+8n1wiIHNpemU9XCJzbWFsbFwiPlxuICAgICAgICAgIDxTcGFjZSBkaXJlY3Rpb249XCJ2ZXJ0aWNhbFwiPlxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPFRleHQgc3Ryb25nIHN0eWxlPXt7IGNvbG9yOiAnI2ZhOGMxNicgfX0+8J+PoCDmnKzlnLDlvIDlj5Hnjq/looPvvJo8L1RleHQ+XG4gICAgICAgICAgICAgIDxUZXh0Pua1j+iniOWZqCDihpIgbG9jYWxob3N0OjMwMDAg4oaSIOWQjuerr0FQSe+8jOaJgOacieivt+axgumDveadpeiHquacrOacuu+8jOaJgOS7pUlQ5pivMTI3LjAuMC4xPC9UZXh0PlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxUZXh0IHN0cm9uZyBzdHlsZT17eyBjb2xvcjogJyM1MmM0MWEnIH19PvCfjI0g55Sf5Lqn546v5aKD77yaPC9UZXh0PlxuICAgICAgICAgICAgICA8VGV4dD7nlKjmiLfmtY/op4jlmagg4oaSIENETi/otJ/ovb3lnYfooaEg4oaSIFdlYuacjeWKoeWZqCDihpIg5ZCO56uvQVBJ77yM6IO96I635Y+W5Yiw55yf5a6e5YWs572RSVA8L1RleHQ+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPFRleHQgc3Ryb25nIHN0eWxlPXt7IGNvbG9yOiAnIzE4OTBmZicgfX0+8J+OrSDmqKHmi5/op6PlhrPmlrnmoYjvvJo8L1RleHQ+XG4gICAgICAgICAgICAgIDxUZXh0PuS4remXtOS7tuW3sumFjee9ruWcqOW8gOWPkeeOr+Wig+S9v+eUqOaooeaLn+WFrOe9kUlQICgyMjAuMTgxLjM4LjE0OCkg6L+b6KGM5rWL6K+VPC9UZXh0PlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxUZXh0IHN0cm9uZyBzdHlsZT17eyBjb2xvcjogJyM3MjJlZDEnIH19PvCfp6og55yf5a6eSVDlr7nmr5TvvJo8L1RleHQ+XG4gICAgICAgICAgICAgIDxUZXh0PueCueWHu1wi6I635Y+W55yf5a6e5YWs572RSVBcIuaMiemSru+8jOWvueavlOS9oOeahOecn+WunuWFrOe9kUlQ5ZKM5ZCO56uv6I635Y+W55qESVA8L1RleHQ+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L1NwYWNlPlxuICAgICAgICA8L0NhcmQ+XG5cbiAgICAgICAgey8qIOeOr+Wig+S/oeaBryAqL31cbiAgICAgICAgPENhcmQgdGl0bGU9XCLwn4yNIOW9k+WJjeeOr+Wig+S/oeaBr1wiIHNpemU9XCJzbWFsbFwiPlxuICAgICAgICAgIDxTcGFjZSBkaXJlY3Rpb249XCJ2ZXJ0aWNhbFwiPlxuICAgICAgICAgICAgPFRleHQ+5rWP6KeI5ZmoIFVzZXItQWdlbnQ6IHt0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJyA/IG5hdmlnYXRvci51c2VyQWdlbnQgOiAn5pyN5Yqh56uv5riy5p+TJ308L1RleHQ+XG4gICAgICAgICAgICA8VGV4dD7lvZPliY3ml7bpl7Q6IHtuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCl9PC9UZXh0PlxuICAgICAgICAgICAgPFRleHQ+6aG16Z2iVVJMOiB7dHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcgPyB3aW5kb3cubG9jYXRpb24uaHJlZiA6ICfmnI3liqHnq6/muLLmn5MnfTwvVGV4dD5cbiAgICAgICAgICAgIHt0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJyAmJiAoXG4gICAgICAgICAgICAgIDxUZXh0IHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgY29sb3I6IHdpbmRvdy5sb2NhdGlvbi5ob3N0bmFtZS5pbmNsdWRlcygnbmdyb2snKSB8fCB3aW5kb3cubG9jYXRpb24uaG9zdG5hbWUuaW5jbHVkZXMoJ3R1bm5lbCcpID8gJyM1MmM0MWEnIDogJyNmYThjMTYnLFxuICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6ICdib2xkJ1xuICAgICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgICDorr/pl67mlrnlvI86IHtcbiAgICAgICAgICAgICAgICAgIHdpbmRvdy5sb2NhdGlvbi5ob3N0bmFtZS5pbmNsdWRlcygnbmdyb2snKSB8fCB3aW5kb3cubG9jYXRpb24uaG9zdG5hbWUuaW5jbHVkZXMoJ3R1bm5lbCcpXG4gICAgICAgICAgICAgICAgICAgID8gJ/CfjJAg5YaF572R56m/6YCP6K6/6ZeuICjlj6/ojrflj5bnnJ/lrp5JUCknXG4gICAgICAgICAgICAgICAgICAgIDogJ/Cfj6Ag5pys5Zyw6K6/6ZeuICjmqKHmi59JUCknXG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICA8L1RleHQ+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvU3BhY2U+XG4gICAgICAgIDwvQ2FyZD5cblxuICAgICAgICB7Lyog5YaF572R56m/6YCP5oyH5Y2XICovfVxuICAgICAgICB7dHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcgJiYgIXdpbmRvdy5sb2NhdGlvbi5ob3N0bmFtZS5pbmNsdWRlcygnbmdyb2snKSAmJiAhd2luZG93LmxvY2F0aW9uLmhvc3RuYW1lLmluY2x1ZGVzKCd0dW5uZWwnKSAmJiAoXG4gICAgICAgICAgPENhcmQgdGl0bGU9XCLwn5qAIOaDs+imgea1i+ivleecn+WunklQ6I635Y+W77yfXCIgc2l6ZT1cInNtYWxsXCIgc3R5bGU9e3sgYm9yZGVyQ29sb3I6ICcjNTJjNDFhJyB9fT5cbiAgICAgICAgICAgIDxTcGFjZSBkaXJlY3Rpb249XCJ2ZXJ0aWNhbFwiPlxuICAgICAgICAgICAgICA8VGV4dCBzdHJvbmcgc3R5bGU9e3sgY29sb3I6ICcjNTJjNDFhJyB9fT7kvb/nlKjlhoXnvZHnqb/pgI/ojrflj5bnnJ/lrp5JUO+8mjwvVGV4dD5cbiAgICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBiYWNrZ3JvdW5kOiAnI2Y2ZmZlZCcsIHBhZGRpbmc6ICcxMnB4JywgYm9yZGVyUmFkaXVzOiAnNnB4JywgYm9yZGVyOiAnMXB4IHNvbGlkICNiN2ViOGYnIH19PlxuICAgICAgICAgICAgICAgIDxUZXh0IGNvZGU+MS4g5a6J6KOFbmdyb2s6IG5wbSBpbnN0YWxsIC1nIG5ncm9rPC9UZXh0Pjxici8+XG4gICAgICAgICAgICAgICAgPFRleHQgY29kZT4yLiDnqb/pgI/liY3nq686IG5ncm9rIGh0dHAgMzAwMDwvVGV4dD48YnIvPlxuICAgICAgICAgICAgICAgIDxUZXh0IGNvZGU+My4g6K6/6Zeubmdyb2vmj5DkvpvnmoTlhaznvZHlnLDlnYA8L1RleHQ+PGJyLz5cbiAgICAgICAgICAgICAgICA8VGV4dCBjb2RlPjQuIOmHjeaWsOa1i+ivlUlQ6I635Y+W5Yqf6IO9PC9UZXh0PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPFRleHQgdHlwZT1cInNlY29uZGFyeVwiPumAmui/h+WGhee9keepv+mAj++8jOS9oOWPr+S7peaooeaLn+ecn+WunueahOeUn+S6p+eOr+Wig++8jOiOt+WPluWIsOecn+WunueahOWFrOe9kUlQ5Zyw5Z2A77yBPC9UZXh0PlxuICAgICAgICAgICAgPC9TcGFjZT5cbiAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgICl9XG5cbiAgICAgIDwvU3BhY2U+XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJCdXR0b24iLCJDYXJkIiwiVHlwb2dyYXBoeSIsIlNwYWNlIiwiRGl2aWRlciIsInJlcXVlc3QiLCJUaXRsZSIsIlRleHQiLCJQYXJhZ3JhcGgiLCJUZXN0SVBQYWdlIiwibG9hZGluZyIsInNldExvYWRpbmciLCJyZXN1bHQiLCJzZXRSZXN1bHQiLCJyZWFsUHVibGljSVAiLCJzZXRSZWFsUHVibGljSVAiLCJsb2dzIiwic2V0TG9ncyIsImRlYnVnSW5mbyIsInNldERlYnVnSW5mbyIsImFkZExvZyIsIm1lc3NhZ2UiLCJkYXRhIiwidGltZXN0YW1wIiwiRGF0ZSIsInRvTG9jYWxlVGltZVN0cmluZyIsImxvZ0VudHJ5IiwiY29uc29sZSIsImxvZyIsInByZXYiLCJzbGljZSIsImNsZWFyTG9ncyIsImNsZWFyIiwi6aG16Z2i5L+h5oGvIiwiVVJMIiwid2luZG93IiwibG9jYXRpb24iLCJocmVmIiwi5Z+f5ZCNIiwiaG9zdG5hbWUiLCLnq6/lj6MiLCJwb3J0Iiwi5Y2P6K6uIiwicHJvdG9jb2wiLCLot6/lvoQiLCJwYXRobmFtZSIsIuiuv+mXruaWueW8jyIsIuaYr+WQpuacrOWcsOiuv+mXriIsIuaYr+WQpuWGhee9keepv+mAjyIsImluY2x1ZGVzIiwi6K6/6Zeu57G75Z6LIiwi5rWP6KeI5Zmo5L+h5oGvIiwi55So5oi35Luj55CGIiwibmF2aWdhdG9yIiwidXNlckFnZW50Iiwi6K+t6KiAIiwibGFuZ3VhZ2UiLCLlubPlj7AiLCJwbGF0Zm9ybSIsIuWcqOe6v+eKtuaAgSIsIm9uTGluZSIsIue9kee7nOS/oeaBryIsIui/nuaOpeexu+WeiyIsImNvbm5lY3Rpb24iLCJlZmZlY3RpdmVUeXBlIiwi572R57uc54q25oCBIiwiZG93bmxpbmsiLCLml7bpl7Tkv6Hmga8iLCLmnKzlnLDml7bpl7QiLCJ0b0lTT1N0cmluZyIsIuaXtuWMuiIsIkludGwiLCJEYXRlVGltZUZvcm1hdCIsInJlc29sdmVkT3B0aW9ucyIsInRpbWVab25lIiwi5pe25Yy65YGP56e7IiwiZ2V0VGltZXpvbmVPZmZzZXQiLCLmtYvor5Xnm67moIciLCLmtYvor5XmraXpqqQiLCLop4Llr5/opoHngrkiLCLpooTmnJ/nu5PmnpwiLCLmnKzlnLDorr/pl64iLCLnqb/pgI/orr/pl64iLCLlnLDnkIbkvY3nva4iLCJnZXRSZWFsUHVibGljSVAiLCJpcFNlcnZpY2VzIiwic2VydmljZSIsImNvbnRyb2xsZXIiLCJBYm9ydENvbnRyb2xsZXIiLCJ0aW1lb3V0SWQiLCJzZXRUaW1lb3V0IiwiYWJvcnQiLCJyZXNwb25zZSIsImZldGNoIiwibWV0aG9kIiwic2lnbmFsIiwiY2xlYXJUaW1lb3V0Iiwib2siLCJjb250ZW50VHlwZSIsImhlYWRlcnMiLCJnZXQiLCJqc29uIiwiaXAiLCJvcmlnaW4iLCJxdWVyeSIsImlzVmFsaWRJUEFkZHJlc3MiLCJ0eXBlIiwic291cmNlIiwidGV4dCIsInRyaW0iLCJlcnJvciIsIkVycm9yIiwiaXB2NFJlZ2V4IiwidGVzdCIsInRlc3RDdXJyZW50SVAiLCLpobXpnaJVUkwiLCJzdWJzdHJpbmciLCLml7bpl7TmiLMiLCJzdGFydFRpbWUiLCJub3ciLCJlbmRUaW1lIiwi6ICX5pe2Iiwi5ZON5bqU54q25oCBIiwic3RhdHVzIiwi5ZON5bqU5pWw5o2uIiwicmVxdWVzdFRpbWUiLCLplJnor6/nsbvlnosiLCJuYW1lIiwi6ZSZ6K+v5raI5oGvIiwi5a6M5pW06ZSZ6K+vIiwidGVzdFF1ZXJ5SVAiLCJ0ZXN0SVAiLCLnm67moIdJUCIsIklQ57G75Z6LIiwi6aKE5pyf5L2N572uIiwi5YyF5ZCr6aOO6Zmp6K+E5LywIiwicXVlcnlVcmwiLCLmn6Xor6JJUCIsIuWbveWutiIsImNvdW50cnkiLCLnnIHku70iLCJwcm92aW5jZSIsIuWfjuW4giIsImNpdHkiLCLov5DokKXllYYiLCJpc3AiLCLnva7kv6HluqYiLCJjb25maWRlbmNlIiwi5a6M5pW05ZON5bqUIiwi5Y+v6IO95Y6f5ZugIiwidGVzdExvZ2luSVAiLCLnm67nmoQiLCLop4Llr5/ph43ngrkiLCLmtYvor5Xlh63mja4iLCLmiYvmnLrlj7ciLCLlr4bnoIEiLCJwb3N0IiwicGhvbmUiLCJwYXNzd29yZCIsIuazqOaEjyIsIuWIhuaekCIsIuaYr+WQpumihOacn+Wksei0pSIsIuWksei0peWOn+WboCIsIklQ6I635Y+W54q25oCBIiwi5ZCO57ut5qOA5p+lIiwibm90ZSIsImRpdiIsInN0eWxlIiwicGFkZGluZyIsIm1heFdpZHRoIiwibWFyZ2luIiwibGV2ZWwiLCJkaXJlY3Rpb24iLCJzaXplIiwid2lkdGgiLCJ0aXRsZSIsIndyYXAiLCJvbkNsaWNrIiwiYmFja2dyb3VuZCIsImJvcmRlckNvbG9yIiwiZGFuZ2VyIiwibWFyZ2luTGVmdCIsIm1hcmdpblRvcCIsImJvcmRlciIsImJvcmRlclJhZGl1cyIsInN0cm9uZyIsImNvbG9yIiwicHJlIiwiZm9udFNpemUiLCJsaW5lSGVpZ2h0IiwiSlNPTiIsInN0cmluZ2lmeSIsImZvbnRGYW1pbHkiLCJtYXhIZWlnaHQiLCJvdmVyZmxvd1kiLCJsZW5ndGgiLCJtYXAiLCJpbmRleCIsIm1hcmdpbkJvdHRvbSIsImZvbnRXZWlnaHQiLCJjb2RlIiwiYnIiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/test-ip/page.tsx\n"));

/***/ })

});