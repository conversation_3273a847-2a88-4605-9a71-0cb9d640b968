2025-07-31 11:47:08.884 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-31 11:47:08.997 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:47:08.997 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:47:08.998 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:47:08.998 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:47:08.999 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:47:08.999 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:47:08.999 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:47:09.000 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:47:09.000 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:47:09.001 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:47:09.001 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:47:09.002 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:47:09.002 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:47:09.002 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:47:09.003 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:47:09.003 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:47:09.003 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:47:09.004 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:47:09.004 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:47:09.005 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:47:23.717 [ERROR] [GlobalExceptionFilter] GET /api/v1/ip-location/health - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (F:\logicleap2\logic-back\src\web\router_guard\router-guard.service.ts:116:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async ApiAuthGuard.canActivate (F:\logicleap2\logic-back\src\web\router_guard\api-auth.guard.ts:66:22)
    at async GuardsConsumer.tryActivate (F:\logicleap2\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:16:17)
    at async canActivateFn (F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:33)
    at async F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:31
    at async F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:17
2025-07-31 11:47:23.719 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/v1/ip-location/health","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"您的账号已在其他设备登录","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::1","timestamp":"2025-07-31T03:47:23.718Z"}
2025-07-31 11:50:29.809 [ERROR] [Business] Business IpLocationApplicationService.queryIpLocation failed {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","module":"IpLocationApplicationService","action":"queryIpLocation","data":"{\"ip\":\"127.0.0.***\",\"responseTime\":1}","error":"IP地址不支持地理位置解析"}
Error: IP地址不支持地理位置解析
    at IpLocationApplicationService.queryIpLocation (F:\logicleap2\logic-back\src\util\ip_location\application\services\ip-location-application.service.ts:86:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async IpLocationFacadeService.getLocationByIP (F:\logicleap2\logic-back\src\util\ip_location\application\services\ip-location-facade.service.ts:45:22)
    at async IpLocationController.queryIpLocationV2 (F:\logicleap2\logic-back\src\util\ip_location\controllers\ip-location.controller.ts:76:20)
2025-07-31 11:50:29.810 [ERROR] [IpLocationFacadeService] IP位置查询失败: 127.0.0.1 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
Error: IP地址不支持地理位置解析
2025-07-31 11:50:29.811 [ERROR] [GlobalExceptionFilter] GET /api/v1/ip-location/query?ip=127.0.0.1&includeRisk=false - IP地址不支持地理位置解析 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
Error: IP地址不支持地理位置解析
    at IpLocationController.queryIpLocationV2 (F:\logicleap2\logic-back\src\util\ip_location\controllers\ip-location.controller.ts:83:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
2025-07-31 11:50:29.812 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/v1/ip-location/query?ip=127.0.0.1&includeRisk=false","method":"GET","statusCode":500,"message":"IP地址不支持地理位置解析","details":{"name":"Error","stack":"Error: IP地址不支持地理位置解析\n    at IpLocationController.queryIpLocationV2 (F:\\logicleap2\\logic-back\\src\\util\\ip_location\\controllers\\ip-location.controller.ts:83:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::1","timestamp":"2025-07-31T03:50:29.811Z"}
2025-07-31 11:50:30.637 [ERROR] [Business] Business IpLocationApplicationService.queryIpLocation failed {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","module":"IpLocationApplicationService","action":"queryIpLocation","data":"{\"ip\":\"127.0.0.***\",\"responseTime\":0}","error":"IP地址不支持地理位置解析"}
Error: IP地址不支持地理位置解析
    at IpLocationApplicationService.queryIpLocation (F:\logicleap2\logic-back\src\util\ip_location\application\services\ip-location-application.service.ts:86:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async IpLocationFacadeService.getLocationByIP (F:\logicleap2\logic-back\src\util\ip_location\application\services\ip-location-facade.service.ts:45:22)
    at async IpLocationController.getCurrentIpLocation (F:\logicleap2\logic-back\src\util\ip_location\controllers\ip-location.controller.ts:227:20)
2025-07-31 11:50:30.638 [ERROR] [IpLocationFacadeService] IP位置查询失败: 127.0.0.1 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
Error: IP地址不支持地理位置解析
2025-07-31 11:50:30.639 [ERROR] [GlobalExceptionFilter] GET /api/v1/ip-location/current - IP地址不支持地理位置解析 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
Error: IP地址不支持地理位置解析
    at IpLocationController.getCurrentIpLocation (F:\logicleap2\logic-back\src\util\ip_location\controllers\ip-location.controller.ts:230:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
2025-07-31 11:50:30.639 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/v1/ip-location/current","method":"GET","statusCode":500,"message":"IP地址不支持地理位置解析","details":{"name":"Error","stack":"Error: IP地址不支持地理位置解析\n    at IpLocationController.getCurrentIpLocation (F:\\logicleap2\\logic-back\\src\\util\\ip_location\\controllers\\ip-location.controller.ts:230:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::1","timestamp":"2025-07-31T03:50:30.639Z"}
2025-07-31 11:50:31.104 [ERROR] [Business] Business IpLocationApplicationService.queryIpLocation failed {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","module":"IpLocationApplicationService","action":"queryIpLocation","data":"{\"ip\":\"127.0.0.***\",\"responseTime\":0}","error":"IP地址不支持地理位置解析"}
Error: IP地址不支持地理位置解析
    at IpLocationApplicationService.queryIpLocation (F:\logicleap2\logic-back\src\util\ip_location\application\services\ip-location-application.service.ts:86:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async IpLocationFacadeService.getLocationByIP (F:\logicleap2\logic-back\src\util\ip_location\application\services\ip-location-facade.service.ts:45:22)
    at async IpLocationController.queryIpLocationV2 (F:\logicleap2\logic-back\src\util\ip_location\controllers\ip-location.controller.ts:76:20)
2025-07-31 11:50:31.105 [ERROR] [IpLocationFacadeService] IP位置查询失败: 127.0.0.1 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
Error: IP地址不支持地理位置解析
2025-07-31 11:50:31.105 [ERROR] [GlobalExceptionFilter] GET /api/v1/ip-location/query?ip=127.0.0.1&includeRisk=false - IP地址不支持地理位置解析 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
Error: IP地址不支持地理位置解析
    at IpLocationController.queryIpLocationV2 (F:\logicleap2\logic-back\src\util\ip_location\controllers\ip-location.controller.ts:83:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
2025-07-31 11:50:31.106 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/v1/ip-location/query?ip=127.0.0.1&includeRisk=false","method":"GET","statusCode":500,"message":"IP地址不支持地理位置解析","details":{"name":"Error","stack":"Error: IP地址不支持地理位置解析\n    at IpLocationController.queryIpLocationV2 (F:\\logicleap2\\logic-back\\src\\util\\ip_location\\controllers\\ip-location.controller.ts:83:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::1","timestamp":"2025-07-31T03:50:31.105Z"}
2025-07-31 11:50:43.043 [ERROR] [Business] Business IpLocationApplicationService.queryIpLocation failed {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","module":"IpLocationApplicationService","action":"queryIpLocation","data":"{\"ip\":\"127.0.0.***\",\"responseTime\":0}","error":"IP地址不支持地理位置解析"}
Error: IP地址不支持地理位置解析
    at IpLocationApplicationService.queryIpLocation (F:\logicleap2\logic-back\src\util\ip_location\application\services\ip-location-application.service.ts:86:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async IpLocationFacadeService.getLocationByIP (F:\logicleap2\logic-back\src\util\ip_location\application\services\ip-location-facade.service.ts:45:22)
    at async IpLocationController.getCurrentIpLocation (F:\logicleap2\logic-back\src\util\ip_location\controllers\ip-location.controller.ts:227:20)
2025-07-31 11:50:43.043 [ERROR] [IpLocationFacadeService] IP位置查询失败: 127.0.0.1 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
Error: IP地址不支持地理位置解析
2025-07-31 11:50:43.044 [ERROR] [GlobalExceptionFilter] GET /api/v1/ip-location/current - IP地址不支持地理位置解析 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
Error: IP地址不支持地理位置解析
    at IpLocationController.getCurrentIpLocation (F:\logicleap2\logic-back\src\util\ip_location\controllers\ip-location.controller.ts:230:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
2025-07-31 11:50:43.044 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/v1/ip-location/current","method":"GET","statusCode":500,"message":"IP地址不支持地理位置解析","details":{"name":"Error","stack":"Error: IP地址不支持地理位置解析\n    at IpLocationController.getCurrentIpLocation (F:\\logicleap2\\logic-back\\src\\util\\ip_location\\controllers\\ip-location.controller.ts:230:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::1","timestamp":"2025-07-31T03:50:43.044Z"}
2025-07-31 11:50:43.842 [ERROR] [Business] Business IpLocationApplicationService.queryIpLocation failed {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","module":"IpLocationApplicationService","action":"queryIpLocation","data":"{\"ip\":\"127.0.0.***\",\"responseTime\":0}","error":"IP地址不支持地理位置解析"}
Error: IP地址不支持地理位置解析
    at IpLocationApplicationService.queryIpLocation (F:\logicleap2\logic-back\src\util\ip_location\application\services\ip-location-application.service.ts:86:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async IpLocationFacadeService.getLocationByIP (F:\logicleap2\logic-back\src\util\ip_location\application\services\ip-location-facade.service.ts:45:22)
    at async IpLocationController.queryIpLocationV2 (F:\logicleap2\logic-back\src\util\ip_location\controllers\ip-location.controller.ts:76:20)
2025-07-31 11:50:43.842 [ERROR] [IpLocationFacadeService] IP位置查询失败: 127.0.0.1 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
Error: IP地址不支持地理位置解析
2025-07-31 11:50:43.843 [ERROR] [GlobalExceptionFilter] GET /api/v1/ip-location/query?ip=127.0.0.1&includeRisk=false - IP地址不支持地理位置解析 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
Error: IP地址不支持地理位置解析
    at IpLocationController.queryIpLocationV2 (F:\logicleap2\logic-back\src\util\ip_location\controllers\ip-location.controller.ts:83:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
2025-07-31 11:50:43.844 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/v1/ip-location/query?ip=127.0.0.1&includeRisk=false","method":"GET","statusCode":500,"message":"IP地址不支持地理位置解析","details":{"name":"Error","stack":"Error: IP地址不支持地理位置解析\n    at IpLocationController.queryIpLocationV2 (F:\\logicleap2\\logic-back\\src\\util\\ip_location\\controllers\\ip-location.controller.ts:83:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::1","timestamp":"2025-07-31T03:50:43.844Z"}
2025-07-31 11:59:29.200 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":8184,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-31 11:59:29.266 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":8184,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:59:29.266 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":8184,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:59:29.266 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":8184,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:59:29.267 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":8184,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:59:29.267 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":8184,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:59:29.267 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":8184,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:59:29.268 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":8184,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:59:29.268 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":8184,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:59:29.268 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":8184,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:59:29.269 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":8184,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:59:29.269 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":8184,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:59:29.269 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":8184,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:59:29.269 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":8184,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:59:29.270 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":8184,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:59:29.270 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":8184,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:59:29.270 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":8184,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:59:29.271 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":8184,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:59:29.271 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":8184,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:59:29.271 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":8184,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:59:29.272 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":8184,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:00:06.664 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77760,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-31 12:00:06.750 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:00:06.751 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:00:06.751 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:00:06.752 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:00:06.752 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:00:06.753 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:00:06.754 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:00:06.754 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:00:06.755 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:00:06.755 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:00:06.756 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:00:06.756 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:00:06.757 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:00:06.757 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:00:06.757 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:00:06.758 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:00:06.758 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:00:06.758 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:00:06.759 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:00:06.759 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:04:23.238 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":22760,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-31 12:04:23.338 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":22760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:04:23.338 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":22760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:04:23.339 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":22760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:04:23.339 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":22760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:04:23.339 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":22760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:04:23.340 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":22760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:04:23.340 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":22760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:04:23.341 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":22760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:04:23.341 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":22760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:04:23.342 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":22760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:04:23.342 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":22760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:04:23.342 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":22760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:04:23.343 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":22760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:04:23.343 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":22760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:04:23.344 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":22760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:04:23.344 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":22760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:04:23.345 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":22760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:04:23.345 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":22760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:04:23.346 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":22760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:04:23.346 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":22760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
