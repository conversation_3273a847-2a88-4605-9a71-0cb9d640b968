"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/test-ip/page",{

/***/ "(app-pages-browser)/./app/test-ip/page.tsx":
/*!******************************!*\
  !*** ./app/test-ip/page.tsx ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ TestIPPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Divider,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/typography/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Divider,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/space/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Divider,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/card/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Divider,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Divider,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/divider/index.js\");\n/* harmony import */ var _lib_request__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/request */ \"(app-pages-browser)/./lib/request.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst { Title, Text, Paragraph } = _barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_3__[\"default\"];\nfunction TestIPPage() {\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [result, setResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [realPublicIP, setRealPublicIP] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 获取真实的公网IP地址\n    const getRealPublicIP = async ()=>{\n        setLoading(true);\n        try {\n            console.log(\"\\uD83C\\uDF0D [Frontend] 开始获取真实公网IP...\");\n            // 使用多个IP查询服务，提高成功率\n            const ipServices = [\n                \"https://api.ipify.org?format=json\",\n                \"https://ipapi.co/json/\",\n                \"https://httpbin.org/ip\",\n                \"https://api.ip.sb/ip\",\n                \"https://ifconfig.me/ip\",\n                \"https://icanhazip.com\"\n            ];\n            for (const service of ipServices){\n                try {\n                    console.log(\"\\uD83D\\uDD0D 尝试获取公网IP: \".concat(service));\n                    const controller = new AbortController();\n                    const timeoutId = setTimeout(()=>controller.abort(), 5000);\n                    const response = await fetch(service, {\n                        method: \"GET\",\n                        signal: controller.signal\n                    });\n                    clearTimeout(timeoutId);\n                    if (!response.ok) continue;\n                    let data;\n                    const contentType = response.headers.get(\"content-type\");\n                    if (contentType && contentType.includes(\"application/json\")) {\n                        data = await response.json();\n                        const ip = data.ip || data.origin || data.query;\n                        if (ip && isValidIPAddress(ip)) {\n                            console.log(\"✅ 成功获取公网IP: \".concat(ip, \" (来源: \").concat(service, \")\"));\n                            setRealPublicIP(ip);\n                            setResult({\n                                type: \"real-public-ip\",\n                                ip: ip,\n                                source: service,\n                                timestamp: new Date().toISOString()\n                            });\n                            return;\n                        }\n                    } else {\n                        const text = await response.text();\n                        const ip = text.trim();\n                        if (isValidIPAddress(ip)) {\n                            console.log(\"✅ 成功获取公网IP: \".concat(ip, \" (来源: \").concat(service, \")\"));\n                            setRealPublicIP(ip);\n                            setResult({\n                                type: \"real-public-ip\",\n                                ip: ip,\n                                source: service,\n                                timestamp: new Date().toISOString()\n                            });\n                            return;\n                        }\n                    }\n                } catch (error) {\n                    console.log(\"❌ 获取公网IP失败: \".concat(service, \" - \").concat(error));\n                    continue;\n                }\n            }\n            throw new Error(\"所有公网IP服务都无法访问\");\n        } catch (error) {\n            console.error(\"❌ [Frontend] 获取真实公网IP失败:\", error);\n            setResult({\n                type: \"error\",\n                error: error.message,\n                timestamp: new Date().toISOString()\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 验证IP地址格式\n    const isValidIPAddress = (ip)=>{\n        const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;\n        return ipv4Regex.test(ip);\n    };\n    // 测试获取当前IP位置\n    const testCurrentIP = async ()=>{\n        setLoading(true);\n        try {\n            console.log(\"\\uD83E\\uDDEA [Frontend Test] 开始测试当前IP获取...\");\n            console.log(\"\\uD83C\\uDF10 [Frontend Test] 当前环境信息:\", {\n                页面URL: window.location.href,\n                域名: window.location.hostname,\n                是否内网穿透: window.location.hostname.includes(\"ngrok\") || window.location.hostname.includes(\"tunnel\"),\n                用户代理: navigator.userAgent.substring(0, 100) + \"...\",\n                时间戳: new Date().toISOString()\n            });\n            console.log(\"\\uD83D\\uDCE4 [Frontend Test] 准备发送请求到: /api/v1/ip-location/current\");\n            const startTime = Date.now();\n            const response = await _lib_request__WEBPACK_IMPORTED_MODULE_2__[\"default\"].get(\"/api/v1/ip-location/current\");\n            const endTime = Date.now();\n            console.log(\"\\uD83D\\uDCE5 [Frontend Test] 请求完成:\", {\n                耗时: \"\".concat(endTime - startTime, \"ms\"),\n                响应状态: response.status,\n                响应数据: response.data,\n                时间戳: new Date().toISOString()\n            });\n            setResult({\n                type: \"current-ip\",\n                data: response.data,\n                requestTime: endTime - startTime,\n                timestamp: new Date().toISOString()\n            });\n        } catch (error) {\n            var _error_response, _error_response1, _error_response2;\n            console.error(\"❌ [Frontend Test] 当前IP测试失败:\", {\n                错误类型: error.name,\n                错误消息: error.message,\n                响应状态: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status,\n                响应数据: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.data,\n                完整错误: error,\n                时间戳: new Date().toISOString()\n            });\n            setResult({\n                type: \"error\",\n                error: error.message,\n                response: (_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.data,\n                timestamp: new Date().toISOString()\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 测试查询指定IP\n    const testQueryIP = async ()=>{\n        setLoading(true);\n        try {\n            const testIP = \"*******\"; // Google DNS\n            console.log(\"\\uD83E\\uDDEA [Frontend Test] 开始测试IP查询...\");\n            console.log(\"\\uD83C\\uDFAF [Frontend Test] 查询参数:\", {\n                目标IP: testIP,\n                IP类型: \"Google DNS服务器\",\n                预期位置: \"美国\",\n                包含风险评估: false,\n                时间戳: new Date().toISOString()\n            });\n            const queryUrl = \"/api/v1/ip-location/query?ip=\".concat(testIP, \"&includeRisk=false\");\n            console.log(\"\\uD83D\\uDCE4 [Frontend Test] 准备发送查询请求:\", queryUrl);\n            const startTime = Date.now();\n            const response = await _lib_request__WEBPACK_IMPORTED_MODULE_2__[\"default\"].get(queryUrl);\n            const endTime = Date.now();\n            console.log(\"\\uD83D\\uDCE5 [Frontend Test] IP查询完成:\", {\n                查询IP: testIP,\n                耗时: \"\".concat(endTime - startTime, \"ms\"),\n                响应状态: response.status,\n                地理位置: response.data ? {\n                    国家: response.data.country,\n                    省份: response.data.province,\n                    城市: response.data.city,\n                    运营商: response.data.isp,\n                    置信度: response.data.confidence\n                } : \"无数据\",\n                完整响应: response.data,\n                时间戳: new Date().toISOString()\n            });\n            setResult({\n                type: \"query-ip\",\n                testIP,\n                data: response.data,\n                requestTime: endTime - startTime,\n                timestamp: new Date().toISOString()\n            });\n        } catch (error) {\n            var _error_response, _error_response1, _error_response2;\n            console.error(\"❌ [Frontend Test] IP查询失败:\", {\n                查询IP: \"*******\",\n                错误类型: error.name,\n                错误消息: error.message,\n                响应状态: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status,\n                响应数据: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.data,\n                可能原因: [\n                    \"后端服务未启动\",\n                    \"IP解析服务异常\",\n                    \"网络连接问题\"\n                ],\n                时间戳: new Date().toISOString()\n            });\n            setResult({\n                type: \"error\",\n                error: error.message,\n                response: (_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.data,\n                timestamp: new Date().toISOString()\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 测试登录接口（观察IP日志）\n    const testLoginIP = async ()=>{\n        setLoading(true);\n        try {\n            console.log(\"\\uD83E\\uDDEA [Frontend Test] 开始测试登录IP获取...\");\n            console.log(\"\\uD83D\\uDD10 [Frontend Test] 登录测试说明:\", {\n                目的: \"观察登录时的IP获取和记录过程\",\n                预期结果: \"登录失败（使用错误凭据）\",\n                观察重点: [\n                    \"IP地址获取\",\n                    \"登录日志记录\",\n                    \"错误处理\"\n                ],\n                测试凭据: {\n                    手机号: \"12345678910\",\n                    密码: \"123456 (错误密码)\"\n                },\n                时间戳: new Date().toISOString()\n            });\n            console.log(\"\\uD83D\\uDCE4 [Frontend Test] 发送登录请求...\");\n            const startTime = Date.now();\n            // 这里故意使用错误的登录信息，只是为了触发IP获取逻辑\n            const response = await _lib_request__WEBPACK_IMPORTED_MODULE_2__[\"default\"].post(\"/api/user-auth/password\", {\n                phone: \"12345678910\",\n                password: \"123456\"\n            });\n            const endTime = Date.now();\n            console.log(\"\\uD83D\\uDCE5 [Frontend Test] 登录响应 (意外成功):\", {\n                耗时: \"\".concat(endTime - startTime, \"ms\"),\n                响应状态: response.status,\n                响应数据: response.data,\n                注意: \"这不应该成功，请检查后端验证逻辑\",\n                时间戳: new Date().toISOString()\n            });\n            setResult({\n                type: \"login-test\",\n                data: response.data,\n                requestTime: endTime - startTime,\n                timestamp: new Date().toISOString()\n            });\n        } catch (error) {\n            var _error_response, _error_response1, _error_response2;\n            console.log(\"\\uD83D\\uDCDD [Frontend Test] 登录测试完成 (预期失败):\", {\n                错误类型: error.name,\n                错误消息: error.message,\n                响应状态: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status,\n                响应数据: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.data,\n                分析: {\n                    是否预期失败: true,\n                    失败原因: \"使用了错误的登录凭据\",\n                    IP获取状态: \"应该已触发IP获取和日志记录\",\n                    后续检查: \"查看后端控制台的 [LoginLog] 日志\"\n                },\n                时间戳: new Date().toISOString()\n            });\n            setResult({\n                type: \"login-test\",\n                error: error.message,\n                response: (_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.data,\n                note: \"这是预期的失败，主要用于观察IP获取日志\",\n                timestamp: new Date().toISOString()\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"24px\",\n            maxWidth: \"1200px\",\n            margin: \"0 auto\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                level: 2,\n                children: \"\\uD83E\\uDDEA IP地址获取测试页面\"\n            }, void 0, false, {\n                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                lineNumber: 287,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                children: \"这个页面用于测试前端到后端的IP地址传递和获取功能。 请打开浏览器开发者工具的控制台，以及后端服务器的日志，观察IP获取过程。\"\n            }, void 0, false, {\n                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                lineNumber: 289,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                direction: \"vertical\",\n                size: \"large\",\n                style: {\n                    width: \"100%\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83C\\uDFAF 测试功能\",\n                        size: \"small\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                wrap: true,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        type: \"primary\",\n                                        loading: loading,\n                                        onClick: getRealPublicIP,\n                                        style: {\n                                            background: \"#52c41a\",\n                                            borderColor: \"#52c41a\"\n                                        },\n                                        children: \"\\uD83C\\uDF0D 获取真实公网IP\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        loading: loading,\n                                        onClick: testCurrentIP,\n                                        children: \"测试获取当前IP位置\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        loading: loading,\n                                        onClick: testQueryIP,\n                                        children: \"测试查询指定IP (*******)\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        loading: loading,\n                                        onClick: testLoginIP,\n                                        danger: true,\n                                        children: \"测试登录IP获取 (会失败)\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 11\n                            }, this),\n                            realPublicIP && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginTop: \"12px\",\n                                    padding: \"8px\",\n                                    background: \"#f6ffed\",\n                                    border: \"1px solid #b7eb8f\",\n                                    borderRadius: \"6px\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    strong: true,\n                                    style: {\n                                        color: \"#52c41a\"\n                                    },\n                                    children: [\n                                        \"\\uD83C\\uDF0D 你的真实公网IP: \",\n                                        realPublicIP\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                lineNumber: 332,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 297,\n                        columnNumber: 9\n                    }, this),\n                    result && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83D\\uDCCA 测试结果\",\n                        size: \"small\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                background: \"#f5f5f5\",\n                                padding: \"16px\",\n                                borderRadius: \"6px\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                style: {\n                                    margin: 0,\n                                    fontSize: \"12px\",\n                                    lineHeight: \"1.4\"\n                                },\n                                children: JSON.stringify(result, null, 2)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                lineNumber: 342,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                            lineNumber: 341,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 340,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 349,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83D\\uDCCB 观察要点\",\n                        size: \"small\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            direction: \"vertical\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            children: \"\\uD83C\\uDF10 前端中间件日志：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 355,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"查看浏览器控制台，观察 [Middleware] 标记的日志\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 356,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 354,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            children: \"\\uD83D\\uDDA5️ 后端IP提取日志：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 360,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"查看后端控制台，观察 [Backend] 标记的日志\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 361,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 359,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            children: \"\\uD83D\\uDD10 登录日志：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 365,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"查看后端控制台，观察 [LoginLog] 标记的日志\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 364,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            children: \"\\uD83D\\uDD0D 重点观察：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 370,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"IP地址是否从前端正确传递到后端，以及各个环节的IP获取情况\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 369,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                            lineNumber: 353,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 352,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83E\\uDD14 为什么本地开发获取到127.0.0.1？\",\n                        size: \"small\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            direction: \"vertical\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            style: {\n                                                color: \"#fa8c16\"\n                                            },\n                                            children: \"\\uD83C\\uDFE0 本地开发环境：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 380,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"浏览器 → localhost:3000 → 后端API，所有请求都来自本机，所以IP是127.0.0.1\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 381,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 379,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            style: {\n                                                color: \"#52c41a\"\n                                            },\n                                            children: \"\\uD83C\\uDF0D 生产环境：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 385,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"用户浏览器 → CDN/负载均衡 → Web服务器 → 后端API，能获取到真实公网IP\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 386,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 384,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            style: {\n                                                color: \"#1890ff\"\n                                            },\n                                            children: \"\\uD83C\\uDFAD 模拟解决方案：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 390,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: \"中间件已配置在开发环境使用模拟公网IP (**************) 进行测试\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 391,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 389,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            style: {\n                                                color: \"#722ed1\"\n                                            },\n                                            children: \"\\uD83E\\uDDEA 真实IP对比：\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 395,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            children: '点击\"获取真实公网IP\"按钮，对比你的真实公网IP和后端获取的IP'\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 396,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 394,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                            lineNumber: 378,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 377,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83C\\uDF0D 当前环境信息\",\n                        size: \"small\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            direction: \"vertical\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    children: [\n                                        \"浏览器 User-Agent: \",\n                                         true ? navigator.userAgent : 0\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 404,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    children: [\n                                        \"当前时间: \",\n                                        new Date().toISOString()\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 405,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    children: [\n                                        \"页面URL: \",\n                                         true ? window.location.href : 0\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 406,\n                                    columnNumber: 13\n                                }, this),\n                                 true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    style: {\n                                        color: window.location.hostname.includes(\"ngrok\") || window.location.hostname.includes(\"tunnel\") ? \"#52c41a\" : \"#fa8c16\",\n                                        fontWeight: \"bold\"\n                                    },\n                                    children: [\n                                        \"访问方式: \",\n                                        window.location.hostname.includes(\"ngrok\") || window.location.hostname.includes(\"tunnel\") ? \"\\uD83C\\uDF10 内网穿透访问 (可获取真实IP)\" : \"\\uD83C\\uDFE0 本地访问 (模拟IP)\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 408,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                            lineNumber: 403,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 402,\n                        columnNumber: 9\n                    }, this),\n                     true && !window.location.hostname.includes(\"ngrok\") && !window.location.hostname.includes(\"tunnel\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: \"\\uD83D\\uDE80 想要测试真实IP获取？\",\n                        size: \"small\",\n                        style: {\n                            borderColor: \"#52c41a\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Divider_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            direction: \"vertical\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    strong: true,\n                                    style: {\n                                        color: \"#52c41a\"\n                                    },\n                                    children: \"使用内网穿透获取真实IP：\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 426,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        background: \"#f6ffed\",\n                                        padding: \"12px\",\n                                        borderRadius: \"6px\",\n                                        border: \"1px solid #b7eb8f\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            code: true,\n                                            children: \"1. 安装ngrok: npm install -g ngrok\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 428,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 428,\n                                            columnNumber: 67\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            code: true,\n                                            children: \"2. 穿透前端: ngrok http 3000\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 429,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 429,\n                                            columnNumber: 59\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            code: true,\n                                            children: \"3. 访问ngrok提供的公网地址\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 430,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 430,\n                                            columnNumber: 52\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            code: true,\n                                            children: \"4. 重新测试IP获取功能\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                            lineNumber: 431,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 427,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    type: \"secondary\",\n                                    children: \"通过内网穿透，你可以模拟真实的生产环境，获取到真实的公网IP地址！\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                                    lineNumber: 433,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                            lineNumber: 425,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                        lineNumber: 424,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n                lineNumber: 294,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\logicleap2\\\\logicleapweb\\\\app\\\\test-ip\\\\page.tsx\",\n        lineNumber: 286,\n        columnNumber: 5\n    }, this);\n}\n_s(TestIPPage, \"X1Msb4W44QQRH4Yvzh/LKJZfd50=\");\n_c = TestIPPage;\nvar _c;\n$RefreshReg$(_c, \"TestIPPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC90ZXN0LWlwL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7OztBQUVpQztBQUMrQjtBQUM1QjtBQUVwQyxNQUFNLEVBQUVPLEtBQUssRUFBRUMsSUFBSSxFQUFFQyxTQUFTLEVBQUUsR0FBR04sd0dBQVVBO0FBRTlCLFNBQVNPOztJQUN0QixNQUFNLENBQUNDLFNBQVNDLFdBQVcsR0FBR1osK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDYSxRQUFRQyxVQUFVLEdBQUdkLCtDQUFRQSxDQUFNO0lBQzFDLE1BQU0sQ0FBQ2UsY0FBY0MsZ0JBQWdCLEdBQUdoQiwrQ0FBUUEsQ0FBUztJQUV6RCxjQUFjO0lBQ2QsTUFBTWlCLGtCQUFrQjtRQUN0QkwsV0FBVztRQUNYLElBQUk7WUFDRk0sUUFBUUMsR0FBRyxDQUFDO1lBRVosbUJBQW1CO1lBQ25CLE1BQU1DLGFBQWE7Z0JBQ2pCO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2FBQ0Q7WUFFRCxLQUFLLE1BQU1DLFdBQVdELFdBQVk7Z0JBQ2hDLElBQUk7b0JBQ0ZGLFFBQVFDLEdBQUcsQ0FBQywwQkFBd0IsT0FBUkU7b0JBRTVCLE1BQU1DLGFBQWEsSUFBSUM7b0JBQ3ZCLE1BQU1DLFlBQVlDLFdBQVcsSUFBTUgsV0FBV0ksS0FBSyxJQUFJO29CQUV2RCxNQUFNQyxXQUFXLE1BQU1DLE1BQU1QLFNBQVM7d0JBQ3BDUSxRQUFRO3dCQUNSQyxRQUFRUixXQUFXUSxNQUFNO29CQUMzQjtvQkFFQUMsYUFBYVA7b0JBRWIsSUFBSSxDQUFDRyxTQUFTSyxFQUFFLEVBQUU7b0JBRWxCLElBQUlDO29CQUNKLE1BQU1DLGNBQWNQLFNBQVNRLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDO29CQUV6QyxJQUFJRixlQUFlQSxZQUFZRyxRQUFRLENBQUMscUJBQXFCO3dCQUMzREosT0FBTyxNQUFNTixTQUFTVyxJQUFJO3dCQUMxQixNQUFNQyxLQUFLTixLQUFLTSxFQUFFLElBQUlOLEtBQUtPLE1BQU0sSUFBSVAsS0FBS1EsS0FBSzt3QkFDL0MsSUFBSUYsTUFBTUcsaUJBQWlCSCxLQUFLOzRCQUM5QnJCLFFBQVFDLEdBQUcsQ0FBQyxlQUEwQkUsT0FBWGtCLElBQUcsVUFBZ0IsT0FBUmxCLFNBQVE7NEJBQzlDTCxnQkFBZ0J1Qjs0QkFDaEJ6QixVQUFVO2dDQUNSNkIsTUFBTTtnQ0FDTkosSUFBSUE7Z0NBQ0pLLFFBQVF2QjtnQ0FDUndCLFdBQVcsSUFBSUMsT0FBT0MsV0FBVzs0QkFDbkM7NEJBQ0E7d0JBQ0Y7b0JBQ0YsT0FBTzt3QkFDTCxNQUFNQyxPQUFPLE1BQU1yQixTQUFTcUIsSUFBSTt3QkFDaEMsTUFBTVQsS0FBS1MsS0FBS0MsSUFBSTt3QkFDcEIsSUFBSVAsaUJBQWlCSCxLQUFLOzRCQUN4QnJCLFFBQVFDLEdBQUcsQ0FBQyxlQUEwQkUsT0FBWGtCLElBQUcsVUFBZ0IsT0FBUmxCLFNBQVE7NEJBQzlDTCxnQkFBZ0J1Qjs0QkFDaEJ6QixVQUFVO2dDQUNSNkIsTUFBTTtnQ0FDTkosSUFBSUE7Z0NBQ0pLLFFBQVF2QjtnQ0FDUndCLFdBQVcsSUFBSUMsT0FBT0MsV0FBVzs0QkFDbkM7NEJBQ0E7d0JBQ0Y7b0JBQ0Y7Z0JBQ0YsRUFBRSxPQUFPRyxPQUFPO29CQUNkaEMsUUFBUUMsR0FBRyxDQUFDLGVBQTRCK0IsT0FBYjdCLFNBQVEsT0FBVyxPQUFONkI7b0JBQ3hDO2dCQUNGO1lBQ0Y7WUFFQSxNQUFNLElBQUlDLE1BQU07UUFDbEIsRUFBRSxPQUFPRCxPQUFZO1lBQ25CaEMsUUFBUWdDLEtBQUssQ0FBQyw0QkFBNEJBO1lBQzFDcEMsVUFBVTtnQkFDUjZCLE1BQU07Z0JBQ05PLE9BQU9BLE1BQU1FLE9BQU87Z0JBQ3BCUCxXQUFXLElBQUlDLE9BQU9DLFdBQVc7WUFDbkM7UUFDRixTQUFVO1lBQ1JuQyxXQUFXO1FBQ2I7SUFDRjtJQUVBLFdBQVc7SUFDWCxNQUFNOEIsbUJBQW1CLENBQUNIO1FBQ3hCLE1BQU1jLFlBQVk7UUFDbEIsT0FBT0EsVUFBVUMsSUFBSSxDQUFDZjtJQUN4QjtJQUVBLGFBQWE7SUFDYixNQUFNZ0IsZ0JBQWdCO1FBQ3BCM0MsV0FBVztRQUNYLElBQUk7WUFDRk0sUUFBUUMsR0FBRyxDQUFDO1lBQ1pELFFBQVFDLEdBQUcsQ0FBQyx3Q0FBOEI7Z0JBQ3hDcUMsT0FBT0MsT0FBT0MsUUFBUSxDQUFDQyxJQUFJO2dCQUMzQkMsSUFBSUgsT0FBT0MsUUFBUSxDQUFDRyxRQUFRO2dCQUM1QkMsUUFBUUwsT0FBT0MsUUFBUSxDQUFDRyxRQUFRLENBQUN4QixRQUFRLENBQUMsWUFBWW9CLE9BQU9DLFFBQVEsQ0FBQ0csUUFBUSxDQUFDeEIsUUFBUSxDQUFDO2dCQUN4RjBCLE1BQU1DLFVBQVVDLFNBQVMsQ0FBQ0MsU0FBUyxDQUFDLEdBQUcsT0FBTztnQkFDOUNDLEtBQUssSUFBSXJCLE9BQU9DLFdBQVc7WUFDN0I7WUFFQTdCLFFBQVFDLEdBQUcsQ0FBQztZQUNaLE1BQU1pRCxZQUFZdEIsS0FBS3VCLEdBQUc7WUFFMUIsTUFBTTFDLFdBQVcsTUFBTXJCLG9EQUFPQSxDQUFDOEIsR0FBRyxDQUFDO1lBRW5DLE1BQU1rQyxVQUFVeEIsS0FBS3VCLEdBQUc7WUFDeEJuRCxRQUFRQyxHQUFHLENBQUMsc0NBQTRCO2dCQUN0Q29ELElBQUksR0FBdUIsT0FBcEJELFVBQVVGLFdBQVU7Z0JBQzNCSSxNQUFNN0MsU0FBUzhDLE1BQU07Z0JBQ3JCQyxNQUFNL0MsU0FBU00sSUFBSTtnQkFDbkJrQyxLQUFLLElBQUlyQixPQUFPQyxXQUFXO1lBQzdCO1lBRUFqQyxVQUFVO2dCQUNSNkIsTUFBTTtnQkFDTlYsTUFBTU4sU0FBU00sSUFBSTtnQkFDbkIwQyxhQUFhTCxVQUFVRjtnQkFDdkJ2QixXQUFXLElBQUlDLE9BQU9DLFdBQVc7WUFDbkM7UUFDRixFQUFFLE9BQU9HLE9BQVk7Z0JBSVhBLGlCQUNBQSxrQkFPSUE7WUFYWmhDLFFBQVFnQyxLQUFLLENBQUMsK0JBQStCO2dCQUMzQzBCLE1BQU0xQixNQUFNMkIsSUFBSTtnQkFDaEJDLE1BQU01QixNQUFNRSxPQUFPO2dCQUNuQm9CLElBQUksR0FBRXRCLGtCQUFBQSxNQUFNdkIsUUFBUSxjQUFkdUIsc0NBQUFBLGdCQUFnQnVCLE1BQU07Z0JBQzVCQyxJQUFJLEdBQUV4QixtQkFBQUEsTUFBTXZCLFFBQVEsY0FBZHVCLHVDQUFBQSxpQkFBZ0JqQixJQUFJO2dCQUMxQjhDLE1BQU03QjtnQkFDTmlCLEtBQUssSUFBSXJCLE9BQU9DLFdBQVc7WUFDN0I7WUFDQWpDLFVBQVU7Z0JBQ1I2QixNQUFNO2dCQUNOTyxPQUFPQSxNQUFNRSxPQUFPO2dCQUNwQnpCLFFBQVEsR0FBRXVCLG1CQUFBQSxNQUFNdkIsUUFBUSxjQUFkdUIsdUNBQUFBLGlCQUFnQmpCLElBQUk7Z0JBQzlCWSxXQUFXLElBQUlDLE9BQU9DLFdBQVc7WUFDbkM7UUFDRixTQUFVO1lBQ1JuQyxXQUFXO1FBQ2I7SUFDRjtJQUVBLFdBQVc7SUFDWCxNQUFNb0UsY0FBYztRQUNsQnBFLFdBQVc7UUFDWCxJQUFJO1lBQ0YsTUFBTXFFLFNBQVMsV0FBVyxhQUFhO1lBQ3ZDL0QsUUFBUUMsR0FBRyxDQUFDO1lBQ1pELFFBQVFDLEdBQUcsQ0FBQyxzQ0FBNEI7Z0JBQ3RDK0QsTUFBTUQ7Z0JBQ05FLE1BQU07Z0JBQ05DLE1BQU07Z0JBQ05DLFFBQVE7Z0JBQ1JsQixLQUFLLElBQUlyQixPQUFPQyxXQUFXO1lBQzdCO1lBRUEsTUFBTXVDLFdBQVcsZ0NBQXVDLE9BQVBMLFFBQU87WUFDeEQvRCxRQUFRQyxHQUFHLENBQUMsMENBQWdDbUU7WUFFNUMsTUFBTWxCLFlBQVl0QixLQUFLdUIsR0FBRztZQUMxQixNQUFNMUMsV0FBVyxNQUFNckIsb0RBQU9BLENBQUM4QixHQUFHLENBQUNrRDtZQUNuQyxNQUFNaEIsVUFBVXhCLEtBQUt1QixHQUFHO1lBRXhCbkQsUUFBUUMsR0FBRyxDQUFDLHdDQUE4QjtnQkFDeENvRSxNQUFNTjtnQkFDTlYsSUFBSSxHQUF1QixPQUFwQkQsVUFBVUYsV0FBVTtnQkFDM0JJLE1BQU03QyxTQUFTOEMsTUFBTTtnQkFDckJlLE1BQU03RCxTQUFTTSxJQUFJLEdBQUc7b0JBQ3BCd0QsSUFBSTlELFNBQVNNLElBQUksQ0FBQ3lELE9BQU87b0JBQ3pCQyxJQUFJaEUsU0FBU00sSUFBSSxDQUFDMkQsUUFBUTtvQkFDMUJDLElBQUlsRSxTQUFTTSxJQUFJLENBQUM2RCxJQUFJO29CQUN0QkMsS0FBS3BFLFNBQVNNLElBQUksQ0FBQytELEdBQUc7b0JBQ3RCQyxLQUFLdEUsU0FBU00sSUFBSSxDQUFDaUUsVUFBVTtnQkFDL0IsSUFBSTtnQkFDSkMsTUFBTXhFLFNBQVNNLElBQUk7Z0JBQ25Ca0MsS0FBSyxJQUFJckIsT0FBT0MsV0FBVztZQUM3QjtZQUVBakMsVUFBVTtnQkFDUjZCLE1BQU07Z0JBQ05zQztnQkFDQWhELE1BQU1OLFNBQVNNLElBQUk7Z0JBQ25CMEMsYUFBYUwsVUFBVUY7Z0JBQ3ZCdkIsV0FBVyxJQUFJQyxPQUFPQyxXQUFXO1lBQ25DO1FBQ0YsRUFBRSxPQUFPRyxPQUFZO2dCQUtYQSxpQkFDQUEsa0JBT0lBO1lBWlpoQyxRQUFRZ0MsS0FBSyxDQUFDLDZCQUE2QjtnQkFDekNxQyxNQUFNO2dCQUNOWCxNQUFNMUIsTUFBTTJCLElBQUk7Z0JBQ2hCQyxNQUFNNUIsTUFBTUUsT0FBTztnQkFDbkJvQixJQUFJLEdBQUV0QixrQkFBQUEsTUFBTXZCLFFBQVEsY0FBZHVCLHNDQUFBQSxnQkFBZ0J1QixNQUFNO2dCQUM1QkMsSUFBSSxHQUFFeEIsbUJBQUFBLE1BQU12QixRQUFRLGNBQWR1Qix1Q0FBQUEsaUJBQWdCakIsSUFBSTtnQkFDMUJtRSxNQUFNO29CQUFDO29CQUFXO29CQUFZO2lCQUFTO2dCQUN2Q2pDLEtBQUssSUFBSXJCLE9BQU9DLFdBQVc7WUFDN0I7WUFDQWpDLFVBQVU7Z0JBQ1I2QixNQUFNO2dCQUNOTyxPQUFPQSxNQUFNRSxPQUFPO2dCQUNwQnpCLFFBQVEsR0FBRXVCLG1CQUFBQSxNQUFNdkIsUUFBUSxjQUFkdUIsdUNBQUFBLGlCQUFnQmpCLElBQUk7Z0JBQzlCWSxXQUFXLElBQUlDLE9BQU9DLFdBQVc7WUFDbkM7UUFDRixTQUFVO1lBQ1JuQyxXQUFXO1FBQ2I7SUFDRjtJQUVBLGlCQUFpQjtJQUNqQixNQUFNeUYsY0FBYztRQUNsQnpGLFdBQVc7UUFDWCxJQUFJO1lBQ0ZNLFFBQVFDLEdBQUcsQ0FBQztZQUNaRCxRQUFRQyxHQUFHLENBQUMsd0NBQThCO2dCQUN4Q21GLElBQUk7Z0JBQ0pDLE1BQU07Z0JBQ05DLE1BQU07b0JBQUM7b0JBQVU7b0JBQVU7aUJBQU87Z0JBQ2xDQyxNQUFNO29CQUNKQyxLQUFLO29CQUNMQyxJQUFJO2dCQUNOO2dCQUNBeEMsS0FBSyxJQUFJckIsT0FBT0MsV0FBVztZQUM3QjtZQUVBN0IsUUFBUUMsR0FBRyxDQUFDO1lBQ1osTUFBTWlELFlBQVl0QixLQUFLdUIsR0FBRztZQUUxQiw2QkFBNkI7WUFDN0IsTUFBTTFDLFdBQVcsTUFBTXJCLG9EQUFPQSxDQUFDc0csSUFBSSxDQUFDLDJCQUEyQjtnQkFDN0RDLE9BQU87Z0JBQ1BDLFVBQVU7WUFDWjtZQUVBLE1BQU14QyxVQUFVeEIsS0FBS3VCLEdBQUc7WUFDeEJuRCxRQUFRQyxHQUFHLENBQUMsNkNBQW1DO2dCQUM3Q29ELElBQUksR0FBdUIsT0FBcEJELFVBQVVGLFdBQVU7Z0JBQzNCSSxNQUFNN0MsU0FBUzhDLE1BQU07Z0JBQ3JCQyxNQUFNL0MsU0FBU00sSUFBSTtnQkFDbkI4RSxJQUFJO2dCQUNKNUMsS0FBSyxJQUFJckIsT0FBT0MsV0FBVztZQUM3QjtZQUVBakMsVUFBVTtnQkFDUjZCLE1BQU07Z0JBQ05WLE1BQU1OLFNBQVNNLElBQUk7Z0JBQ25CMEMsYUFBYUwsVUFBVUY7Z0JBQ3ZCdkIsV0FBVyxJQUFJQyxPQUFPQyxXQUFXO1lBQ25DO1FBQ0YsRUFBRSxPQUFPRyxPQUFZO2dCQUlYQSxpQkFDQUEsa0JBWUlBO1lBaEJaaEMsUUFBUUMsR0FBRyxDQUFDLCtDQUFxQztnQkFDL0N5RCxNQUFNMUIsTUFBTTJCLElBQUk7Z0JBQ2hCQyxNQUFNNUIsTUFBTUUsT0FBTztnQkFDbkJvQixJQUFJLEdBQUV0QixrQkFBQUEsTUFBTXZCLFFBQVEsY0FBZHVCLHNDQUFBQSxnQkFBZ0J1QixNQUFNO2dCQUM1QkMsSUFBSSxHQUFFeEIsbUJBQUFBLE1BQU12QixRQUFRLGNBQWR1Qix1Q0FBQUEsaUJBQWdCakIsSUFBSTtnQkFDMUIrRSxJQUFJO29CQUNGQyxRQUFRO29CQUNSQyxNQUFNO29CQUNOQyxRQUFRO29CQUNSQyxNQUFNO2dCQUNSO2dCQUNBakQsS0FBSyxJQUFJckIsT0FBT0MsV0FBVztZQUM3QjtZQUNBakMsVUFBVTtnQkFDUjZCLE1BQU07Z0JBQ05PLE9BQU9BLE1BQU1FLE9BQU87Z0JBQ3BCekIsUUFBUSxHQUFFdUIsbUJBQUFBLE1BQU12QixRQUFRLGNBQWR1Qix1Q0FBQUEsaUJBQWdCakIsSUFBSTtnQkFDOUJvRixNQUFNO2dCQUNOeEUsV0FBVyxJQUFJQyxPQUFPQyxXQUFXO1lBQ25DO1FBQ0YsU0FBVTtZQUNSbkMsV0FBVztRQUNiO0lBQ0Y7SUFFQSxxQkFDRSw4REFBQzBHO1FBQUlDLE9BQU87WUFBRUMsU0FBUztZQUFRQyxVQUFVO1lBQVVDLFFBQVE7UUFBUzs7MEJBQ2xFLDhEQUFDbkg7Z0JBQU1vSCxPQUFPOzBCQUFHOzs7Ozs7MEJBRWpCLDhEQUFDbEg7MEJBQVU7Ozs7OzswQkFLWCw4REFBQ0wsd0dBQUtBO2dCQUFDd0gsV0FBVTtnQkFBV0MsTUFBSztnQkFBUU4sT0FBTztvQkFBRU8sT0FBTztnQkFBTzs7a0NBRzlELDhEQUFDNUgsd0dBQUlBO3dCQUFDNkgsT0FBTTt3QkFBVUYsTUFBSzs7MENBQ3pCLDhEQUFDekgsd0dBQUtBO2dDQUFDNEgsSUFBSTs7a0RBQ1QsOERBQUMvSCx3R0FBTUE7d0NBQ0wwQyxNQUFLO3dDQUNMaEMsU0FBU0E7d0NBQ1RzSCxTQUFTaEg7d0NBQ1RzRyxPQUFPOzRDQUFFVyxZQUFZOzRDQUFXQyxhQUFhO3dDQUFVO2tEQUN4RDs7Ozs7O2tEQUlELDhEQUFDbEksd0dBQU1BO3dDQUNMVSxTQUFTQTt3Q0FDVHNILFNBQVMxRTtrREFDVjs7Ozs7O2tEQUlELDhEQUFDdEQsd0dBQU1BO3dDQUNMVSxTQUFTQTt3Q0FDVHNILFNBQVNqRDtrREFDVjs7Ozs7O2tEQUlELDhEQUFDL0Usd0dBQU1BO3dDQUNMVSxTQUFTQTt3Q0FDVHNILFNBQVM1Qjt3Q0FDVCtCLE1BQU07a0RBQ1A7Ozs7Ozs7Ozs7Ozs0QkFLRnJILDhCQUNDLDhEQUFDdUc7Z0NBQUlDLE9BQU87b0NBQUVjLFdBQVc7b0NBQVFiLFNBQVM7b0NBQU9VLFlBQVk7b0NBQVdJLFFBQVE7b0NBQXFCQyxjQUFjO2dDQUFNOzBDQUN2SCw0RUFBQy9IO29DQUFLZ0ksTUFBTTtvQ0FBQ2pCLE9BQU87d0NBQUVrQixPQUFPO29DQUFVOzt3Q0FBRzt3Q0FBYzFIOzs7Ozs7Ozs7Ozs7Ozs7Ozs7b0JBTTdERix3QkFDQyw4REFBQ1gsd0dBQUlBO3dCQUFDNkgsT0FBTTt3QkFBVUYsTUFBSztrQ0FDekIsNEVBQUNQOzRCQUFJQyxPQUFPO2dDQUFFVyxZQUFZO2dDQUFXVixTQUFTO2dDQUFRZSxjQUFjOzRCQUFNO3NDQUN4RSw0RUFBQ0c7Z0NBQUluQixPQUFPO29DQUFFRyxRQUFRO29DQUFHaUIsVUFBVTtvQ0FBUUMsWUFBWTtnQ0FBTTswQ0FDMURDLEtBQUtDLFNBQVMsQ0FBQ2pJLFFBQVEsTUFBTTs7Ozs7Ozs7Ozs7Ozs7OztrQ0FNdEMsOERBQUNSLHdHQUFPQTs7Ozs7a0NBR1IsOERBQUNILHdHQUFJQTt3QkFBQzZILE9BQU07d0JBQVVGLE1BQUs7a0NBQ3pCLDRFQUFDekgsd0dBQUtBOzRCQUFDd0gsV0FBVTs7OENBQ2YsOERBQUNOOztzREFDQyw4REFBQzlHOzRDQUFLZ0ksTUFBTTtzREFBQzs7Ozs7O3NEQUNiLDhEQUFDaEk7c0RBQUs7Ozs7Ozs7Ozs7Ozs4Q0FHUiw4REFBQzhHOztzREFDQyw4REFBQzlHOzRDQUFLZ0ksTUFBTTtzREFBQzs7Ozs7O3NEQUNiLDhEQUFDaEk7c0RBQUs7Ozs7Ozs7Ozs7Ozs4Q0FHUiw4REFBQzhHOztzREFDQyw4REFBQzlHOzRDQUFLZ0ksTUFBTTtzREFBQzs7Ozs7O3NEQUNiLDhEQUFDaEk7c0RBQUs7Ozs7Ozs7Ozs7Ozs4Q0FHUiw4REFBQzhHOztzREFDQyw4REFBQzlHOzRDQUFLZ0ksTUFBTTtzREFBQzs7Ozs7O3NEQUNiLDhEQUFDaEk7c0RBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQU1aLDhEQUFDTix3R0FBSUE7d0JBQUM2SCxPQUFNO3dCQUEwQkYsTUFBSztrQ0FDekMsNEVBQUN6SCx3R0FBS0E7NEJBQUN3SCxXQUFVOzs4Q0FDZiw4REFBQ047O3NEQUNDLDhEQUFDOUc7NENBQUtnSSxNQUFNOzRDQUFDakIsT0FBTztnREFBRWtCLE9BQU87NENBQVU7c0RBQUc7Ozs7OztzREFDMUMsOERBQUNqSTtzREFBSzs7Ozs7Ozs7Ozs7OzhDQUdSLDhEQUFDOEc7O3NEQUNDLDhEQUFDOUc7NENBQUtnSSxNQUFNOzRDQUFDakIsT0FBTztnREFBRWtCLE9BQU87NENBQVU7c0RBQUc7Ozs7OztzREFDMUMsOERBQUNqSTtzREFBSzs7Ozs7Ozs7Ozs7OzhDQUdSLDhEQUFDOEc7O3NEQUNDLDhEQUFDOUc7NENBQUtnSSxNQUFNOzRDQUFDakIsT0FBTztnREFBRWtCLE9BQU87NENBQVU7c0RBQUc7Ozs7OztzREFDMUMsOERBQUNqSTtzREFBSzs7Ozs7Ozs7Ozs7OzhDQUdSLDhEQUFDOEc7O3NEQUNDLDhEQUFDOUc7NENBQUtnSSxNQUFNOzRDQUFDakIsT0FBTztnREFBRWtCLE9BQU87NENBQVU7c0RBQUc7Ozs7OztzREFDMUMsOERBQUNqSTtzREFBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBTVosOERBQUNOLHdHQUFJQTt3QkFBQzZILE9BQU07d0JBQVlGLE1BQUs7a0NBQzNCLDRFQUFDekgsd0dBQUtBOzRCQUFDd0gsV0FBVTs7OENBQ2YsOERBQUNwSDs7d0NBQUs7d0NBQWlCLEtBQWtCLEdBQWN3RCxVQUFVQyxTQUFTLEdBQUc7Ozs7Ozs7OENBQzdFLDhEQUFDekQ7O3dDQUFLO3dDQUFPLElBQUlzQyxPQUFPQyxXQUFXOzs7Ozs7OzhDQUNuQyw4REFBQ3ZDOzt3Q0FBSzt3Q0FBUSxLQUFrQixHQUFjaUQsT0FBT0MsUUFBUSxDQUFDQyxJQUFJLEdBQUc7Ozs7Ozs7Z0NBQ3BFLEtBQWtCLGtCQUNqQiw4REFBQ25EO29DQUFLK0csT0FBTzt3Q0FDWGtCLE9BQU9oRixPQUFPQyxRQUFRLENBQUNHLFFBQVEsQ0FBQ3hCLFFBQVEsQ0FBQyxZQUFZb0IsT0FBT0MsUUFBUSxDQUFDRyxRQUFRLENBQUN4QixRQUFRLENBQUMsWUFBWSxZQUFZO3dDQUMvRzBHLFlBQVk7b0NBQ2Q7O3dDQUFHO3dDQUVDdEYsT0FBT0MsUUFBUSxDQUFDRyxRQUFRLENBQUN4QixRQUFRLENBQUMsWUFBWW9CLE9BQU9DLFFBQVEsQ0FBQ0csUUFBUSxDQUFDeEIsUUFBUSxDQUFDLFlBQzVFLGtDQUNBOzs7Ozs7Ozs7Ozs7Ozs7Ozs7b0JBUWIsS0FBa0IsSUFBZSxDQUFDb0IsT0FBT0MsUUFBUSxDQUFDRyxRQUFRLENBQUN4QixRQUFRLENBQUMsWUFBWSxDQUFDb0IsT0FBT0MsUUFBUSxDQUFDRyxRQUFRLENBQUN4QixRQUFRLENBQUMsMkJBQ2xILDhEQUFDbkMsd0dBQUlBO3dCQUFDNkgsT0FBTTt3QkFBaUJGLE1BQUs7d0JBQVFOLE9BQU87NEJBQUVZLGFBQWE7d0JBQVU7a0NBQ3hFLDRFQUFDL0gsd0dBQUtBOzRCQUFDd0gsV0FBVTs7OENBQ2YsOERBQUNwSDtvQ0FBS2dJLE1BQU07b0NBQUNqQixPQUFPO3dDQUFFa0IsT0FBTztvQ0FBVTs4Q0FBRzs7Ozs7OzhDQUMxQyw4REFBQ25CO29DQUFJQyxPQUFPO3dDQUFFVyxZQUFZO3dDQUFXVixTQUFTO3dDQUFRZSxjQUFjO3dDQUFPRCxRQUFRO29DQUFvQjs7c0RBQ3JHLDhEQUFDOUg7NENBQUt3SSxJQUFJO3NEQUFDOzs7Ozs7c0RBQXVDLDhEQUFDQzs7Ozs7c0RBQ25ELDhEQUFDekk7NENBQUt3SSxJQUFJO3NEQUFDOzs7Ozs7c0RBQStCLDhEQUFDQzs7Ozs7c0RBQzNDLDhEQUFDekk7NENBQUt3SSxJQUFJO3NEQUFDOzs7Ozs7c0RBQXdCLDhEQUFDQzs7Ozs7c0RBQ3BDLDhEQUFDekk7NENBQUt3SSxJQUFJO3NEQUFDOzs7Ozs7Ozs7Ozs7OENBRWIsOERBQUN4STtvQ0FBS21DLE1BQUs7OENBQVk7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBUXJDO0dBaGJ3QmpDO0tBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL2FwcC90ZXN0LWlwL3BhZ2UudHN4PzQyMjgiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgQnV0dG9uLCBDYXJkLCBUeXBvZ3JhcGh5LCBTcGFjZSwgRGl2aWRlciB9IGZyb20gJ2FudGQnO1xuaW1wb3J0IHJlcXVlc3QgZnJvbSAnQC9saWIvcmVxdWVzdCc7XG5cbmNvbnN0IHsgVGl0bGUsIFRleHQsIFBhcmFncmFwaCB9ID0gVHlwb2dyYXBoeTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gVGVzdElQUGFnZSgpIHtcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbcmVzdWx0LCBzZXRSZXN1bHRdID0gdXNlU3RhdGU8YW55PihudWxsKTtcbiAgY29uc3QgW3JlYWxQdWJsaWNJUCwgc2V0UmVhbFB1YmxpY0lQXSA9IHVzZVN0YXRlPHN0cmluZz4oJycpO1xuXG4gIC8vIOiOt+WPluecn+WunueahOWFrOe9kUlQ5Zyw5Z2AXG4gIGNvbnN0IGdldFJlYWxQdWJsaWNJUCA9IGFzeW5jICgpID0+IHtcbiAgICBzZXRMb2FkaW5nKHRydWUpO1xuICAgIHRyeSB7XG4gICAgICBjb25zb2xlLmxvZygn8J+MjSBbRnJvbnRlbmRdIOW8gOWni+iOt+WPluecn+WunuWFrOe9kUlQLi4uJyk7XG5cbiAgICAgIC8vIOS9v+eUqOWkmuS4qklQ5p+l6K+i5pyN5Yqh77yM5o+Q6auY5oiQ5Yqf546HXG4gICAgICBjb25zdCBpcFNlcnZpY2VzID0gW1xuICAgICAgICAnaHR0cHM6Ly9hcGkuaXBpZnkub3JnP2Zvcm1hdD1qc29uJyxcbiAgICAgICAgJ2h0dHBzOi8vaXBhcGkuY28vanNvbi8nLFxuICAgICAgICAnaHR0cHM6Ly9odHRwYmluLm9yZy9pcCcsXG4gICAgICAgICdodHRwczovL2FwaS5pcC5zYi9pcCcsXG4gICAgICAgICdodHRwczovL2lmY29uZmlnLm1lL2lwJyxcbiAgICAgICAgJ2h0dHBzOi8vaWNhbmhhemlwLmNvbSdcbiAgICAgIF07XG5cbiAgICAgIGZvciAoY29uc3Qgc2VydmljZSBvZiBpcFNlcnZpY2VzKSB7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgY29uc29sZS5sb2coYPCflI0g5bCd6K+V6I635Y+W5YWs572RSVA6ICR7c2VydmljZX1gKTtcblxuICAgICAgICAgIGNvbnN0IGNvbnRyb2xsZXIgPSBuZXcgQWJvcnRDb250cm9sbGVyKCk7XG4gICAgICAgICAgY29uc3QgdGltZW91dElkID0gc2V0VGltZW91dCgoKSA9PiBjb250cm9sbGVyLmFib3J0KCksIDUwMDApO1xuXG4gICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChzZXJ2aWNlLCB7XG4gICAgICAgICAgICBtZXRob2Q6ICdHRVQnLFxuICAgICAgICAgICAgc2lnbmFsOiBjb250cm9sbGVyLnNpZ25hbFxuICAgICAgICAgIH0pO1xuXG4gICAgICAgICAgY2xlYXJUaW1lb3V0KHRpbWVvdXRJZCk7XG5cbiAgICAgICAgICBpZiAoIXJlc3BvbnNlLm9rKSBjb250aW51ZTtcblxuICAgICAgICAgIGxldCBkYXRhO1xuICAgICAgICAgIGNvbnN0IGNvbnRlbnRUeXBlID0gcmVzcG9uc2UuaGVhZGVycy5nZXQoJ2NvbnRlbnQtdHlwZScpO1xuXG4gICAgICAgICAgaWYgKGNvbnRlbnRUeXBlICYmIGNvbnRlbnRUeXBlLmluY2x1ZGVzKCdhcHBsaWNhdGlvbi9qc29uJykpIHtcbiAgICAgICAgICAgIGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICAgICAgICBjb25zdCBpcCA9IGRhdGEuaXAgfHwgZGF0YS5vcmlnaW4gfHwgZGF0YS5xdWVyeTtcbiAgICAgICAgICAgIGlmIChpcCAmJiBpc1ZhbGlkSVBBZGRyZXNzKGlwKSkge1xuICAgICAgICAgICAgICBjb25zb2xlLmxvZyhg4pyFIOaIkOWKn+iOt+WPluWFrOe9kUlQOiAke2lwfSAo5p2l5rqQOiAke3NlcnZpY2V9KWApO1xuICAgICAgICAgICAgICBzZXRSZWFsUHVibGljSVAoaXApO1xuICAgICAgICAgICAgICBzZXRSZXN1bHQoe1xuICAgICAgICAgICAgICAgIHR5cGU6ICdyZWFsLXB1YmxpYy1pcCcsXG4gICAgICAgICAgICAgICAgaXA6IGlwLFxuICAgICAgICAgICAgICAgIHNvdXJjZTogc2VydmljZSxcbiAgICAgICAgICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICBjb25zdCB0ZXh0ID0gYXdhaXQgcmVzcG9uc2UudGV4dCgpO1xuICAgICAgICAgICAgY29uc3QgaXAgPSB0ZXh0LnRyaW0oKTtcbiAgICAgICAgICAgIGlmIChpc1ZhbGlkSVBBZGRyZXNzKGlwKSkge1xuICAgICAgICAgICAgICBjb25zb2xlLmxvZyhg4pyFIOaIkOWKn+iOt+WPluWFrOe9kUlQOiAke2lwfSAo5p2l5rqQOiAke3NlcnZpY2V9KWApO1xuICAgICAgICAgICAgICBzZXRSZWFsUHVibGljSVAoaXApO1xuICAgICAgICAgICAgICBzZXRSZXN1bHQoe1xuICAgICAgICAgICAgICAgIHR5cGU6ICdyZWFsLXB1YmxpYy1pcCcsXG4gICAgICAgICAgICAgICAgaXA6IGlwLFxuICAgICAgICAgICAgICAgIHNvdXJjZTogc2VydmljZSxcbiAgICAgICAgICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICBjb25zb2xlLmxvZyhg4p2MIOiOt+WPluWFrOe9kUlQ5aSx6LSlOiAke3NlcnZpY2V9IC0gJHtlcnJvcn1gKTtcbiAgICAgICAgICBjb250aW51ZTtcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ+aJgOacieWFrOe9kUlQ5pyN5Yqh6YO95peg5rOV6K6/6ZeuJyk7XG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgY29uc29sZS5lcnJvcign4p2MIFtGcm9udGVuZF0g6I635Y+W55yf5a6e5YWs572RSVDlpLHotKU6JywgZXJyb3IpO1xuICAgICAgc2V0UmVzdWx0KHtcbiAgICAgICAgdHlwZTogJ2Vycm9yJyxcbiAgICAgICAgZXJyb3I6IGVycm9yLm1lc3NhZ2UsXG4gICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgICB9KTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIC8vIOmqjOivgUlQ5Zyw5Z2A5qC85byPXG4gIGNvbnN0IGlzVmFsaWRJUEFkZHJlc3MgPSAoaXA6IHN0cmluZyk6IGJvb2xlYW4gPT4ge1xuICAgIGNvbnN0IGlwdjRSZWdleCA9IC9eKD86KD86MjVbMC01XXwyWzAtNF1bMC05XXxbMDFdP1swLTldWzAtOV0/KVxcLil7M30oPzoyNVswLTVdfDJbMC00XVswLTldfFswMV0/WzAtOV1bMC05XT8pJC87XG4gICAgcmV0dXJuIGlwdjRSZWdleC50ZXN0KGlwKTtcbiAgfTtcblxuICAvLyDmtYvor5Xojrflj5blvZPliY1JUOS9jee9rlxuICBjb25zdCB0ZXN0Q3VycmVudElQID0gYXN5bmMgKCkgPT4ge1xuICAgIHNldExvYWRpbmcodHJ1ZSk7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnNvbGUubG9nKCfwn6eqIFtGcm9udGVuZCBUZXN0XSDlvIDlp4vmtYvor5XlvZPliY1JUOiOt+WPli4uLicpO1xuICAgICAgY29uc29sZS5sb2coJ/CfjJAgW0Zyb250ZW5kIFRlc3RdIOW9k+WJjeeOr+Wig+S/oeaBrzonLCB7XG4gICAgICAgIOmhtemdolVSTDogd2luZG93LmxvY2F0aW9uLmhyZWYsXG4gICAgICAgIOWfn+WQjTogd2luZG93LmxvY2F0aW9uLmhvc3RuYW1lLFxuICAgICAgICDmmK/lkKblhoXnvZHnqb/pgI86IHdpbmRvdy5sb2NhdGlvbi5ob3N0bmFtZS5pbmNsdWRlcygnbmdyb2snKSB8fCB3aW5kb3cubG9jYXRpb24uaG9zdG5hbWUuaW5jbHVkZXMoJ3R1bm5lbCcpLFxuICAgICAgICDnlKjmiLfku6PnkIY6IG5hdmlnYXRvci51c2VyQWdlbnQuc3Vic3RyaW5nKDAsIDEwMCkgKyAnLi4uJyxcbiAgICAgICAg5pe26Ze05oizOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICAgIH0pO1xuXG4gICAgICBjb25zb2xlLmxvZygn8J+TpCBbRnJvbnRlbmQgVGVzdF0g5YeG5aSH5Y+R6YCB6K+35rGC5YiwOiAvYXBpL3YxL2lwLWxvY2F0aW9uL2N1cnJlbnQnKTtcbiAgICAgIGNvbnN0IHN0YXJ0VGltZSA9IERhdGUubm93KCk7XG5cbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgcmVxdWVzdC5nZXQoJy9hcGkvdjEvaXAtbG9jYXRpb24vY3VycmVudCcpO1xuXG4gICAgICBjb25zdCBlbmRUaW1lID0gRGF0ZS5ub3coKTtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5OlIFtGcm9udGVuZCBUZXN0XSDor7fmsYLlrozmiJA6Jywge1xuICAgICAgICDogJfml7Y6IGAke2VuZFRpbWUgLSBzdGFydFRpbWV9bXNgLFxuICAgICAgICDlk43lupTnirbmgIE6IHJlc3BvbnNlLnN0YXR1cyxcbiAgICAgICAg5ZON5bqU5pWw5o2uOiByZXNwb25zZS5kYXRhLFxuICAgICAgICDml7bpl7TmiLM6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgfSk7XG5cbiAgICAgIHNldFJlc3VsdCh7XG4gICAgICAgIHR5cGU6ICdjdXJyZW50LWlwJyxcbiAgICAgICAgZGF0YTogcmVzcG9uc2UuZGF0YSxcbiAgICAgICAgcmVxdWVzdFRpbWU6IGVuZFRpbWUgLSBzdGFydFRpbWUsXG4gICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgICB9KTtcbiAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfinYwgW0Zyb250ZW5kIFRlc3RdIOW9k+WJjUlQ5rWL6K+V5aSx6LSlOicsIHtcbiAgICAgICAg6ZSZ6K+v57G75Z6LOiBlcnJvci5uYW1lLFxuICAgICAgICDplJnor6/mtojmga86IGVycm9yLm1lc3NhZ2UsXG4gICAgICAgIOWTjeW6lOeKtuaAgTogZXJyb3IucmVzcG9uc2U/LnN0YXR1cyxcbiAgICAgICAg5ZON5bqU5pWw5o2uOiBlcnJvci5yZXNwb25zZT8uZGF0YSxcbiAgICAgICAg5a6M5pW06ZSZ6K+vOiBlcnJvcixcbiAgICAgICAg5pe26Ze05oizOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICAgIH0pO1xuICAgICAgc2V0UmVzdWx0KHtcbiAgICAgICAgdHlwZTogJ2Vycm9yJyxcbiAgICAgICAgZXJyb3I6IGVycm9yLm1lc3NhZ2UsXG4gICAgICAgIHJlc3BvbnNlOiBlcnJvci5yZXNwb25zZT8uZGF0YSxcbiAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICAgIH0pO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgLy8g5rWL6K+V5p+l6K+i5oyH5a6aSVBcbiAgY29uc3QgdGVzdFF1ZXJ5SVAgPSBhc3luYyAoKSA9PiB7XG4gICAgc2V0TG9hZGluZyh0cnVlKTtcbiAgICB0cnkge1xuICAgICAgY29uc3QgdGVzdElQID0gJzguOC44LjgnOyAvLyBHb29nbGUgRE5TXG4gICAgICBjb25zb2xlLmxvZygn8J+nqiBbRnJvbnRlbmQgVGVzdF0g5byA5aeL5rWL6K+VSVDmn6Xor6IuLi4nKTtcbiAgICAgIGNvbnNvbGUubG9nKCfwn46vIFtGcm9udGVuZCBUZXN0XSDmn6Xor6Llj4LmlbA6Jywge1xuICAgICAgICDnm67moIdJUDogdGVzdElQLFxuICAgICAgICBJUOexu+WeizogJ0dvb2dsZSBETlPmnI3liqHlmagnLFxuICAgICAgICDpooTmnJ/kvY3nva46ICfnvo7lm70nLFxuICAgICAgICDljIXlkKvpo47pmanor4TkvLA6IGZhbHNlLFxuICAgICAgICDml7bpl7TmiLM6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgfSk7XG5cbiAgICAgIGNvbnN0IHF1ZXJ5VXJsID0gYC9hcGkvdjEvaXAtbG9jYXRpb24vcXVlcnk/aXA9JHt0ZXN0SVB9JmluY2x1ZGVSaXNrPWZhbHNlYDtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5OkIFtGcm9udGVuZCBUZXN0XSDlh4blpIflj5HpgIHmn6Xor6Lor7fmsYI6JywgcXVlcnlVcmwpO1xuXG4gICAgICBjb25zdCBzdGFydFRpbWUgPSBEYXRlLm5vdygpO1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCByZXF1ZXN0LmdldChxdWVyeVVybCk7XG4gICAgICBjb25zdCBlbmRUaW1lID0gRGF0ZS5ub3coKTtcblxuICAgICAgY29uc29sZS5sb2coJ/Cfk6UgW0Zyb250ZW5kIFRlc3RdIElQ5p+l6K+i5a6M5oiQOicsIHtcbiAgICAgICAg5p+l6K+iSVA6IHRlc3RJUCxcbiAgICAgICAg6ICX5pe2OiBgJHtlbmRUaW1lIC0gc3RhcnRUaW1lfW1zYCxcbiAgICAgICAg5ZON5bqU54q25oCBOiByZXNwb25zZS5zdGF0dXMsXG4gICAgICAgIOWcsOeQhuS9jee9rjogcmVzcG9uc2UuZGF0YSA/IHtcbiAgICAgICAgICDlm73lrrY6IHJlc3BvbnNlLmRhdGEuY291bnRyeSxcbiAgICAgICAgICDnnIHku706IHJlc3BvbnNlLmRhdGEucHJvdmluY2UsXG4gICAgICAgICAg5Z+O5biCOiByZXNwb25zZS5kYXRhLmNpdHksXG4gICAgICAgICAg6L+Q6JCl5ZWGOiByZXNwb25zZS5kYXRhLmlzcCxcbiAgICAgICAgICDnva7kv6HluqY6IHJlc3BvbnNlLmRhdGEuY29uZmlkZW5jZVxuICAgICAgICB9IDogJ+aXoOaVsOaNricsXG4gICAgICAgIOWujOaVtOWTjeW6lDogcmVzcG9uc2UuZGF0YSxcbiAgICAgICAg5pe26Ze05oizOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICAgIH0pO1xuXG4gICAgICBzZXRSZXN1bHQoe1xuICAgICAgICB0eXBlOiAncXVlcnktaXAnLFxuICAgICAgICB0ZXN0SVAsXG4gICAgICAgIGRhdGE6IHJlc3BvbnNlLmRhdGEsXG4gICAgICAgIHJlcXVlc3RUaW1lOiBlbmRUaW1lIC0gc3RhcnRUaW1lLFxuICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgfSk7XG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgY29uc29sZS5lcnJvcign4p2MIFtGcm9udGVuZCBUZXN0XSBJUOafpeivouWksei0pTonLCB7XG4gICAgICAgIOafpeivoklQOiAnOC44LjguOCcsXG4gICAgICAgIOmUmeivr+exu+WeizogZXJyb3IubmFtZSxcbiAgICAgICAg6ZSZ6K+v5raI5oGvOiBlcnJvci5tZXNzYWdlLFxuICAgICAgICDlk43lupTnirbmgIE6IGVycm9yLnJlc3BvbnNlPy5zdGF0dXMsXG4gICAgICAgIOWTjeW6lOaVsOaNrjogZXJyb3IucmVzcG9uc2U/LmRhdGEsXG4gICAgICAgIOWPr+iDveWOn+WboDogWyflkI7nq6/mnI3liqHmnKrlkK/liqgnLCAnSVDop6PmnpDmnI3liqHlvILluLgnLCAn572R57uc6L+e5o6l6Zeu6aKYJ10sXG4gICAgICAgIOaXtumXtOaIszogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgICB9KTtcbiAgICAgIHNldFJlc3VsdCh7XG4gICAgICAgIHR5cGU6ICdlcnJvcicsXG4gICAgICAgIGVycm9yOiBlcnJvci5tZXNzYWdlLFxuICAgICAgICByZXNwb25zZTogZXJyb3IucmVzcG9uc2U/LmRhdGEsXG4gICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgICB9KTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIC8vIOa1i+ivleeZu+W9leaOpeWPo++8iOinguWvn0lQ5pel5b+X77yJXG4gIGNvbnN0IHRlc3RMb2dpbklQID0gYXN5bmMgKCkgPT4ge1xuICAgIHNldExvYWRpbmcodHJ1ZSk7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnNvbGUubG9nKCfwn6eqIFtGcm9udGVuZCBUZXN0XSDlvIDlp4vmtYvor5XnmbvlvZVJUOiOt+WPli4uLicpO1xuICAgICAgY29uc29sZS5sb2coJ/CflJAgW0Zyb250ZW5kIFRlc3RdIOeZu+W9lea1i+ivleivtOaYjjonLCB7XG4gICAgICAgIOebrueahDogJ+inguWvn+eZu+W9leaXtueahElQ6I635Y+W5ZKM6K6w5b2V6L+H56iLJyxcbiAgICAgICAg6aKE5pyf57uT5p6cOiAn55m75b2V5aSx6LSl77yI5L2/55So6ZSZ6K+v5Yet5o2u77yJJyxcbiAgICAgICAg6KeC5a+f6YeN54K5OiBbJ0lQ5Zyw5Z2A6I635Y+WJywgJ+eZu+W9leaXpeW/l+iusOW9lScsICfplJnor6/lpITnkIYnXSxcbiAgICAgICAg5rWL6K+V5Yet5o2uOiB7XG4gICAgICAgICAg5omL5py65Y+3OiAnMTIzNDU2Nzg5MTAnLFxuICAgICAgICAgIOWvhueggTogJzEyMzQ1NiAo6ZSZ6K+v5a+G56CBKSdcbiAgICAgICAgfSxcbiAgICAgICAg5pe26Ze05oizOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICAgIH0pO1xuXG4gICAgICBjb25zb2xlLmxvZygn8J+TpCBbRnJvbnRlbmQgVGVzdF0g5Y+R6YCB55m75b2V6K+35rGCLi4uJyk7XG4gICAgICBjb25zdCBzdGFydFRpbWUgPSBEYXRlLm5vdygpO1xuXG4gICAgICAvLyDov5nph4zmlYXmhI/kvb/nlKjplJnor6/nmoTnmbvlvZXkv6Hmga/vvIzlj6rmmK/kuLrkuobop6blj5FJUOiOt+WPlumAu+i+kVxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCByZXF1ZXN0LnBvc3QoJy9hcGkvdXNlci1hdXRoL3Bhc3N3b3JkJywge1xuICAgICAgICBwaG9uZTogJzEyMzQ1Njc4OTEwJyxcbiAgICAgICAgcGFzc3dvcmQ6ICcxMjM0NTYnXG4gICAgICB9KTtcblxuICAgICAgY29uc3QgZW5kVGltZSA9IERhdGUubm93KCk7XG4gICAgICBjb25zb2xlLmxvZygn8J+TpSBbRnJvbnRlbmQgVGVzdF0g55m75b2V5ZON5bqUICjmhI/lpJbmiJDlip8pOicsIHtcbiAgICAgICAg6ICX5pe2OiBgJHtlbmRUaW1lIC0gc3RhcnRUaW1lfW1zYCxcbiAgICAgICAg5ZON5bqU54q25oCBOiByZXNwb25zZS5zdGF0dXMsXG4gICAgICAgIOWTjeW6lOaVsOaNrjogcmVzcG9uc2UuZGF0YSxcbiAgICAgICAg5rOo5oSPOiAn6L+Z5LiN5bqU6K+l5oiQ5Yqf77yM6K+35qOA5p+l5ZCO56uv6aqM6K+B6YC76L6RJyxcbiAgICAgICAg5pe26Ze05oizOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICAgIH0pO1xuXG4gICAgICBzZXRSZXN1bHQoe1xuICAgICAgICB0eXBlOiAnbG9naW4tdGVzdCcsXG4gICAgICAgIGRhdGE6IHJlc3BvbnNlLmRhdGEsXG4gICAgICAgIHJlcXVlc3RUaW1lOiBlbmRUaW1lIC0gc3RhcnRUaW1lLFxuICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgfSk7XG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgY29uc29sZS5sb2coJ/Cfk50gW0Zyb250ZW5kIFRlc3RdIOeZu+W9lea1i+ivleWujOaIkCAo6aKE5pyf5aSx6LSlKTonLCB7XG4gICAgICAgIOmUmeivr+exu+WeizogZXJyb3IubmFtZSxcbiAgICAgICAg6ZSZ6K+v5raI5oGvOiBlcnJvci5tZXNzYWdlLFxuICAgICAgICDlk43lupTnirbmgIE6IGVycm9yLnJlc3BvbnNlPy5zdGF0dXMsXG4gICAgICAgIOWTjeW6lOaVsOaNrjogZXJyb3IucmVzcG9uc2U/LmRhdGEsXG4gICAgICAgIOWIhuaekDoge1xuICAgICAgICAgIOaYr+WQpumihOacn+Wksei0pTogdHJ1ZSxcbiAgICAgICAgICDlpLHotKXljp/lm6A6ICfkvb/nlKjkuobplJnor6/nmoTnmbvlvZXlh63mja4nLFxuICAgICAgICAgIElQ6I635Y+W54q25oCBOiAn5bqU6K+l5bey6Kem5Y+RSVDojrflj5blkozml6Xlv5forrDlvZUnLFxuICAgICAgICAgIOWQjue7reajgOafpTogJ+afpeeci+WQjuerr+aOp+WItuWPsOeahCBbTG9naW5Mb2ddIOaXpeW/lydcbiAgICAgICAgfSxcbiAgICAgICAg5pe26Ze05oizOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICAgIH0pO1xuICAgICAgc2V0UmVzdWx0KHtcbiAgICAgICAgdHlwZTogJ2xvZ2luLXRlc3QnLFxuICAgICAgICBlcnJvcjogZXJyb3IubWVzc2FnZSxcbiAgICAgICAgcmVzcG9uc2U6IGVycm9yLnJlc3BvbnNlPy5kYXRhLFxuICAgICAgICBub3RlOiAn6L+Z5piv6aKE5pyf55qE5aSx6LSl77yM5Li76KaB55So5LqO6KeC5a+fSVDojrflj5bml6Xlv5cnLFxuICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgfSk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgc3R5bGU9e3sgcGFkZGluZzogJzI0cHgnLCBtYXhXaWR0aDogJzEyMDBweCcsIG1hcmdpbjogJzAgYXV0bycgfX0+XG4gICAgICA8VGl0bGUgbGV2ZWw9ezJ9PvCfp6ogSVDlnLDlnYDojrflj5bmtYvor5XpobXpnaI8L1RpdGxlPlxuICAgICAgXG4gICAgICA8UGFyYWdyYXBoPlxuICAgICAgICDov5nkuKrpobXpnaLnlKjkuo7mtYvor5XliY3nq6/liLDlkI7nq6/nmoRJUOWcsOWdgOS8oOmAkuWSjOiOt+WPluWKn+iDveOAglxuICAgICAgICDor7fmiZPlvIDmtY/op4jlmajlvIDlj5HogIXlt6XlhbfnmoTmjqfliLblj7DvvIzku6Xlj4rlkI7nq6/mnI3liqHlmajnmoTml6Xlv5fvvIzop4Llr59JUOiOt+WPlui/h+eoi+OAglxuICAgICAgPC9QYXJhZ3JhcGg+XG5cbiAgICAgIDxTcGFjZSBkaXJlY3Rpb249XCJ2ZXJ0aWNhbFwiIHNpemU9XCJsYXJnZVwiIHN0eWxlPXt7IHdpZHRoOiAnMTAwJScgfX0+XG4gICAgICAgIFxuICAgICAgICB7Lyog5rWL6K+V5oyJ6ZKu5Yy65Z+fICovfVxuICAgICAgICA8Q2FyZCB0aXRsZT1cIvCfjq8g5rWL6K+V5Yqf6IO9XCIgc2l6ZT1cInNtYWxsXCI+XG4gICAgICAgICAgPFNwYWNlIHdyYXA+XG4gICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgIHR5cGU9XCJwcmltYXJ5XCJcbiAgICAgICAgICAgICAgbG9hZGluZz17bG9hZGluZ31cbiAgICAgICAgICAgICAgb25DbGljaz17Z2V0UmVhbFB1YmxpY0lQfVxuICAgICAgICAgICAgICBzdHlsZT17eyBiYWNrZ3JvdW5kOiAnIzUyYzQxYScsIGJvcmRlckNvbG9yOiAnIzUyYzQxYScgfX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAg8J+MjSDojrflj5bnnJ/lrp7lhaznvZFJUFxuICAgICAgICAgICAgPC9CdXR0b24+XG5cbiAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgbG9hZGluZz17bG9hZGluZ31cbiAgICAgICAgICAgICAgb25DbGljaz17dGVzdEN1cnJlbnRJUH1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAg5rWL6K+V6I635Y+W5b2T5YmNSVDkvY3nva5cbiAgICAgICAgICAgIDwvQnV0dG9uPlxuXG4gICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgIGxvYWRpbmc9e2xvYWRpbmd9XG4gICAgICAgICAgICAgIG9uQ2xpY2s9e3Rlc3RRdWVyeUlQfVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICDmtYvor5Xmn6Xor6LmjIflrppJUCAoOC44LjguOClcbiAgICAgICAgICAgIDwvQnV0dG9uPlxuXG4gICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgIGxvYWRpbmc9e2xvYWRpbmd9XG4gICAgICAgICAgICAgIG9uQ2xpY2s9e3Rlc3RMb2dpbklQfVxuICAgICAgICAgICAgICBkYW5nZXJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAg5rWL6K+V55m75b2VSVDojrflj5YgKOS8muWksei0pSlcbiAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgIDwvU3BhY2U+XG5cbiAgICAgICAgICB7cmVhbFB1YmxpY0lQICYmIChcbiAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgbWFyZ2luVG9wOiAnMTJweCcsIHBhZGRpbmc6ICc4cHgnLCBiYWNrZ3JvdW5kOiAnI2Y2ZmZlZCcsIGJvcmRlcjogJzFweCBzb2xpZCAjYjdlYjhmJywgYm9yZGVyUmFkaXVzOiAnNnB4JyB9fT5cbiAgICAgICAgICAgICAgPFRleHQgc3Ryb25nIHN0eWxlPXt7IGNvbG9yOiAnIzUyYzQxYScgfX0+8J+MjSDkvaDnmoTnnJ/lrp7lhaznvZFJUDoge3JlYWxQdWJsaWNJUH08L1RleHQ+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApfVxuICAgICAgICA8L0NhcmQ+XG5cbiAgICAgICAgey8qIOe7k+aenOaYvuekuuWMuuWfnyAqL31cbiAgICAgICAge3Jlc3VsdCAmJiAoXG4gICAgICAgICAgPENhcmQgdGl0bGU9XCLwn5OKIOa1i+ivlee7k+aenFwiIHNpemU9XCJzbWFsbFwiPlxuICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBiYWNrZ3JvdW5kOiAnI2Y1ZjVmNScsIHBhZGRpbmc6ICcxNnB4JywgYm9yZGVyUmFkaXVzOiAnNnB4JyB9fT5cbiAgICAgICAgICAgICAgPHByZSBzdHlsZT17eyBtYXJnaW46IDAsIGZvbnRTaXplOiAnMTJweCcsIGxpbmVIZWlnaHQ6ICcxLjQnIH19PlxuICAgICAgICAgICAgICAgIHtKU09OLnN0cmluZ2lmeShyZXN1bHQsIG51bGwsIDIpfVxuICAgICAgICAgICAgICA8L3ByZT5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgKX1cblxuICAgICAgICA8RGl2aWRlciAvPlxuXG4gICAgICAgIHsvKiDor7TmmI7kv6Hmga8gKi99XG4gICAgICAgIDxDYXJkIHRpdGxlPVwi8J+TiyDop4Llr5/opoHngrlcIiBzaXplPVwic21hbGxcIj5cbiAgICAgICAgICA8U3BhY2UgZGlyZWN0aW9uPVwidmVydGljYWxcIj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxUZXh0IHN0cm9uZz7wn4yQIOWJjeerr+S4remXtOS7tuaXpeW/l++8mjwvVGV4dD5cbiAgICAgICAgICAgICAgPFRleHQ+5p+l55yL5rWP6KeI5Zmo5o6n5Yi25Y+w77yM6KeC5a+fIFtNaWRkbGV3YXJlXSDmoIforrDnmoTml6Xlv5c8L1RleHQ+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPFRleHQgc3Ryb25nPvCflqXvuI8g5ZCO56uvSVDmj5Dlj5bml6Xlv5fvvJo8L1RleHQ+XG4gICAgICAgICAgICAgIDxUZXh0Puafpeeci+WQjuerr+aOp+WItuWPsO+8jOinguWvnyBbQmFja2VuZF0g5qCH6K6w55qE5pel5b+XPC9UZXh0PlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxUZXh0IHN0cm9uZz7wn5SQIOeZu+W9leaXpeW/l++8mjwvVGV4dD5cbiAgICAgICAgICAgICAgPFRleHQ+5p+l55yL5ZCO56uv5o6n5Yi25Y+w77yM6KeC5a+fIFtMb2dpbkxvZ10g5qCH6K6w55qE5pel5b+XPC9UZXh0PlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxUZXh0IHN0cm9uZz7wn5SNIOmHjeeCueinguWvn++8mjwvVGV4dD5cbiAgICAgICAgICAgICAgPFRleHQ+SVDlnLDlnYDmmK/lkKbku47liY3nq6/mraPnoa7kvKDpgJLliLDlkI7nq6/vvIzku6Xlj4rlkITkuKrnjq/oioLnmoRJUOiOt+WPluaDheWGtTwvVGV4dD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvU3BhY2U+XG4gICAgICAgIDwvQ2FyZD5cblxuICAgICAgICB7LyogSVDojrflj5bljp/nkIbor7TmmI4gKi99XG4gICAgICAgIDxDYXJkIHRpdGxlPVwi8J+klCDkuLrku4DkuYjmnKzlnLDlvIDlj5Hojrflj5bliLAxMjcuMC4wLjHvvJ9cIiBzaXplPVwic21hbGxcIj5cbiAgICAgICAgICA8U3BhY2UgZGlyZWN0aW9uPVwidmVydGljYWxcIj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxUZXh0IHN0cm9uZyBzdHlsZT17eyBjb2xvcjogJyNmYThjMTYnIH19PvCfj6Ag5pys5Zyw5byA5Y+R546v5aKD77yaPC9UZXh0PlxuICAgICAgICAgICAgICA8VGV4dD7mtY/op4jlmagg4oaSIGxvY2FsaG9zdDozMDAwIOKGkiDlkI7nq69BUEnvvIzmiYDmnInor7fmsYLpg73mnaXoh6rmnKzmnLrvvIzmiYDku6VJUOaYrzEyNy4wLjAuMTwvVGV4dD5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8VGV4dCBzdHJvbmcgc3R5bGU9e3sgY29sb3I6ICcjNTJjNDFhJyB9fT7wn4yNIOeUn+S6p+eOr+Wig++8mjwvVGV4dD5cbiAgICAgICAgICAgICAgPFRleHQ+55So5oi35rWP6KeI5ZmoIOKGkiBDRE4v6LSf6L295Z2H6KGhIOKGkiBXZWLmnI3liqHlmagg4oaSIOWQjuerr0FQSe+8jOiDveiOt+WPluWIsOecn+WunuWFrOe9kUlQPC9UZXh0PlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxUZXh0IHN0cm9uZyBzdHlsZT17eyBjb2xvcjogJyMxODkwZmYnIH19PvCfjq0g5qih5ouf6Kej5Yaz5pa55qGI77yaPC9UZXh0PlxuICAgICAgICAgICAgICA8VGV4dD7kuK3pl7Tku7blt7LphY3nva7lnKjlvIDlj5Hnjq/looPkvb/nlKjmqKHmi5/lhaznvZFJUCAoMjIwLjE4MS4zOC4xNDgpIOi/m+ihjOa1i+ivlTwvVGV4dD5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8VGV4dCBzdHJvbmcgc3R5bGU9e3sgY29sb3I6ICcjNzIyZWQxJyB9fT7wn6eqIOecn+WunklQ5a+55q+U77yaPC9UZXh0PlxuICAgICAgICAgICAgICA8VGV4dD7ngrnlh7tcIuiOt+WPluecn+WunuWFrOe9kUlQXCLmjInpkq7vvIzlr7nmr5TkvaDnmoTnnJ/lrp7lhaznvZFJUOWSjOWQjuerr+iOt+WPlueahElQPC9UZXh0PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9TcGFjZT5cbiAgICAgICAgPC9DYXJkPlxuXG4gICAgICAgIHsvKiDnjq/looPkv6Hmga8gKi99XG4gICAgICAgIDxDYXJkIHRpdGxlPVwi8J+MjSDlvZPliY3njq/looPkv6Hmga9cIiBzaXplPVwic21hbGxcIj5cbiAgICAgICAgICA8U3BhY2UgZGlyZWN0aW9uPVwidmVydGljYWxcIj5cbiAgICAgICAgICAgIDxUZXh0Pua1j+iniOWZqCBVc2VyLUFnZW50OiB7dHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcgPyBuYXZpZ2F0b3IudXNlckFnZW50IDogJ+acjeWKoeerr+a4suafkyd9PC9UZXh0PlxuICAgICAgICAgICAgPFRleHQ+5b2T5YmN5pe26Ze0OiB7bmV3IERhdGUoKS50b0lTT1N0cmluZygpfTwvVGV4dD5cbiAgICAgICAgICAgIDxUZXh0PumhtemdolVSTDoge3R5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnID8gd2luZG93LmxvY2F0aW9uLmhyZWYgOiAn5pyN5Yqh56uv5riy5p+TJ308L1RleHQ+XG4gICAgICAgICAgICB7dHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcgJiYgKFxuICAgICAgICAgICAgICA8VGV4dCBzdHlsZT17e1xuICAgICAgICAgICAgICAgIGNvbG9yOiB3aW5kb3cubG9jYXRpb24uaG9zdG5hbWUuaW5jbHVkZXMoJ25ncm9rJykgfHwgd2luZG93LmxvY2F0aW9uLmhvc3RuYW1lLmluY2x1ZGVzKCd0dW5uZWwnKSA/ICcjNTJjNDFhJyA6ICcjZmE4YzE2JyxcbiAgICAgICAgICAgICAgICBmb250V2VpZ2h0OiAnYm9sZCdcbiAgICAgICAgICAgICAgfX0+XG4gICAgICAgICAgICAgICAg6K6/6Zeu5pa55byPOiB7XG4gICAgICAgICAgICAgICAgICB3aW5kb3cubG9jYXRpb24uaG9zdG5hbWUuaW5jbHVkZXMoJ25ncm9rJykgfHwgd2luZG93LmxvY2F0aW9uLmhvc3RuYW1lLmluY2x1ZGVzKCd0dW5uZWwnKVxuICAgICAgICAgICAgICAgICAgICA/ICfwn4yQIOWGhee9keepv+mAj+iuv+mXriAo5Y+v6I635Y+W55yf5a6eSVApJ1xuICAgICAgICAgICAgICAgICAgICA6ICfwn4+gIOacrOWcsOiuv+mXriAo5qih5oufSVApJ1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgPC9UZXh0PlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L1NwYWNlPlxuICAgICAgICA8L0NhcmQ+XG5cbiAgICAgICAgey8qIOWGhee9keepv+mAj+aMh+WNlyAqL31cbiAgICAgICAge3R5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnICYmICF3aW5kb3cubG9jYXRpb24uaG9zdG5hbWUuaW5jbHVkZXMoJ25ncm9rJykgJiYgIXdpbmRvdy5sb2NhdGlvbi5ob3N0bmFtZS5pbmNsdWRlcygndHVubmVsJykgJiYgKFxuICAgICAgICAgIDxDYXJkIHRpdGxlPVwi8J+agCDmg7PopoHmtYvor5XnnJ/lrp5JUOiOt+WPlu+8n1wiIHNpemU9XCJzbWFsbFwiIHN0eWxlPXt7IGJvcmRlckNvbG9yOiAnIzUyYzQxYScgfX0+XG4gICAgICAgICAgICA8U3BhY2UgZGlyZWN0aW9uPVwidmVydGljYWxcIj5cbiAgICAgICAgICAgICAgPFRleHQgc3Ryb25nIHN0eWxlPXt7IGNvbG9yOiAnIzUyYzQxYScgfX0+5L2/55So5YaF572R56m/6YCP6I635Y+W55yf5a6eSVDvvJo8L1RleHQ+XG4gICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgYmFja2dyb3VuZDogJyNmNmZmZWQnLCBwYWRkaW5nOiAnMTJweCcsIGJvcmRlclJhZGl1czogJzZweCcsIGJvcmRlcjogJzFweCBzb2xpZCAjYjdlYjhmJyB9fT5cbiAgICAgICAgICAgICAgICA8VGV4dCBjb2RlPjEuIOWuieijhW5ncm9rOiBucG0gaW5zdGFsbCAtZyBuZ3JvazwvVGV4dD48YnIvPlxuICAgICAgICAgICAgICAgIDxUZXh0IGNvZGU+Mi4g56m/6YCP5YmN56uvOiBuZ3JvayBodHRwIDMwMDA8L1RleHQ+PGJyLz5cbiAgICAgICAgICAgICAgICA8VGV4dCBjb2RlPjMuIOiuv+mXrm5ncm9r5o+Q5L6b55qE5YWs572R5Zyw5Z2APC9UZXh0Pjxici8+XG4gICAgICAgICAgICAgICAgPFRleHQgY29kZT40LiDph43mlrDmtYvor5VJUOiOt+WPluWKn+iDvTwvVGV4dD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxUZXh0IHR5cGU9XCJzZWNvbmRhcnlcIj7pgJrov4flhoXnvZHnqb/pgI/vvIzkvaDlj6/ku6XmqKHmi5/nnJ/lrp7nmoTnlJ/kuqfnjq/looPvvIzojrflj5bliLDnnJ/lrp7nmoTlhaznvZFJUOWcsOWdgO+8gTwvVGV4dD5cbiAgICAgICAgICAgIDwvU3BhY2U+XG4gICAgICAgICAgPC9DYXJkPlxuICAgICAgICApfVxuXG4gICAgICA8L1NwYWNlPlxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwiQnV0dG9uIiwiQ2FyZCIsIlR5cG9ncmFwaHkiLCJTcGFjZSIsIkRpdmlkZXIiLCJyZXF1ZXN0IiwiVGl0bGUiLCJUZXh0IiwiUGFyYWdyYXBoIiwiVGVzdElQUGFnZSIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwicmVzdWx0Iiwic2V0UmVzdWx0IiwicmVhbFB1YmxpY0lQIiwic2V0UmVhbFB1YmxpY0lQIiwiZ2V0UmVhbFB1YmxpY0lQIiwiY29uc29sZSIsImxvZyIsImlwU2VydmljZXMiLCJzZXJ2aWNlIiwiY29udHJvbGxlciIsIkFib3J0Q29udHJvbGxlciIsInRpbWVvdXRJZCIsInNldFRpbWVvdXQiLCJhYm9ydCIsInJlc3BvbnNlIiwiZmV0Y2giLCJtZXRob2QiLCJzaWduYWwiLCJjbGVhclRpbWVvdXQiLCJvayIsImRhdGEiLCJjb250ZW50VHlwZSIsImhlYWRlcnMiLCJnZXQiLCJpbmNsdWRlcyIsImpzb24iLCJpcCIsIm9yaWdpbiIsInF1ZXJ5IiwiaXNWYWxpZElQQWRkcmVzcyIsInR5cGUiLCJzb3VyY2UiLCJ0aW1lc3RhbXAiLCJEYXRlIiwidG9JU09TdHJpbmciLCJ0ZXh0IiwidHJpbSIsImVycm9yIiwiRXJyb3IiLCJtZXNzYWdlIiwiaXB2NFJlZ2V4IiwidGVzdCIsInRlc3RDdXJyZW50SVAiLCLpobXpnaJVUkwiLCJ3aW5kb3ciLCJsb2NhdGlvbiIsImhyZWYiLCLln5/lkI0iLCJob3N0bmFtZSIsIuaYr+WQpuWGhee9keepv+mAjyIsIueUqOaIt+S7o+eQhiIsIm5hdmlnYXRvciIsInVzZXJBZ2VudCIsInN1YnN0cmluZyIsIuaXtumXtOaIsyIsInN0YXJ0VGltZSIsIm5vdyIsImVuZFRpbWUiLCLogJfml7YiLCLlk43lupTnirbmgIEiLCJzdGF0dXMiLCLlk43lupTmlbDmja4iLCJyZXF1ZXN0VGltZSIsIumUmeivr+exu+WeiyIsIm5hbWUiLCLplJnor6/mtojmga8iLCLlrozmlbTplJnor68iLCJ0ZXN0UXVlcnlJUCIsInRlc3RJUCIsIuebruagh0lQIiwiSVDnsbvlnosiLCLpooTmnJ/kvY3nva4iLCLljIXlkKvpo47pmanor4TkvLAiLCJxdWVyeVVybCIsIuafpeivoklQIiwi5Zyw55CG5L2N572uIiwi5Zu95a62IiwiY291bnRyeSIsIuecgeS7vSIsInByb3ZpbmNlIiwi5Z+O5biCIiwiY2l0eSIsIui/kOiQpeWVhiIsImlzcCIsIue9ruS/oeW6piIsImNvbmZpZGVuY2UiLCLlrozmlbTlk43lupQiLCLlj6/og73ljp/lm6AiLCJ0ZXN0TG9naW5JUCIsIuebrueahCIsIumihOacn+e7k+aenCIsIuinguWvn+mHjeeCuSIsIua1i+ivleWHreaNriIsIuaJi+acuuWPtyIsIuWvhueggSIsInBvc3QiLCJwaG9uZSIsInBhc3N3b3JkIiwi5rOo5oSPIiwi5YiG5p6QIiwi5piv5ZCm6aKE5pyf5aSx6LSlIiwi5aSx6LSl5Y6f5ZugIiwiSVDojrflj5bnirbmgIEiLCLlkI7nu63mo4Dmn6UiLCJub3RlIiwiZGl2Iiwic3R5bGUiLCJwYWRkaW5nIiwibWF4V2lkdGgiLCJtYXJnaW4iLCJsZXZlbCIsImRpcmVjdGlvbiIsInNpemUiLCJ3aWR0aCIsInRpdGxlIiwid3JhcCIsIm9uQ2xpY2siLCJiYWNrZ3JvdW5kIiwiYm9yZGVyQ29sb3IiLCJkYW5nZXIiLCJtYXJnaW5Ub3AiLCJib3JkZXIiLCJib3JkZXJSYWRpdXMiLCJzdHJvbmciLCJjb2xvciIsInByZSIsImZvbnRTaXplIiwibGluZUhlaWdodCIsIkpTT04iLCJzdHJpbmdpZnkiLCJmb250V2VpZ2h0IiwiY29kZSIsImJyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/test-ip/page.tsx\n"));

/***/ })

});