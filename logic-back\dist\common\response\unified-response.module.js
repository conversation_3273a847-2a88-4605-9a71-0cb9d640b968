"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UnifiedResponseModule = void 0;
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const unified_response_service_1 = require("./unified-response.service");
const unified_exception_filter_1 = require("./unified-exception.filter");
const unified_response_interceptor_1 = require("./unified-response.interceptor");
const logger_module_1 = require("../logger/logger.module");
let UnifiedResponseModule = class UnifiedResponseModule {
};
exports.UnifiedResponseModule = UnifiedResponseModule;
exports.UnifiedResponseModule = UnifiedResponseModule = __decorate([
    (0, common_1.Global)(),
    (0, common_1.Module)({
        imports: [logger_module_1.LoggerModule],
        providers: [
            unified_response_service_1.UnifiedResponseService,
            unified_response_interceptor_1.RequestTimeMiddleware,
            {
                provide: core_1.APP_FILTER,
                useClass: unified_exception_filter_1.UnifiedExceptionFilter,
            },
            {
                provide: core_1.APP_INTERCEPTOR,
                useClass: unified_response_interceptor_1.UnifiedResponseInterceptor,
            },
        ],
        exports: [
            unified_response_service_1.UnifiedResponseService,
            unified_response_interceptor_1.RequestTimeMiddleware,
        ],
    })
], UnifiedResponseModule);
//# sourceMappingURL=unified-response.module.js.map