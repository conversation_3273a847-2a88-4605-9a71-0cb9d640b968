"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./middleware.ts":
/*!***********************!*\
  !*** ./middleware.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n\nfunction middleware(request) {\n    // 只处理 API 请求\n    if (request.nextUrl.pathname.startsWith(\"/api/\")) {\n        // 获取客户端真实IP地址\n        const forwarded = request.headers.get(\"x-forwarded-for\");\n        const realIp = request.headers.get(\"x-real-ip\");\n        const cfConnectingIp = request.headers.get(\"cf-connecting-ip\");\n        const remoteAddr = request.headers.get(\"remote-addr\");\n        // 尝试多种方式获取IP\n        let clientIp = request.ip;\n        if (!clientIp && forwarded) {\n            clientIp = forwarded.split(\",\")[0].trim();\n        }\n        if (!clientIp && realIp) {\n            clientIp = realIp;\n        }\n        if (!clientIp && cfConnectingIp) {\n            clientIp = cfConnectingIp;\n        }\n        if (!clientIp && remoteAddr) {\n            clientIp = remoteAddr;\n        }\n        // 如果还是没有获取到，尝试从其他来源\n        if (!clientIp) {\n            // 检查是否通过内网穿透访问\n            const host = request.headers.get(\"host\");\n            const isNgrokTunnel = host?.includes(\"ngrok\") || host?.includes(\"tunnel\") || host?.includes(\"vicp.fun\");\n            if (true) {\n                if (isNgrokTunnel) {\n                    // 通过内网穿透访问，尝试从其他头部获取真实IP\n                    // 有些内网穿透服务会使用不同的头部名称\n                    const possibleIpHeaders = [\n                        \"x-original-forwarded-for\",\n                        \"x-forwarded-for-original\",\n                        \"x-client-ip-original\",\n                        \"x-tunnel-client-ip\",\n                        \"cf-connecting-ip\",\n                        \"true-client-ip\"\n                    ];\n                    for (const header of possibleIpHeaders){\n                        const headerValue = request.headers.get(header);\n                        if (headerValue && headerValue !== \"127.0.0.1\") {\n                            clientIp = headerValue.split(\",\")[0].trim();\n                            console.log(`🌐 [Middleware] 从 ${header} 获取到IP:`, clientIp);\n                            break;\n                        }\n                    }\n                    // 如果还是没有获取到，使用一个测试IP\n                    if (!clientIp || clientIp === \"127.0.0.1\") {\n                        clientIp = \"**************\"; // 使用测试IP\n                        console.log(\"\\uD83C\\uDFAD [Middleware] 内网穿透环境未获取到真实IP，使用测试IP:\", clientIp);\n                    }\n                } else {\n                    // 本地开发环境，使用模拟公网IP进行测试\n                    clientIp = \"**************\"; // 模拟的公网IP（百度的IP）\n                    console.log(\"\\uD83C\\uDFAD [Middleware] 本地开发环境使用模拟公网IP:\", clientIp);\n                }\n            } else {}\n        }\n        // 添加详细的IP获取日志\n        console.log(\"\\uD83C\\uDF10 [Middleware] IP地址获取详情:\", {\n            url: request.nextUrl.pathname,\n            method: request.method,\n            原始IP来源: {\n                \"request.ip\": request.ip,\n                \"x-forwarded-for\": forwarded,\n                \"x-real-ip\": realIp,\n                \"cf-connecting-ip\": cfConnectingIp,\n                \"remote-addr\": remoteAddr\n            },\n            最终确定IP: clientIp,\n            环境: \"development\",\n            时间戳: new Date().toISOString()\n        });\n        // 创建新的请求头\n        const requestHeaders = new Headers(request.headers);\n        // 设置真实IP相关的头部\n        requestHeaders.set(\"x-forwarded-for\", clientIp);\n        requestHeaders.set(\"x-real-ip\", clientIp);\n        requestHeaders.set(\"x-client-ip\", clientIp);\n        // 创建新的响应，将修改后的头部传递给后端\n        const response = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.rewrite(request.nextUrl, {\n            request: {\n                headers: requestHeaders\n            }\n        });\n        return response;\n    }\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n}\nconst config = {\n    matcher: [\n        /*\r\n     * 匹配所有 API 路由\r\n     */ \"/api/:path*\"\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./middleware.ts\n");

/***/ })

});