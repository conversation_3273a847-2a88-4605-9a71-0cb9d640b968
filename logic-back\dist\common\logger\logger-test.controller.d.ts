import { LoggerService } from './logger.service';
export declare class LoggerTestController {
    private readonly loggerService;
    private loggerTest;
    constructor(loggerService: LoggerService);
    testAllLogs(): {
        success: boolean;
        message: string;
        logFiles: string[];
        error?: undefined;
    } | {
        success: boolean;
        message: string;
        error: any;
        logFiles?: undefined;
    };
    testAsyncLogs(): Promise<{
        success: boolean;
        message: string;
        error?: undefined;
    } | {
        success: boolean;
        message: string;
        error: any;
    }>;
    testLogPerformance(): {
        success: boolean;
        message: string;
        error?: undefined;
    } | {
        success: boolean;
        message: string;
        error: any;
    };
    testCustomLogs(body: {
        message: string;
        level: string;
        context?: string;
    }): {
        success: boolean;
        message: string;
        data: {
            message: string;
            level: string;
            context: string;
        };
        error?: undefined;
    } | {
        success: boolean;
        message: string;
        error: any;
        data?: undefined;
    };
    testConsoleMethods(): {
        success: boolean;
        message: string;
        error?: undefined;
    } | {
        success: boolean;
        message: string;
        error: any;
    };
    simulateErrors(): {
        success: boolean;
        message: string;
        error?: undefined;
    } | {
        success: boolean;
        message: string;
        error: any;
    };
}
