import { Repository } from 'typeorm';
import { GetLocationByIpQuery } from '../queries/get-location-by-ip.query';
import { GetUserLocationStatsQuery } from '../queries/get-user-location-stats.query';
import { AssessLoginRiskQuery } from '../queries/assess-login-risk.query';
import { QueryResult } from '../interfaces/query-handler.interface';
import { IpLocationDomainService } from '../../domain/services/ip-location-domain.service';
import { RiskAssessmentDomainService } from '../../domain/services/risk-assessment-domain.service';
import { LocationComparisonService } from '../../domain/services/location-comparison.service';
import { UserCommonLocation } from '../../domain/entities/user-common-location.entity';
export interface LocationQueryResult {
    ip: string;
    country: string;
    province: string;
    city: string;
    isp: string;
    dataSource: string;
    confidence: number;
    isHighQuality: boolean;
    displayName: string;
    riskAssessment?: {
        level: string;
        score: number;
        reason: string;
        factors: string[];
        needVerification: boolean;
        recommendedActions: string[];
    };
}
export interface UserLocationStatsResult {
    userId: number;
    queryPeriod: {
        days: number;
        startDate: string;
        endDate: string;
    };
    summary: {
        totalLocations: number;
        trustedLocations: number;
        totalLogins: number;
        riskLogins: number;
        uniqueProvinces: number;
        uniqueCities: number;
    };
    locations: Array<{
        country: string;
        province: string;
        city: string;
        isp: string;
        loginCount: number;
        isTrusted: boolean;
        trustScore: number;
        lastLoginAt: string;
        firstLoginAt: string;
    }>;
    riskAnalysis?: {
        overallRiskLevel: string;
        riskFactors: string[];
        recommendations: string[];
    };
}
export interface RiskAssessmentResult {
    riskAssessment: {
        level: string;
        score: number;
        reason: string;
        factors: string[];
        needVerification: boolean;
        recommendedActions: string[];
    };
    location: {
        country: string;
        province: string;
        city: string;
        isp: string;
        displayName: string;
    };
    userHistory: {
        lastLoginLocation?: string;
        commonLocationCount: number;
        isNewLocation: boolean;
    };
    recommendations?: {
        verificationMethods: string[];
        urgency: string;
        reason: string;
    };
    userProfile?: {
        riskLevel: string;
        trustScore: number;
        characteristics: string[];
        recommendations: string[];
    };
}
export declare class IpLocationQueryService {
    private readonly userLocationRepository;
    private readonly ipLocationDomainService;
    private readonly riskAssessmentService;
    private readonly locationComparisonService;
    constructor(userLocationRepository: Repository<UserCommonLocation>, ipLocationDomainService: IpLocationDomainService, riskAssessmentService: RiskAssessmentDomainService, locationComparisonService: LocationComparisonService);
    handleGetLocationByIp(query: GetLocationByIpQuery): Promise<QueryResult<LocationQueryResult>>;
    handleGetUserLocationStats(query: GetUserLocationStatsQuery): Promise<QueryResult<UserLocationStatsResult>>;
    handleAssessLoginRisk(query: AssessLoginRiskQuery): Promise<QueryResult<RiskAssessmentResult>>;
    private isDevelopmentEnvironment;
    private createDefaultLocationForDevelopment;
}
